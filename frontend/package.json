{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@babylonjs/core": "^8.20.0", "@babylonjs/inspector": "^8.20.0", "@babylonjs/loaders": "^8.20.0", "@babylonjs/materials": "^8.20.0", "@element-plus/icons-vue": "^2.3.2", "axios": "^1.11.0", "dayjs": "^1.11.13", "draco3d": "^1.5.7", "echarts": "^6.0.0", "element-plus": "^2.10.5", "pinia": "^3.0.3", "three": "^0.179.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "prettier": "3.6.2", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}