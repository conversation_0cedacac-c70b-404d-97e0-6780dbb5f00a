/**
 * API接口统一管理
 */

import request from '@/utils/request'

// 物料管理API
export const materialsApi = {
  // 物料分类
  getCategories: (params) => request.get('/materials/categories/', { params }),
  getCategoryTree: () => request.get('/materials/categories/tree/'),
  createCategory: (data) => request.post('/materials/categories/', data),
  updateCategory: (id, data) => request.put(`/materials/categories/${id}/`, data),
  deleteCategory: (id) => request.delete(`/materials/categories/${id}/`),
  
  // 物料管理
  getMaterials: (params) => request.get('/materials/materials/', { params }),
  getMaterial: (id) => request.get(`/materials/materials/${id}/`),
  createMaterial: (data) => request.post('/materials/materials/', data),
  updateMaterial: (id, data) => request.put(`/materials/materials/${id}/`, data),
  deleteMaterial: (id) => request.delete(`/materials/materials/${id}/`),
  activateMaterial: (id) => request.post(`/materials/materials/${id}/activate/`),
  deactivateMaterial: (id) => request.post(`/materials/materials/${id}/deactivate/`),
  getMaterialsByCategory: (categoryCode) => request.get(`/materials/materials/by_category/?category_code=${categoryCode}`),
  addToPreferred: (materialIds) => request.post('/materials/materials/add_to_preferred/', { material_ids: materialIds }),
  removeFromPreferred: (materialIds) => request.post('/materials/materials/remove_from_preferred/', { material_ids: materialIds }),
}

// BOM管理API
export const bomApi = {
  getBOMs: (params) => request.get('/bom/boms/', { params }),
  getBOM: (id) => request.get(`/bom/boms/${id}/`),
  createBOM: (data) => request.post('/bom/boms/', data),
  updateBOM: (id, data) => request.put(`/bom/boms/${id}/`, data),
  deleteBOM: (id) => request.delete(`/bom/boms/${id}/`),
  getBOMItems: (bomId) => request.get(`/bom/boms/${bomId}/items/`),
  getCostAnalysis: (bomId) => request.get(`/bom/boms/${bomId}/cost_analysis/`),
  
  // BOM明细
  getBOMItemsList: (params) => request.get('/bom/items/', { params }),
  createBOMItem: (data) => request.post('/bom/items/', data),
  updateBOMItem: (id, data) => request.put(`/bom/items/${id}/`, data),
  deleteBOMItem: (id) => request.delete(`/bom/items/${id}/`),
  

}

// 库存管理API
export const inventoryApi = {
  // 仓库管理
  getWarehouses: (params) => request.get('/inventory/warehouses/', { params }),
  createWarehouse: (data) => request.post('/inventory/warehouses/', data),
  updateWarehouse: (id, data) => request.put(`/inventory/warehouses/${id}/`, data),
  deleteWarehouse: (id) => request.delete(`/inventory/warehouses/${id}/`),
  
  // 库存记录
  getInventoryRecords: (params) => request.get('/inventory/records/', { params }),
  getInventoryRecord: (id) => request.get(`/inventory/records/${id}/`),
  updateInventoryRecord: (id, data) => request.put(`/inventory/records/${id}/`, data),
  getLowStockRecords: () => request.get('/inventory/records/low_stock/'),
  getInventorySummary: (materialCode) => request.get(`/inventory/records/summary/?material_code=${materialCode}`),
  stockIn: (data) => request.post('/inventory/records/stock_in/', data),
  stockOut: (data) => request.post('/inventory/records/stock_out/', data),
  
  // 库存事务
  getInventoryTransactions: (params) => request.get('/inventory/transactions/', { params }),
}

// 采购管理API
export const procurementApi = {
  // 供应商管理
  getSuppliers: (params) => request.get('/procurement/suppliers/', { params }),
  getSupplier: (id) => request.get(`/procurement/suppliers/${id}/`),
  createSupplier: (data) => request.post('/procurement/suppliers/', data),
  updateSupplier: (id, data) => request.put(`/procurement/suppliers/${id}/`, data),
  deleteSupplier: (id) => request.delete(`/procurement/suppliers/${id}/`),
  
  // 采购申请
  getPurchaseRequests: (params) => request.get('/procurement/requests/', { params }),
  getPurchaseRequest: (id) => request.get(`/procurement/requests/${id}/`),
  createPurchaseRequest: (data) => request.post('/procurement/requests/', data),
  updatePurchaseRequest: (id, data) => request.put(`/procurement/requests/${id}/`, data),
  deletePurchaseRequest: (id) => request.delete(`/procurement/requests/${id}/`),
  
  // 审批功能
  approvePurchaseRequest: (id) => request.post(`/procurement/requests/${id}/approve/`),
  rejectPurchaseRequest: (id, data) => request.post(`/procurement/requests/${id}/reject/`, data),
  
  // 重新申请功能
  resubmitPurchaseRequest: (id) => request.post(`/procurement/requests/${id}/resubmit/`),
  
  // 备注功能
  updatePurchaseRequestNotes: (id, data) => request.post(`/procurement/requests/${id}/update_notes/`, data),
}

// 用户认证API
export const authApi = {
  // 登录注册
  login: (data) => request.post('/users/auth/login/', data),
  register: (data) => request.post('/users/auth/register/', data),
  logout: () => request.post('/users/auth/logout/'),
  getProfile: () => request.get('/users/auth/profile/'),
  
  // 修改密码
  changePassword: (userId, data) => request.post(`/users/users/${userId}/change_password/`, data),
}

// 用户管理API
export const userApi = {
  // 用户
  getUsers: (params) => request.get('/users/users/', { params }),
  getUser: (id) => request.get(`/users/users/${id}/`),
  createUser: (data) => request.post('/users/users/', data),
  updateUser: (id, data) => request.put(`/users/users/${id}/`, data),
  deleteUser: (id) => request.delete(`/users/users/${id}/`),
  activateUser: (id) => request.post(`/users/users/${id}/activate/`),
  deactivateUser: (id) => request.post(`/users/users/${id}/deactivate/`),
  
  // 用户配置
  getUserProfiles: (params) => request.get('/users/profiles/', { params }),
  getUserProfile: (id) => request.get(`/users/profiles/${id}/`),
  createUserProfile: (data) => request.post('/users/profiles/', data),
  updateUserProfile: (id, data) => request.put(`/users/profiles/${id}/`, data),
  deleteUserProfile: (id) => request.delete(`/users/profiles/${id}/`),
  
  // 部门
  getDepartments: (params) => request.get('/users/departments/', { params }),
  getDepartmentTree: () => request.get('/users/departments/tree/'),
  createDepartment: (data) => request.post('/users/departments/', data),
  updateDepartment: (id, data) => request.put(`/users/departments/${id}/`, data),
  deleteDepartment: (id) => request.delete(`/users/departments/${id}/`),
  activateDepartment: (id) => request.post(`/users/departments/${id}/activate/`),
  deactivateDepartment: (id) => request.post(`/users/departments/${id}/deactivate/`),
  getDepartmentUsers: (id) => request.get(`/users/departments/${id}/users/`),
}