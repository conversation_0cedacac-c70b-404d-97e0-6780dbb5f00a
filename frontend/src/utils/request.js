/**
 * HTTP请求工具类
 * 基于axios封装，统一处理请求和响应
 */

import axios from 'axios'
import { ElMessage } from 'element-plus'

// 获取API基础URL
const getApiBaseUrl = () => {
  // 强制使用开发机器IP地址，请不要改动这里
  const apiUrl = 'http://*************:8000/api'

  return apiUrl
}

// 创建axios实例
const request = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Token ${token}`
    }
    
    // 添加CSRF token
    const csrfToken = document.cookie.split('; ').find(row => row.startsWith('csrftoken='))
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken.split('=')[1]
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // 只处理401未授权的情况，其他错误让组件自己处理
    if (error.response && error.response.status === 401) {
      ElMessage.error('未授权，请重新登录')
      // 清除token并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

export default request