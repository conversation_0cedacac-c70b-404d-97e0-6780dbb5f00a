<template>
  <div class="optimized-model-3d-viewer">
    <div ref="container" class="viewer-container">
      <!-- 加载进度显示 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-icon">
            <i class="el-icon-loading"></i>
          </div>
          <el-progress 
            :percentage="loadingProgress" 
            :show-text="false"
            :stroke-width="8"
            color="#409EFF"
          />
          <p class="loading-text">{{ loadingStatus }}</p>
          <p class="loading-tip">{{ loadingTip }}</p>
        </div>
      </div>
    </div>
    <div class="viewer-controls">
      <el-button-group>
        <el-button size="small" @click="resetView">重置视图</el-button>
        <el-button size="small" @click="toggleWireframe">线框模式</el-button>
        <el-button size="small" @click="togglePerformanceMode">性能模式</el-button>
        <el-button size="small" @click="downloadModel">下载模型</el-button>
      </el-button-group>
      <div class="performance-info" v-if="performanceData.fps">
        <span class="fps-counter">FPS: {{ performanceData.fps }}</span>
        <span class="vertex-counter">顶点: {{ performanceData.vertices }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import * as THREE from 'three'
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelUrl: {
    type: String,
    required: true
  },
  modelType: {
    type: String,
    default: 'stl'
  },
  enableOptimizations: {
    type: Boolean,
    default: true
  }
})

// 响应式数据
const container = ref(null)
const loadingProgress = ref(0)
const loadingStatus = ref('')
const loadingTip = ref('正在启动高性能3D渲染器...')
const isLoading = ref(false)
const performanceMode = ref(false)

// 性能监控数据
const performanceData = ref({
  fps: 0,
  vertices: 0,
  triangles: 0,
  renderTime: 0
})

// 3D对象
let scene, camera, renderer, controls
let model = null
let wireframe = false
let clock = new THREE.Clock()
let frameCount = 0
let lastFpsUpdate = 0

// Web Workers支持
let geometryWorker = null

// 自适应质量设置
const qualitySettings = ref({
  pixelRatio: Math.min(window.devicePixelRatio, 2),
  antialias: false,
  shadows: false,
  useBasicMaterial: false,
  useLOD: true,
  instancedRendering: false
})

// 检测设备性能能力
const detectDeviceCapabilities = () => {
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
  
  if (!gl) {
    return 'low'
  }

  // 检测GPU能力
  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
  const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : ''
  
  // 检测内存和核心数
  const cores = navigator.hardwareConcurrency || 4
  const memory = navigator.deviceMemory || 4

  // 移动设备检测
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  
  // 性能评分
  let score = 0
  score += cores * 10
  score += memory * 5
  score += isMobile ? -20 : 20
  score += renderer.toLowerCase().includes('nvidia') ? 20 : 0
  score += renderer.toLowerCase().includes('amd') ? 15 : 0
  score += renderer.toLowerCase().includes('intel') ? 5 : 0

  if (score > 80) return 'high'
  if (score > 40) return 'medium'
  return 'low'
}

// 根据设备能力调整质量设置
const adjustQualitySettings = () => {
  const capability = detectDeviceCapabilities()
  
  switch (capability) {
    case 'low':
      qualitySettings.value = {
        pixelRatio: 1,
        antialias: false,
        shadows: false,
        useBasicMaterial: true,
        useLOD: true,
        instancedRendering: true
      }
      break
    case 'medium':
      qualitySettings.value = {
        pixelRatio: Math.min(window.devicePixelRatio, 1.5),
        antialias: false,
        shadows: false,
        useBasicMaterial: false,
        useLOD: true,
        instancedRendering: false
      }
      break
    case 'high':
      qualitySettings.value = {
        pixelRatio: Math.min(window.devicePixelRatio, 2),
        antialias: true,
        shadows: true,
        useBasicMaterial: false,
        useLOD: false,
        instancedRendering: false
      }
      break
  }
}

// 初始化Web Worker进行几何体处理
const initGeometryWorker = () => {
  try {
    const workerCode = `
      self.onmessage = function(e) {
        const { geometryData, operation } = e.data;
        
        switch(operation) {
          case 'simplify':
            // 几何体简化算法
            const simplified = simplifyGeometry(geometryData);
            self.postMessage({ result: simplified, operation: 'simplify' });
            break;
          case 'computeNormals':
            // 计算法向量
            const withNormals = computeNormals(geometryData);
            self.postMessage({ result: withNormals, operation: 'computeNormals' });
            break;
        }
      };
      
      function simplifyGeometry(data) {
        // 简化几何体的算法实现
        // 这里可以实现边缘收缩或其他简化算法
        return data;
      }
      
      function computeNormals(data) {
        // 计算法向量的算法实现
        return data;
      }
    `
    
    const blob = new Blob([workerCode], { type: 'application/javascript' })
    geometryWorker = new Worker(URL.createObjectURL(blob))
    
    geometryWorker.onmessage = (e) => {
      console.log('Worker完成处理:', e.data.operation)
    }
  } catch (error) {
    console.warn('Web Worker不可用，使用主线程处理:', error)
  }
}

// 初始化高性能Three.js场景
const initOptimizedScene = () => {
  if (!container.value) return

  adjustQualitySettings()

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf8f9fa)

  // 创建相机
  const width = container.value.clientWidth
  const height = container.value.clientHeight
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.set(0, 0, 5)

  // 创建高性能渲染器
  renderer = new THREE.WebGLRenderer({ 
    antialias: qualitySettings.value.antialias,
    powerPreference: "high-performance",
    precision: "mediump", // 使用中等精度提高性能
    alpha: false, // 关闭透明度支持
    premultipliedAlpha: false,
    preserveDrawingBuffer: false,
    stencil: false,
    depth: true
  })
  
  renderer.setSize(width, height)
  renderer.setPixelRatio(qualitySettings.value.pixelRatio)
  renderer.shadowMap.enabled = qualitySettings.value.shadows
  renderer.shadowMap.type = THREE.PCFShadowMap
  renderer.outputEncoding = THREE.sRGBEncoding
  renderer.physicallyCorrectLights = true
  
  // 启用渲染器优化
  renderer.info.autoReset = false
  
  container.value.appendChild(renderer.domElement)

  // 优化的光照设置
  setupOptimizedLighting()

  // 创建控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.maxPolarAngle = Math.PI
  controls.enableZoom = true
  controls.enablePan = true

  // 启动渲染循环
  startOptimizedRenderLoop()
  
  // 初始化性能监控
  initPerformanceMonitoring()
}

// 设置优化的光照
const setupOptimizedLighting = () => {
  // 使用环境光 + 一个方向光的简化光照设置
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(10, 10, 5)
  
  if (qualitySettings.value.shadows) {
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.setScalar(1024) // 降低阴影贴图分辨率
    directionalLight.shadow.bias = -0.0001
  }
  
  scene.add(directionalLight)
}

// 优化的渲染循环
let animationId = null
let shouldRender = true
let lastRenderTime = 0

const startOptimizedRenderLoop = () => {
  const render = (timestamp) => {
    // 限制帧率以节省电量（可选）
    if (timestamp - lastRenderTime < 16) { // 约60fps
      animationId = requestAnimationFrame(render)
      return
    }
    lastRenderTime = timestamp

    // 更新控制器
    if (controls.enabled) {
      controls.update()
    }

    // 只在需要时渲染
    if (shouldRender || controls.autoRotate) {
      renderer.render(scene, camera)
      updatePerformanceMetrics()
    }

    // 重置渲染标志
    shouldRender = false

    animationId = requestAnimationFrame(render)
  }

  animationId = requestAnimationFrame(render)
  
  // 监听控制器变化
  controls.addEventListener('change', () => {
    shouldRender = true
  })
}

// 性能监控
const initPerformanceMonitoring = () => {
  setInterval(() => {
    const delta = clock.getDelta()
    frameCount++
    
    if (clock.getElapsedTime() - lastFpsUpdate > 1) {
      performanceData.value.fps = Math.round(frameCount / (clock.getElapsedTime() - lastFpsUpdate))
      frameCount = 0
      lastFpsUpdate = clock.getElapsedTime()
    }
  }, 100)
}

const updatePerformanceMetrics = () => {
  if (renderer.info) {
    performanceData.value.vertices = renderer.info.render.triangles * 3
    performanceData.value.triangles = renderer.info.render.triangles
  }
}

// 优化的模型加载
const loadOptimizedModel = async () => {
  if (!props.modelUrl) {
    console.warn('No model URL provided')
    return
  }

  console.log('Loading optimized 3D model:', props.modelUrl, 'Type:', props.modelType)
  
  isLoading.value = true
  loadingProgress.value = 0
  loadingStatus.value = '正在加载3D模型...'
  loadingTip.value = '使用高性能渲染器，请稍候...'

  try {
    let geometry

    if (props.modelType.toLowerCase() === 'stl') {
      geometry = await loadSTLWithOptimizations()
    } else if (props.modelType.toLowerCase() === 'glb') {
      geometry = await loadGLTFWithOptimizations()
    }

    if (geometry) {
      await createOptimizedMesh(geometry)
    }

  } catch (error) {
    console.error('Error loading optimized model:', error)
    handleLoadingError(error)
  }
}

// 优化的STL加载
const loadSTLWithOptimizations = async () => {
  loadingStatus.value = '正在加载STL文件...'
  const loader = new STLLoader()
  
  return new Promise((resolve, reject) => {
    const startTime = performance.now()
    
    loader.load(
      props.modelUrl,
      (geometry) => {
        const loadTime = performance.now() - startTime
        console.log(`STL加载完成，耗时: ${loadTime.toFixed(2)}ms`)
        
        loadingProgress.value = 60
        loadingStatus.value = '正在优化几何体...'
        
        // 异步优化几何体
        setTimeout(() => {
          const optimizedGeometry = optimizeGeometry(geometry)
          resolve(optimizedGeometry)
        }, 0)
      },
      (progress) => {
        if (progress.lengthComputable) {
          const percent = Math.round((progress.loaded / progress.total) * 60)
          loadingProgress.value = percent
          loadingStatus.value = `正在加载STL文件... ${percent}%`
        }
      },
      reject
    )
  })
}

// 优化的GLTF加载（支持Draco压缩）
const loadGLTFWithOptimizations = async () => {
  loadingStatus.value = '正在加载GLTF模型...'
  
  const loader = new GLTFLoader()
  
  // 启用Draco解压缩
  const dracoLoader = new DRACOLoader()
  dracoLoader.setDecoderPath('https://www.gstatic.com/draco/v1/decoders/')
  dracoLoader.setDecoderConfig({ type: 'js' })
  loader.setDRACOLoader(dracoLoader)
  
  return new Promise((resolve, reject) => {
    loader.load(
      props.modelUrl,
      (gltf) => {
        loadingProgress.value = 70
        loadingStatus.value = '正在提取几何体...'
        
        // 提取第一个mesh的几何体
        let geometry = null
        gltf.scene.traverse((child) => {
          if (child.isMesh && !geometry) {
            geometry = child.geometry
          }
        })
        
        resolve(geometry)
      },
      (progress) => {
        if (progress.lengthComputable) {
          const percent = Math.round((progress.loaded / progress.total) * 70)
          loadingProgress.value = percent
        }
      },
      reject
    )
  })
}

// 几何体优化
const optimizeGeometry = (geometry) => {
  loadingStatus.value = '正在优化几何体性能...'
  
  // 确保使用BufferGeometry
  if (!geometry.isBufferGeometry) {
    geometry = new THREE.BufferGeometry().fromGeometry(geometry)
  }

  // 计算边界信息
  geometry.computeBoundingBox()
  geometry.computeBoundingSphere()
  
  // 计算法向量（如果没有）
  if (!geometry.attributes.normal) {
    geometry.computeVertexNormals()
  }

  // 合并重复顶点
  geometry = THREE.BufferGeometryUtils.mergeVertices(geometry)
  
  const vertexCount = geometry.attributes.position.count
  console.log(`几何体优化完成，顶点数: ${vertexCount}`)
  
  // 根据顶点数决定是否需要进一步简化
  if (vertexCount > 100000 && qualitySettings.value.useLOD) {
    console.log('高顶点数模型，正在简化...')
    geometry = simplifyGeometry(geometry, 0.7) // 简化到70%
  }

  return geometry
}

// 几何体简化（基础版本）
const simplifyGeometry = (geometry, ratio) => {
  // 这里可以实现更复杂的简化算法
  // 目前使用简单的顶点抽取
  const positions = geometry.attributes.position.array
  const normals = geometry.attributes.normal?.array
  
  const targetVertexCount = Math.floor(positions.length / 3 * ratio)
  const step = Math.ceil((positions.length / 3) / targetVertexCount)
  
  const newPositions = []
  const newNormals = []
  
  for (let i = 0; i < positions.length; i += step * 3) {
    newPositions.push(positions[i], positions[i + 1], positions[i + 2])
    if (normals) {
      newNormals.push(normals[i], normals[i + 1], normals[i + 2])
    }
  }
  
  const newGeometry = new THREE.BufferGeometry()
  newGeometry.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3))
  if (newNormals.length > 0) {
    newGeometry.setAttribute('normal', new THREE.Float32BufferAttribute(newNormals, 3))
  }
  
  return newGeometry
}

// 创建优化的网格
const createOptimizedMesh = async (geometry) => {
  loadingStatus.value = '正在创建优化材质...'
  loadingProgress.value = 85

  // 移除现有模型
  if (model) {
    scene.remove(model)
    model.geometry?.dispose()
    model.material?.dispose()
  }

  // 选择高性能材质
  let material
  if (qualitySettings.value.useBasicMaterial || performanceMode.value) {
    material = new THREE.MeshBasicMaterial({
      color: 0x4a90e2,
      side: THREE.FrontSide
    })
  } else {
    material = new THREE.MeshLambertMaterial({
      color: 0x4a90e2,
      side: THREE.FrontSide,
      flatShading: false
    })
  }

  // 创建网格
  model = new THREE.Mesh(geometry, material)
  model.castShadow = qualitySettings.value.shadows
  model.receiveShadow = qualitySettings.value.shadows

  loadingStatus.value = '正在调整模型位置...'
  loadingProgress.value = 95

  // 居中并调整模型
  const box = new THREE.Box3().setFromObject(model)
  const center = box.getCenter(new THREE.Vector3())
  model.position.sub(center)

  // 调整相机位置
  const size = box.getSize(new THREE.Vector3())
  const maxDim = Math.max(size.x, size.y, size.z)
  const fov = camera.fov * (Math.PI / 180)
  let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2))
  cameraZ *= 2.5

  camera.position.set(cameraZ, cameraZ, cameraZ)
  camera.lookAt(0, 0, 0)
  
  if (controls) {
    controls.target.set(0, 0, 0)
    controls.update()
  }

  scene.add(model)

  // 完成加载
  loadingProgress.value = 100
  loadingStatus.value = '加载完成'
  
  // 延迟隐藏加载界面，确保渲染完成
  setTimeout(() => {
    isLoading.value = false
    shouldRender = true
    ElMessage.success('3D模型已优化加载完成！')
  }, 500)

  console.log('优化后的3D模型已加载完成')
}

// 处理加载错误
const handleLoadingError = (error) => {
  console.error('加载错误:', error)
  isLoading.value = false
  loadingStatus.value = '加载失败'
  
  const errorContainer = document.createElement('div')
  errorContainer.innerHTML = `
    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c;">
      <div style="text-align: center;">
        <h3>高性能3D渲染器加载失败</h3>
        <p>错误信息: ${error.message}</p>
        <p>正在尝试使用标准渲染器...</p>
        <el-button type="primary" onclick="location.reload()">重新加载</el-button>
      </div>
    </div>
  `
  
  if (container.value) {
    container.value.appendChild(errorContainer)
  }
}

// 控制方法
const resetView = () => {
  if (controls) {
    controls.reset()
    shouldRender = true
  }
}

const toggleWireframe = () => {
  if (model && model.material) {
    wireframe = !wireframe
    model.material.wireframe = wireframe
    shouldRender = true
  }
}

const togglePerformanceMode = () => {
  performanceMode.value = !performanceMode.value
  
  if (model && model.material) {
    // 切换材质类型
    const currentColor = model.material.color.getHex()
    model.material.dispose()
    
    if (performanceMode.value) {
      model.material = new THREE.MeshBasicMaterial({
        color: currentColor,
        wireframe: wireframe,
        side: THREE.FrontSide
      })
      ElMessage.info('已启用性能模式（基础材质）')
    } else {
      model.material = new THREE.MeshLambertMaterial({
        color: currentColor,
        wireframe: wireframe,
        side: THREE.FrontSide
      })
      ElMessage.info('已禁用性能模式（标准材质）')
    }
    
    shouldRender = true
  }
}

const downloadModel = () => {
  if (props.modelUrl) {
    const link = document.createElement('a')
    link.href = props.modelUrl
    link.download = props.modelUrl.split('/').pop()
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (!container.value || !camera || !renderer) return

  const width = container.value.clientWidth
  const height = container.value.clientHeight

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
  shouldRender = true
}

// 监听模型URL变化
watch(() => props.modelUrl, () => {
  if (props.modelUrl) {
    loadOptimizedModel()
  }
})

// 生命周期
onMounted(() => {
  console.log('正在初始化高性能3D渲染器...')
  initGeometryWorker()
  initOptimizedScene()
  
  if (props.modelUrl) {
    loadOptimizedModel()
  }
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  
  if (geometryWorker) {
    geometryWorker.terminate()
  }
  
  if (renderer && container.value) {
    container.value.removeChild(renderer.domElement)
  }
  
  if (renderer) {
    renderer.dispose()
  }
  
  // 清理资源
  if (model) {
    model.geometry?.dispose()
    model.material?.dispose()
  }
  
  scene?.clear()
})
</script>

<style scoped>
.optimized-model-3d-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.viewer-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.viewer-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.viewer-controls .el-button-group {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.performance-info {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.fps-counter {
  color: #4CAF50;
  font-weight: bold;
}

.vertex-counter {
  color: #2196F3;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  text-align: center;
  padding: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 320px;
}

.loading-text {
  margin-top: 15px;
  color: #606266;
  font-size: 16px;
  font-weight: 500;
}

.loading-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 13px;
}

.loading-icon {
  margin-bottom: 20px;
  font-size: 32px;
  color: #409EFF;
}

.loading-icon i {
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viewer-controls {
    top: 5px;
    left: 5px;
  }
  
  .viewer-controls .el-button-group {
    padding: 6px;
  }
  
  .loading-content {
    margin: 20px;
    padding: 20px;
    min-width: auto;
  }
}
</style>
