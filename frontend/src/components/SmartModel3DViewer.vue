<template>
  <div class="smart-model-3d-viewer">
    <!-- 引擎选择器 -->
    <div v-if="showEngineSelector" class="engine-selector-overlay">
      <div class="engine-selector">
        <h3>选择3D渲染引擎</h3>
        <p class="device-info">
          设备能力评分: <span :class="getScoreClass(deviceScore)">{{ deviceScore }}</span>
          ({{ getDeviceCapability() }})
        </p>
        
        <div class="engine-options">
          <div 
            v-for="engine in availableEngines" 
            :key="engine.name"
            class="engine-option"
            :class="{ 'recommended': engine.recommended, 'selected': selectedEngine === engine.name }"
            @click="selectEngine(engine.name)"
          >
            <div class="engine-icon">
              <i :class="engine.icon"></i>
            </div>
            <div class="engine-details">
              <h4>{{ engine.title }}</h4>
              <p>{{ engine.description }}</p>
              <div class="engine-metrics">
                <span class="metric">性能: {{ engine.performance }}/5</span>
                <span class="metric">质量: {{ engine.quality }}/5</span>
                <span class="metric">兼容性: {{ engine.compatibility }}/5</span>
              </div>
              <div v-if="engine.recommended" class="recommended-badge">推荐</div>
            </div>
          </div>
        </div>
        
        <div class="selector-actions">
          <el-button @click="useAutoSelection">使用推荐引擎</el-button>
          <el-button type="primary" @click="confirmEngineSelection" :disabled="!selectedEngine">
            确认选择
          </el-button>
        </div>
      </div>
    </div>

    <!-- 动态渲染器组件 -->
    <component 
      v-if="!showEngineSelector"
      :is="currentRendererComponent"
      :modelUrl="modelUrl"
      :modelType="modelType"
      v-bind="rendererProps"
      @performance-update="handlePerformanceUpdate"
      @fallback-to-threejs="handleFallbackToThreeJS"
    />

    <!-- 性能监控悬浮窗 -->
    <div v-if="showPerformanceMonitor" class="performance-monitor">
      <div class="monitor-header">
        <span>性能监控</span>
        <button @click="showPerformanceMonitor = false" class="close-btn">×</button>
      </div>
      <div class="monitor-content">
        <div class="metric-row">
          <span>当前引擎:</span>
          <span class="engine-name">{{ getCurrentEngineTitle() }}</span>
        </div>
        <div class="metric-row">
          <span>FPS:</span>
          <span :class="getFPSClass(currentPerformance.fps)">{{ currentPerformance.fps }}</span>
        </div>
        <div class="metric-row">
          <span>内存使用:</span>
          <span>{{ formatMemory(currentPerformance.memory) }}</span>
        </div>
        <div class="metric-row">
          <span>渲染时间:</span>
          <span>{{ currentPerformance.renderTime }}ms</span>
        </div>
        
        <div v-if="currentPerformance.fps < 30" class="performance-warning">
          <i class="el-icon-warning"></i>
          <span>性能较差，建议切换引擎</span>
          <el-button size="mini" @click="suggestBetterEngine">优化</el-button>
        </div>
      </div>
    </div>

    <!-- 快速切换按钮 -->
    <div class="quick-actions">
      <el-button size="mini" @click="showEngineSelector = true" title="切换引擎">
        <i class="el-icon-refresh"></i>
      </el-button>
      <el-button size="mini" @click="showPerformanceMonitor = !showPerformanceMonitor" title="性能监控">
        <i class="el-icon-data-line"></i>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, shallowRef } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import OptimizedModel3DViewer from './OptimizedModel3DViewer.vue'
import FullBabylonModel3DViewer from './FullBabylonModel3DViewer.vue'
// 如果需要原版渲染器作为fallback
import Model3DViewer from './Model3DViewer.vue'

// Props
const props = defineProps({
  modelUrl: {
    type: String,
    required: true
  },
  modelType: {
    type: String,
    default: 'stl'
  },
  autoSelectEngine: {
    type: Boolean,
    default: true
  },
  showEngineSelector: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const showEngineSelector = ref(props.showEngineSelector)
const selectedEngine = ref('')
const currentEngine = ref('')
const deviceScore = ref(0)
const showPerformanceMonitor = ref(false)
const currentRendererComponent = shallowRef(null)

// 性能数据
const currentPerformance = ref({
  fps: 0,
  memory: 0,
  renderTime: 0,
  triangles: 0
})

// 引擎性能历史
const enginePerformanceHistory = ref({})

// 可用引擎配置
const availableEngines = ref([
  {
    name: 'optimized-threejs',
    title: 'Three.js 优化版',
    description: '基于Three.js的高性能渲染器，针对Web进行了深度优化',
    icon: 'el-icon-lightning',
    performance: 5,
    quality: 4,
    compatibility: 5,
    component: OptimizedModel3DViewer,
    minScore: 0,
    maxScore: 100,
    recommended: false
  },
  {
    name: 'babylonjs',
    title: 'Babylon.js 引擎',
    description: '微软支持的全功能3D引擎，提供专业级渲染质量',
    icon: 'el-icon-view',
    performance: 4,
    quality: 5,
    compatibility: 4,
    component: FullBabylonModel3DViewer,
    minScore: 40,
    maxScore: 100,
    recommended: false
  },
  {
    name: 'threejs-basic',
    title: 'Three.js 标准版',
    description: '经典Three.js渲染器，兼容性最佳，适合低端设备',
    icon: 'el-icon-monitor',
    performance: 3,
    quality: 3,
    compatibility: 5,
    component: Model3DViewer,
    minScore: 0,
    maxScore: 60,
    recommended: false
  }
])

// 计算设备能力分数
const calculateDeviceScore = () => {
  let score = 0
  
  // 基础分数
  score += 20
  
  // CPU核心数
  const cores = navigator.hardwareConcurrency || 4
  score += Math.min(cores * 8, 32)
  
  // 内存
  const memory = navigator.deviceMemory || 4
  score += Math.min(memory * 4, 20)
  
  // 移动设备检测
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  score += isMobile ? -15 : 15
  
  // WebGL版本检测
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
  
  if (gl) {
    score += 10
    
    // GPU信息
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
    if (debugInfo) {
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL).toLowerCase()
      
      if (renderer.includes('nvidia')) score += 15
      else if (renderer.includes('amd')) score += 12
      else if (renderer.includes('intel')) score += 8
      else if (renderer.includes('apple')) score += 10
      
      // 高端GPU检测
      if (renderer.includes('rtx') || renderer.includes('gtx')) score += 10
      if (renderer.includes('radeon')) score += 8
    }
    
    // WebGL扩展支持
    const extensions = gl.getSupportedExtensions()
    if (extensions) {
      score += Math.min(extensions.length * 0.5, 15)
    }
    
    // 最大纹理大小
    const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE)
    if (maxTextureSize >= 4096) score += 10
    else if (maxTextureSize >= 2048) score += 5
  }
  
  // 网络连接类型
  if (navigator.connection) {
    const connection = navigator.connection
    if (connection.effectiveType === '4g') score += 5
    else if (connection.effectiveType === '3g') score -= 5
    else if (connection.effectiveType === '2g') score -= 10
  }
  
  // 电池状态（如果支持）
  if (navigator.getBattery) {
    navigator.getBattery().then(battery => {
      if (battery.charging) score += 5
      if (battery.level < 0.2) score -= 10
    })
  }
  
  return Math.max(0, Math.min(100, score))
}

// 获取设备能力等级
const getDeviceCapability = () => {
  if (deviceScore.value >= 80) return '高端设备'
  if (deviceScore.value >= 60) return '中端设备'
  if (deviceScore.value >= 40) return '入门设备'
  return '低端设备'
}

// 获取分数颜色类
const getScoreClass = (score) => {
  if (score >= 80) return 'score-high'
  if (score >= 60) return 'score-medium'
  if (score >= 40) return 'score-low'
  return 'score-poor'
}

// 获取FPS颜色类
const getFPSClass = (fps) => {
  if (fps >= 50) return 'fps-excellent'
  if (fps >= 30) return 'fps-good'
  if (fps >= 20) return 'fps-poor'
  return 'fps-critical'
}

// 格式化内存显示
const formatMemory = (bytes) => {
  if (bytes === 0) return '0 MB'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 获取当前引擎标题
const getCurrentEngineTitle = () => {
  const engine = availableEngines.value.find(e => e.name === currentEngine.value)
  return engine ? engine.title : '未知引擎'
}

// 推荐引擎选择算法
const recommendEngine = () => {
  const score = deviceScore.value
  
  // 基于设备分数和模型复杂度推荐引擎
  let recommended = null
  
  if (score >= 70) {
    // 高端设备：优先使用Babylon.js以获得最佳质量
    recommended = availableEngines.value.find(e => e.name === 'babylonjs')
  } else if (score >= 45) {
    // 中端设备：使用优化版Three.js平衡性能和质量
    recommended = availableEngines.value.find(e => e.name === 'optimized-threejs')
  } else {
    // 低端设备：使用基础版Three.js确保兼容性
    recommended = availableEngines.value.find(e => e.name === 'threejs-basic')
  }
  
  // 设置推荐标记
  availableEngines.value.forEach(engine => {
    engine.recommended = engine.name === recommended?.name
  })
  
  return recommended
}

// 选择引擎
const selectEngine = (engineName) => {
  selectedEngine.value = engineName
}

// 使用自动选择
const useAutoSelection = () => {
  const recommended = recommendEngine()
  if (recommended) {
    selectedEngine.value = recommended.name
    confirmEngineSelection()
  }
}

// 确认引擎选择
const confirmEngineSelection = () => {
  if (!selectedEngine.value) return
  
  const engine = availableEngines.value.find(e => e.name === selectedEngine.value)
  if (engine) {
    currentEngine.value = selectedEngine.value
    currentRendererComponent.value = engine.component
    showEngineSelector.value = false
    
    ElMessage.success(`已切换到 ${engine.title}`)
    
    // 记录选择
    localStorage.setItem('preferred3DEngine', selectedEngine.value)
  }
}

// 建议更好的引擎
const suggestBetterEngine = () => {
  const currentEngineObj = availableEngines.value.find(e => e.name === currentEngine.value)
  const betterEngines = availableEngines.value.filter(e => 
    e.performance > currentEngineObj.performance && 
    deviceScore.value >= e.minScore
  )
  
  if (betterEngines.length > 0) {
    const bestEngine = betterEngines.reduce((best, current) => 
      current.performance > best.performance ? current : best
    )
    
    ElNotification({
      title: '性能优化建议',
      message: `建议切换到 ${bestEngine.title} 以提升性能`,
      type: 'warning',
      duration: 5000,
      showClose: true,
      onClick: () => {
        selectedEngine.value = bestEngine.name
        confirmEngineSelection()
      }
    })
  } else {
    ElMessage.info('当前已使用最适合您设备的引擎')
  }
}

// 处理性能更新
const handlePerformanceUpdate = (performance) => {
  currentPerformance.value = { ...performance }
  
  // 记录引擎性能历史
  if (!enginePerformanceHistory.value[currentEngine.value]) {
    enginePerformanceHistory.value[currentEngine.value] = []
  }
  
  enginePerformanceHistory.value[currentEngine.value].push({
    timestamp: Date.now(),
    ...performance
  })
  
  // 只保留最近100个记录
  if (enginePerformanceHistory.value[currentEngine.value].length > 100) {
    enginePerformanceHistory.value[currentEngine.value].shift()
  }
  
  // 自动性能优化
  if (performance.fps < 20 && currentEngine.value !== 'threejs-basic') {
    setTimeout(() => {
      suggestBetterEngine()
    }, 5000) // 5秒后提示
  }
}

// 处理回退到Three.js
const handleFallbackToThreeJS = () => {
  console.log('Babylon.js不可用，回退到Three.js')
  
  ElNotification({
    title: '引擎切换',
    message: 'Babylon.js暂不可用，已自动切换到Three.js渲染器',
    type: 'warning',
    duration: 3000
  })
  
  // 切换到优化版Three.js
  const fallbackEngine = availableEngines.value.find(e => e.name === 'optimized-threejs')
  if (fallbackEngine) {
    currentEngine.value = 'optimized-threejs'
    currentRendererComponent.value = fallbackEngine.component
  }
}

// 渲染器属性
const rendererProps = computed(() => {
  const baseProps = {
    modelUrl: props.modelUrl,
    modelType: props.modelType
  }
  
  // 根据引擎类型添加特定属性
  if (currentEngine.value === 'babylonjs') {
    return {
      ...baseProps,
      enableInspector: deviceScore.value >= 60
    }
  }
  
  if (currentEngine.value === 'optimized-threejs') {
    return {
      ...baseProps,
      enableOptimizations: true
    }
  }
  
  return baseProps
})

// 初始化
onMounted(() => {
  console.log('正在初始化智能3D渲染器选择器...')
  
  // 计算设备能力
  deviceScore.value = calculateDeviceScore()
  console.log('设备能力评分:', deviceScore.value)
  
  // 检查用户偏好
  const preferredEngine = localStorage.getItem('preferred3DEngine')
  
  if (props.autoSelectEngine && !props.showEngineSelector) {
    // 自动选择引擎
    if (preferredEngine && availableEngines.value.find(e => e.name === preferredEngine)) {
      // 使用用户偏好
      selectedEngine.value = preferredEngine
      confirmEngineSelection()
    } else {
      // 智能推荐
      const recommended = recommendEngine()
      if (recommended) {
        selectedEngine.value = recommended.name
        confirmEngineSelection()
      }
    }
  } else {
    // 显示选择器
    showEngineSelector.value = true
    recommendEngine() // 标记推荐引擎
    
    if (preferredEngine) {
      selectedEngine.value = preferredEngine
    }
  }
})

// 监听模型URL变化
watch(() => props.modelUrl, () => {
  // 重置性能数据
  currentPerformance.value = {
    fps: 0,
    memory: 0,
    renderTime: 0,
    triangles: 0
  }
})
</script>

<style scoped>
.smart-model-3d-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.engine-selector-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.engine-selector {
  background: white;
  border-radius: 16px;
  padding: 30px;
  max-width: 800px;
  width: 90%;
  max-height: 80%;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.engine-selector h3 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
  font-size: 24px;
}

.device-info {
  text-align: center;
  margin-bottom: 30px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #666;
}

.score-high { color: #4CAF50; font-weight: bold; }
.score-medium { color: #FF9800; font-weight: bold; }
.score-low { color: #FF5722; font-weight: bold; }
.score-poor { color: #F44336; font-weight: bold; }

.engine-options {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

.engine-option {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.engine-option:hover {
  border-color: #409EFF;
  background: #f0f8ff;
}

.engine-option.selected {
  border-color: #409EFF;
  background: #e6f3ff;
}

.engine-option.recommended {
  border-color: #52c41a;
  background: #f0f9ff;
}

.engine-icon {
  font-size: 48px;
  color: #409EFF;
  min-width: 60px;
  text-align: center;
}

.engine-details h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.engine-details p {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.engine-metrics {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.metric {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.recommended-badge {
  position: absolute;
  top: -8px;
  right: 10px;
  background: #52c41a;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.selector-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.performance-monitor {
  position: absolute;
  top: 60px;
  right: 10px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0;
  border-radius: 8px;
  min-width: 200px;
  z-index: 100;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.monitor-header {
  background: #409EFF;
  padding: 8px 12px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.monitor-content {
  padding: 12px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.engine-name {
  color: #409EFF;
  font-weight: bold;
}

.fps-excellent { color: #4CAF50; font-weight: bold; }
.fps-good { color: #8BC34A; font-weight: bold; }
.fps-poor { color: #FF9800; font-weight: bold; }
.fps-critical { color: #F44336; font-weight: bold; }

.performance-warning {
  margin-top: 12px;
  padding: 8px;
  background: rgba(244, 67, 54, 0.2);
  border-radius: 4px;
  border-left: 3px solid #F44336;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .engine-selector {
    padding: 20px;
    width: 95%;
  }
  
  .engine-option {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .engine-metrics {
    justify-content: center;
  }
  
  .performance-monitor {
    right: 5px;
    min-width: 180px;
  }
  
  .quick-actions {
    top: 5px;
    right: 5px;
  }
}
</style>
