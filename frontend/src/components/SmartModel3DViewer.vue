<template>
  <div class="model-3d-viewer">
    <div ref="container" class="viewer-container">
      <!-- 加载进度显示 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-icon">
            <i class="el-icon-loading"></i>
          </div>
          <el-progress 
            :percentage="loadingProgress" 
            :show-text="false"
            :stroke-width="8"
            color="#409EFF"
          />
          <p class="loading-text">{{ loadingStatus }}</p>
          <p class="loading-tip">大文件可能需要较长时间，请耐心等待</p>
        </div>
      </div>
    </div>
    <div class="viewer-controls">
      <el-button-group>
        <el-button size="small" @click="resetView">重置视图</el-button>
        <el-button size="small" @click="toggleWireframe">线框模式</el-button>
        <el-button size="small" @click="toggleGrid">网格</el-button>
        <el-button size="small" @click="toggleAxes">坐标轴</el-button>
      </el-button-group>
      <el-button size="small" type="primary" @click="downloadModel">下载模型</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as THREE from 'three'
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelUrl: {
    type: String,
    required: true
  },
  modelType: {
    type: String,
    default: 'stl' // stl, obj, etc.
  }
})

// 响应式数据
const container = ref(null)
const loadingProgress = ref(0)
const loadingStatus = ref('')
const isLoading = ref(false)
let scene, camera, renderer, controls
let model = null
let wireframe = false
let showGrid = true
let showAxes = true

// 初始化Three.js场景
const initScene = () => {
  if (!container.value) return

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xffffff) // 白色背景

  // 创建相机
  const width = container.value.clientWidth
  const height = container.value.clientHeight
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.set(0, 0, 5)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ 
    antialias: false, // 关闭抗锯齿以提高性能
    powerPreference: "high-performance" // 优先使用高性能GPU
  })
  renderer.setSize(width, height)
  renderer.shadowMap.enabled = false // 暂时关闭阴影以提高性能
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  renderer.setClearColor(0xffffff, 1) // 设置清除颜色为白色
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)) // 限制像素比以提高性能
  container.value.appendChild(renderer.domElement)

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.8) // 更强的环境光
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0) // 更强的方向光
  directionalLight.position.set(10, 10, 5)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 添加额外的光源以确保模型充分照明
  const pointLight = new THREE.PointLight(0xffffff, 0.5, 100)
  pointLight.position.set(-10, -10, -5)
  scene.add(pointLight)

  // 添加网格
  const gridHelper = new THREE.GridHelper(10, 10)
  gridHelper.name = 'grid'
  scene.add(gridHelper)

  // 添加坐标轴
  const axesHelper = new THREE.AxesHelper(5)
  axesHelper.name = 'axes'
  scene.add(axesHelper)

  // 添加控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05

  // 开始渲染循环
  animate()
}

// 渲染循环
const animate = () => {
  requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

// 加载3D模型
const loadModel = async () => {
  if (!props.modelUrl) {
    console.warn('No model URL provided')
    return
  }

  // start loading model
  
  // 重置加载状态
  isLoading.value = true
  loadingProgress.value = 0
  loadingStatus.value = '正在加载3D模型...'

  try {
    let geometry

    if (props.modelType.toLowerCase() === 'stl') {
      // loading STL model
      loadingStatus.value = '正在加载STL文件...'
      const loader = new STLLoader()
      
      // 添加性能优化选项
      const startTime = performance.now()
      
      geometry = await new Promise((resolve, reject) => {
        loader.load(
          props.modelUrl,
          (geometry) => {
            const loadTime = performance.now() - startTime
            loadingStatus.value = 'STL文件加载完成，正在处理几何体...'
            loadingProgress.value = 80
            resolve(geometry)
          },
          (progress) => {
            if (progress.lengthComputable) {
              const percent = Math.round((progress.loaded / progress.total) * 100)
              loadingProgress.value = percent
              loadingStatus.value = `正在加载STL文件... ${percent}%`
              
            }
          },
          (error) => {
            console.error('STL loading error:', error)
            loadingStatus.value = '加载失败'
            reject(error)
          }
        )
      })
    } else if (props.modelType.toLowerCase() === 'obj') {
      // loading OBJ model
      const loader = new OBJLoader()
      const object = await new Promise((resolve, reject) => {
        loader.load(
          props.modelUrl,
          (object) => {
            resolve(object)
          },
          (progress) => {},
          (error) => {
            console.error('OBJ loading error:', error)
            reject(error)
          }
        )
      })
      // 对于OBJ文件，我们需要提取几何体
      geometry = object.children[0]?.geometry || new THREE.BufferGeometry()
    } else {
      console.warn('Unsupported model type:', props.modelType)
      return
    }

    // 移除现有模型
    if (model) {
      scene.remove(model)
    }

    // creating material and mesh
    loadingStatus.value = '正在创建3D模型...'
    loadingProgress.value = 85

    // 优化几何体
    // optimizing geometry
    geometry.computeBoundingBox()
    geometry.computeBoundingSphere()
    
    // 如果顶点数过多，进行简化
    const vertexCount = geometry.attributes.position ? geometry.attributes.position.count : 0
    
    if (vertexCount > 100000) {
      // 可以在这里添加几何体简化逻辑
    }

    // 创建材质
    const material = new THREE.MeshPhongMaterial({
      color: 0x4a90e2, // 更亮的蓝色
      emissive: 0x1a4a7a, // 更亮的发光色
      side: THREE.FrontSide, // 只渲染正面，提高性能
      flatShading: false, // 使用平滑着色
      transparent: false,
      opacity: 1.0
    })

    // 创建网格
    model = new THREE.Mesh(geometry, material)
    model.castShadow = false // 暂时关闭阴影以提高性能
    model.receiveShadow = false

    // model created
    loadingStatus.value = '正在调整模型位置...'
    loadingProgress.value = 90

    // 居中模型
    const box = new THREE.Box3().setFromObject(model)
    const center = box.getCenter(new THREE.Vector3())
    model.position.sub(center)

    // model centered
    loadingStatus.value = '正在调整相机位置...'
    loadingProgress.value = 95

    // 调整相机位置以适应模型
    const size = box.getSize(new THREE.Vector3())
    const maxDim = Math.max(size.x, size.y, size.z)
    const fov = camera.fov * (Math.PI / 180)
    let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2))
    cameraZ *= 2.5 // 增加边距，确保模型完全可见
    camera.position.set(cameraZ, cameraZ, cameraZ)
    camera.lookAt(0, 0, 0)
    
    // 更新控制器目标
    if (controls) {
      controls.target.set(0, 0, 0)
      controls.update()
    }

    // camera positioned
    loadingStatus.value = '正在添加到场景...'
    loadingProgress.value = 98

    scene.add(model)
    // model added
    
    // 输出调试信息
    // debug output removed
    
    // 等待一帧渲染完成后再隐藏加载界面
    loadingStatus.value = '正在渲染模型...'
    loadingProgress.value = 99
    
    // 使用requestAnimationFrame确保渲染完成
    requestAnimationFrame(() => {
      // 再等待一帧确保模型完全渲染
      requestAnimationFrame(() => {
        // 加载完成
        isLoading.value = false
        loadingProgress.value = 100
        loadingStatus.value = '加载完成'
        
        // 强制重新渲染
        if (renderer && scene && camera) {
          renderer.render(scene, camera)
        }
        
        // 显示加载完成提示
        ElMessage.success('3D模型加载完成！')
      })
    })

  } catch (error) {
    console.error('Error loading model:', error)
    isLoading.value = false
    loadingStatus.value = '加载失败'
    
    // 显示错误信息给用户
    const container = document.querySelector('.viewer-container')
    if (container) {
      container.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c;">
          <div style="text-align: center;">
            <h3>3D模型加载失败</h3>
            <p>错误信息: ${error.message}</p>
            <p>文件大小: ${(957584 / 1024 / 1024).toFixed(2)} MB</p>
            <p>建议：</p>
            <ul style="text-align: left; margin: 10px 0;">
              <li>检查网络连接</li>
              <li>尝试刷新页面</li>
              <li>文件过大时可能需要等待更长时间</li>
            </ul>
            <el-button type="primary" @click="retryLoad">重试加载</el-button>
          </div>
        </div>
      `
    }
  }
}

// 控制方法
const resetView = () => {
  if (controls) {
    controls.reset()
  }
}

const toggleWireframe = () => {
  if (model) {
    wireframe = !wireframe
    model.material.wireframe = wireframe
  }
}

const toggleGrid = () => {
  showGrid = !showGrid
  const grid = scene.getObjectByName('grid')
  if (grid) {
    grid.visible = showGrid
  }
}

const toggleAxes = () => {
  showAxes = !showAxes
  const axes = scene.getObjectByName('axes')
  if (axes) {
    axes.visible = showAxes
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (!container.value || !camera || !renderer) return

  const width = container.value.clientWidth
  const height = container.value.clientHeight

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// 下载模型文件
const downloadModel = () => {
  if (props.modelUrl) {
    const link = document.createElement('a')
    link.href = props.modelUrl
    link.download = props.modelUrl.split('/').pop()
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 重试加载
const retryLoad = () => {
  loadModel()
}

// 监听模型URL变化
watch(() => props.modelUrl, () => {
  if (props.modelUrl) {
    loadModel()
  }
})

// 生命周期
onMounted(() => {
  initScene()
  if (props.modelUrl) {
    loadModel()
  }
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (renderer && container.value) {
    container.value.removeChild(renderer.domElement)
  }
  if (renderer) {
    renderer.dispose()
  }
})
</script>

<style scoped>
.model-3d-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.viewer-container {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
}

.viewer-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
}

.viewer-controls .el-button-group {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 5px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 300px;
}

.loading-text {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.loading-tip {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.loading-icon {
  margin-bottom: 15px;
  font-size: 24px;
  color: #409EFF;
}

.loading-icon i {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
