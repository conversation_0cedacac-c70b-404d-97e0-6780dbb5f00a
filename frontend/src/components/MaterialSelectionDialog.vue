<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    @close="handleClose"
  >
    <!-- 查询条件 -->
    <div class="search-section" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="search.search"
            placeholder="搜索物料编码或名称"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="search.major_category"
            placeholder="选择大类"
            clearable
            @change="handleMajorCategoryChange"
          >
            <el-option
              v-for="category in majorCategories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="search.middle_category"
            placeholder="选择中类"
            clearable
            :disabled="!search.major_category"
            @change="handleMiddleCategoryChange"
          >
            <el-option
              v-for="category in middleCategories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="search.category_code"
            placeholder="选择子类"
            clearable
            :disabled="!search.middle_category"
            @change="handleSearch"
          >
            <el-option
              v-for="category in subCategories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            查询
          </el-button>
          <el-button @click="handleReset">
            重置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="code" label="物料编码" width="150" />
      <el-table-column prop="name" label="物料名称" min-width="200" />
      <el-table-column prop="category_name" label="分类" width="200" />
      <el-table-column prop="specification" label="规格" min-width="200" />
      <el-table-column prop="unit" label="单位" width="80" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            size="small"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-wrapper" style="margin-top: 20px;">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSelect" :loading="submitting">
          选择物料
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { materialsApi } from '@/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'all', // 'product', 'preferred', 'all'
    validator: (value) => ['product', 'preferred', 'all'].includes(value)
  },
  itemIndex: {
    type: Number,
    default: -1
  }
})

// Emits
const emit = defineEmits(['update:visible', 'select'])

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const categories = ref([])

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const search = reactive({
  search: '',
  category_code: '',
  major_category: '',
  middle_category: ''
})

// 计算属性
const dialogTitle = computed(() => {
  if (props.type === 'product') {
    return '从成品库选择物料'
  } else if (props.type === 'preferred') {
    return '从优选库选择物料'
  } else {
    return '从物料库选择物料'
  }
})

const majorCategories = computed(() => {
  if (!categories.value.length) return []
  return categories.value.filter(cat => 
    cat.level === 1 || !cat.parent_code
  )
})

const middleCategories = computed(() => {
  if (!search.major_category || !categories.value.length) return []
  
  const majorCategory = categories.value.find(cat => 
    cat.code === search.major_category
  )
  
  if (majorCategory && majorCategory.children) {
    return majorCategory.children
  }
  
  return []
})

const subCategories = computed(() => {
  if (!search.middle_category || !categories.value.length) return []
  
  const middleCategory = categories.value.find(cat => 
    cat.code === search.middle_category
  )
  
  if (middleCategory && middleCategory.children) {
    return middleCategory.children
  }
  
  return []
})

// 方法
const loadCategories = async () => {
  try {
    const response = await materialsApi.getCategoryTree()
    const categoryTree = response || []
    
    // 将所有分类展平为一维数组
    const allCategories = []
    
    const flattenCategories = (categories) => {
      categories.forEach(category => {
        allCategories.push(category)
        if (category.children && category.children.length > 0) {
          flattenCategories(category.children)
        }
      })
    }
    
    flattenCategories(categoryTree)
    categories.value = allCategories
  } catch (error) {
    ElMessage.error('加载分类失败')
  }
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      status: 'ACTIVE'
    }
    
    // 根据对话框类型设置不同的筛选条件
    if (props.type === 'product') {
      // 成品库：只显示成品物料（非虚拟件）
      params.is_virtual = false
    } else if (props.type === 'preferred') {
      // 优选库：只显示优选库物料
      params.is_preferred = true
    }
    
    // 处理分类筛选
    if (search.major_category) {
      const majorCategory = categories.value.find(cat => 
        cat.code === search.major_category
      )
      
      if (majorCategory && majorCategory.children && majorCategory.children.length > 0) {
        const categoryCodes = [majorCategory.code]
        
        majorCategory.children.forEach(middleCategory => {
          categoryCodes.push(middleCategory.code)
          if (middleCategory.children) {
            middleCategory.children.forEach(subCategory => {
              categoryCodes.push(subCategory.code)
            })
          }
        })
        
        if (search.middle_category) {
          const middleCategory = majorCategory.children.find(cat => 
            cat.code === search.middle_category
          )
          
          if (middleCategory && middleCategory.children && middleCategory.children.length > 0) {
            if (search.category_code) {
              params.category_code = search.category_code
            } else {
              const subCategoryCodes = middleCategory.children.map(child => child.code)
              params.category_codes = subCategoryCodes.join(',')
            }
          } else if (middleCategory) {
            params.category_code = middleCategory.code
          }
        } else {
          params.category_codes = categoryCodes.join(',')
        }
      } else if (majorCategory) {
        params.category_code = majorCategory.code
      }
    }
    
    // 添加查询条件
    if (search.search) {
      params.search = search.search
    }
    
    const response = await materialsApi.getMaterials(params)
    tableData.value = response.results || []
    pagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载物料失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  search.search = ''
  search.category_code = ''
  search.major_category = ''
  search.middle_category = ''
  pagination.page = 1
  loadData()
}

const handleMajorCategoryChange = () => {
  search.middle_category = ''
  search.category_code = ''
  pagination.page = 1
  loadData()
}

const handleMiddleCategoryChange = () => {
  search.category_code = ''
  pagination.page = 1
  loadData()
}

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}

const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

const handleSelect = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择物料')
    return
  }
  
  if (selectedRows.value.length > 1) {
    ElMessage.warning('只能选择一个物料')
    return
  }
  
  const selectedMaterial = selectedRows.value[0]
  
  // 发送选中的物料和明细项索引
  emit('select', {
    material: selectedMaterial,
    itemIndex: props.itemIndex
  })
  
  handleClose()
}

const handleClose = () => {
  // 重置状态
  selectedRows.value = []
  search.search = ''
  search.category_code = ''
  search.major_category = ''
  search.middle_category = ''
  pagination.page = 1
  
  emit('update:visible', false)
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    DRAFT: 'info',
    ACTIVE: 'success',
    INACTIVE: 'warning',
    OBSOLETE: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    DRAFT: '草稿',
    ACTIVE: '活跃',
    INACTIVE: '停用',
    OBSOLETE: '废弃'
  }
  return statusMap[status] || '未知'
}

// 监听visible变化，当对话框打开时加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadCategories()
    loadData()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadCategories()
    loadData()
  }
})
</script>

<style scoped>
.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>
