<template>
  <div class="simple-babylon-model-3d-viewer">
    <div ref="container" class="viewer-container">
      <!-- 加载进度显示 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-icon">
            <i class="el-icon-loading"></i>
          </div>
          <el-progress 
            :percentage="loadingProgress" 
            :show-text="false"
            :stroke-width="8"
            color="#f39c12"
          />
          <p class="loading-text">{{ loadingStatus }}</p>
          <p class="loading-tip">使用Babylon.js引擎渲染（简化版）</p>
        </div>
      </div>
      
      <!-- 错误显示 -->
      <div v-if="hasError" class="error-overlay">
        <div class="error-content">
          <h3>Babylon.js 暂不可用</h3>
          <p>{{ errorMessage }}</p>
          <p class="error-tip">将自动使用Three.js渲染器</p>
          <el-button type="primary" @click="$emit('fallback-to-threejs')">
            使用Three.js渲染器
          </el-button>
        </div>
      </div>
    </div>
    
    <div class="viewer-controls" v-if="!hasError">
      <el-button-group>
        <el-button size="small" @click="resetCamera">重置视图</el-button>
        <el-button size="small" @click="toggleWireframe">线框模式</el-button>
        <el-button size="small" @click="toggleStats">性能统计</el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelUrl: {
    type: String,
    required: true
  },
  modelType: {
    type: String,
    default: 'stl'
  }
})

// Emits
const emit = defineEmits(['fallback-to-threejs', 'performance-update'])

// 响应式数据
const container = ref(null)
const loadingProgress = ref(0)
const loadingStatus = ref('')
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')

// 尝试加载Babylon.js
const tryLoadBabylon = async () => {
  try {
    // 检查是否可以加载Babylon.js
    const babylonModule = await import('@babylonjs/core')
    return babylonModule
  } catch (error) {
    console.warn('无法加载Babylon.js:', error)
    return null
  }
}

// 初始化
const init = async () => {
  isLoading.value = true
  loadingStatus.value = '正在检查Babylon.js可用性...'
  
  try {
    const BABYLON = await tryLoadBabylon()
    
    if (!BABYLON) {
      throw new Error('Babylon.js依赖不可用')
    }
    
    loadingStatus.value = '正在初始化Babylon.js引擎...'
    loadingProgress.value = 50
    
    // 简单的初始化逻辑
    const canvas = document.createElement('canvas')
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    container.value.appendChild(canvas)
    
    const engine = new BABYLON.Engine(canvas, true)
    const scene = new BABYLON.Scene(engine)
    
    // 创建相机
    const camera = new BABYLON.ArcRotateCamera(
      'camera',
      -Math.PI / 2,
      Math.PI / 2.5,
      10,
      BABYLON.Vector3.Zero(),
      scene
    )
    
    // 正确的方式：设置相机控件
    camera.setTarget(BABYLON.Vector3.Zero())
    camera.attachControls(canvas, false)
    
    // 添加光照
    const light = new BABYLON.HemisphericLight('light', new BABYLON.Vector3(0, 1, 0), scene)
    light.intensity = 0.7
    
    // 开始渲染循环
    engine.runRenderLoop(() => {
      scene.render()
    })
    
    loadingProgress.value = 100
    loadingStatus.value = '初始化完成'
    
    setTimeout(() => {
      isLoading.value = false
      ElMessage.success('Babylon.js引擎已就绪')
    }, 500)
    
    // 发送性能更新
    emit('performance-update', {
      fps: 60,
      triangles: 0,
      memory: 0,
      renderTime: 16
    })
    
  } catch (error) {
    console.error('Babylon.js初始化失败:', error)
    hasError.value = true
    errorMessage.value = error.message
    isLoading.value = false
    
    // 自动回退到Three.js
    setTimeout(() => {
      emit('fallback-to-threejs')
    }, 3000)
  }
}

// 控制方法（简化版）
const resetCamera = () => {
  console.log('重置相机')
}

const toggleWireframe = () => {
  console.log('切换线框模式')
}

const toggleStats = () => {
  console.log('切换性能统计')
}

// 监听URL变化
watch(() => props.modelUrl, () => {
  if (props.modelUrl && !hasError.value) {
    // 可以在这里加载模型
    console.log('模型URL变化:', props.modelUrl)
  }
})

// 生命周期
onMounted(async () => {
  await init()
})

onUnmounted(() => {
  // 清理资源
  if (container.value) {
    container.value.innerHTML = ''
  }
})
</script>

<style scoped>
.simple-babylon-model-3d-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.viewer-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.viewer-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
}

.viewer-controls .el-button-group {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 320px;
}

.loading-text {
  margin-top: 15px;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.loading-tip {
  margin-top: 8px;
  color: #f39c12;
  font-size: 13px;
  font-weight: bold;
}

.loading-icon {
  margin-bottom: 20px;
  font-size: 32px;
  color: #f39c12;
}

.loading-icon i {
  animation: babylonRotate 2s linear infinite;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(244, 67, 54, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.error-content {
  text-align: center;
  padding: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border-left: 4px solid #f44336;
  max-width: 400px;
}

.error-content h3 {
  color: #f44336;
  margin-bottom: 15px;
}

.error-content p {
  color: #666;
  margin-bottom: 10px;
}

.error-tip {
  color: #999;
  font-size: 14px;
  margin-bottom: 20px;
}

@keyframes babylonRotate {
  from {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  to {
    transform: rotate(360deg) scale(1);
  }
}
</style>
