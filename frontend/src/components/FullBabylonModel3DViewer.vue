<template>
  <div class="full-babylon-model-3d-viewer">
    <div ref="container" class="viewer-container">
      <!-- 加载进度显示 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-icon">
            <i class="el-icon-loading"></i>
          </div>
          <el-progress 
            :percentage="loadingProgress" 
            :show-text="false"
            :stroke-width="8"
            color="#f39c12"
          />
          <p class="loading-text">{{ loadingStatus }}</p>
          <p class="loading-tip">使用Babylon.js专业引擎</p>
        </div>
      </div>
      
      <!-- 性能统计显示 -->
      <div v-if="showPerformanceStats && !isLoading" class="performance-stats">
        <div class="stat-item">
          <span class="stat-label">FPS:</span>
          <span class="stat-value" :class="getFPSClass(performanceData.fps)">{{ performanceData.fps }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">面数:</span>
          <span class="stat-value">{{ performanceData.triangles.toLocaleString() }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">引擎:</span>
          <span class="stat-value engine-name">Babylon.js</span>
        </div>
      </div>
    </div>
    
    <div class="viewer-controls" v-if="!isLoading">
      <el-button-group>
        <el-button size="small" @click="resetCamera">重置视图</el-button>
        <el-button size="small" @click="toggleWireframe">线框模式</el-button>
        <el-button size="small" @click="toggleStats">性能统计</el-button>
        <el-button size="small" @click="toggleInspector" v-if="enableInspector">调试器</el-button>
      </el-button-group>
      
      <div class="quality-controls">
        <el-select v-model="qualityLevel" @change="adjustQuality" size="small">
          <el-option label="高质量" value="high"></el-option>
          <el-option label="平衡" value="medium"></el-option>
          <el-option label="高性能" value="low"></el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 导入Babylon.js核心和加载器
import * as BABYLON from '@babylonjs/core'
import { ArcRotateCamera } from '@babylonjs/core/Cameras/arcRotateCamera'
import { Vector3 } from '@babylonjs/core/Maths/math.vector'
import { HemisphericLight } from '@babylonjs/core/Lights/hemisphericLight'
import { DirectionalLight } from '@babylonjs/core/Lights/directionalLight'
import { ShadowGenerator } from '@babylonjs/core/Lights/Shadows/shadowGenerator'
import { SceneLoader } from '@babylonjs/core/Loading/sceneLoader'
import { EngineInstrumentation } from '@babylonjs/core/Instrumentation/engineInstrumentation'
import { SceneInstrumentation } from '@babylonjs/core/Instrumentation/sceneInstrumentation'
import { StandardMaterial } from '@babylonjs/core/Materials/standardMaterial'
import { Color3 } from '@babylonjs/core/Maths/math.color'
import { VertexData } from '@babylonjs/core/Meshes/mesh.vertexData'
import { VertexBuffer } from '@babylonjs/core/Buffers/buffer'
import '@babylonjs/loaders/STL'

// Props
const props = defineProps({
  modelUrl: {
    type: String,
    required: true
  },
  modelType: {
    type: String,
    default: 'stl'
  },
  enableInspector: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['performance-update'])

// 响应式数据
const container = ref(null)
const loadingProgress = ref(0)
const loadingStatus = ref('')
const isLoading = ref(false)
const showPerformanceStats = ref(false)
const qualityLevel = ref('medium')

// Babylon.js对象
let engine = null
let scene = null
let camera = null
let model = null
let inspector = null

// 性能监控数据
const performanceData = ref({
  fps: 0,
  triangles: 0,
  renderTime: 0,
  memoryUsage: 0
})

// 质量设置
const qualitySettings = {
  high: {
    renderScale: 1.0,
    antialias: true,
    shadows: true,
    postProcessing: true,
    lod: false,
    maxTextures: 16
  },
  medium: {
    renderScale: 0.8,
    antialias: false,
    shadows: false,
    postProcessing: false,
    lod: true,
    maxTextures: 8
  },
  low: {
    renderScale: 0.6,
    antialias: false,
    shadows: false,
    postProcessing: false,
    lod: true,
    maxTextures: 4
  }
}

// 初始化Babylon.js引擎
const initBabylonEngine = async () => {
  if (!container.value) return

  console.log('正在初始化Babylon.js引擎...')

  try {
    // 创建canvas
    const canvas = document.createElement('canvas')
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    container.value.appendChild(canvas)

    // 引擎配置
    const engineOptions = {
      antialias: qualitySettings[qualityLevel.value].antialias,
      powerPreference: 'high-performance',
      doNotHandleContextLost: true,
      audioEngine: false,
      stencil: false
    }

    engine = new BABYLON.Engine(canvas, true, engineOptions, true)
    
    // 引擎优化设置
    engine.setHardwareScalingLevel(1 / qualitySettings[qualityLevel.value].renderScale)
    
    // 创建场景
    scene = new BABYLON.Scene(engine)
    scene.actionManager = new BABYLON.ActionManager(scene)
    
    // 场景优化设置
    scene.skipPointerMovePicking = true
    scene.constantlyUpdateMeshUnderPointer = false
    scene.autoClear = true
    scene.autoClearDepthAndStencil = true
    
    // 创建相机
    camera = new ArcRotateCamera(
      'camera',
      -Math.PI / 2,
      Math.PI / 2.5,
      10,
      Vector3.Zero(),
      scene
    )
    
    // 设置相机控制
    camera.setTarget(Vector3.Zero())
    camera.attachControl(canvas, false)
    camera.wheelPrecision = 50
    camera.panningDistanceLimit = 20
    camera.lowerRadiusLimit = 2
    camera.upperRadiusLimit = 100

    // 优化的光照设置
    setupBabylonLighting()

    // 启动渲染循环
    startBabylonRenderLoop()
    
    // 初始化性能监控
    initBabylonPerformanceMonitoring()

    console.log('Babylon.js引擎初始化完成')
    
    // 如果有模型URL，立即加载
    if (props.modelUrl) {
      await loadBabylonModel()
    }
    
  } catch (error) {
    console.error('Babylon.js引擎初始化失败:', error)
    throw error
  }
}

// 设置Babylon.js光照
const setupBabylonLighting = () => {
  // 环境光
  const hemisphericLight = new HemisphericLight(
    'hemisphericLight',
    new Vector3(0, 1, 0),
    scene
  )
  hemisphericLight.intensity = 0.7

  // 方向光
  const directionalLight = new DirectionalLight(
    'directionalLight',
    new Vector3(0, -1, 0),
    scene
  )
  directionalLight.position = new Vector3(0, 10, 0)
  directionalLight.intensity = 0.8

  // 阴影设置（仅在高质量模式下启用）
  if (qualitySettings[qualityLevel.value].shadows) {
    const shadowGenerator = new ShadowGenerator(1024, directionalLight)
    shadowGenerator.useBlurExponentialShadowMap = true
  }
}

// Babylon.js渲染循环
const startBabylonRenderLoop = () => {
  let lastFrameTime = performance.now()
  let frameCount = 0
  
  const renderLoop = () => {
    const currentTime = performance.now()
    const deltaTime = currentTime - lastFrameTime
    
    // 更新性能统计
    frameCount++
    if (frameCount % 60 === 0) {
      performanceData.value.fps = Math.round(1000 / (deltaTime / 60))
      performanceData.value.renderTime = Math.round(deltaTime / 60)
      
      if (scene) {
        performanceData.value.triangles = scene.getActiveMeshes().length > 0 
          ? scene.getActiveMeshes().data.reduce((total, mesh) => {
              return total + (mesh.getTotalVertices() / 3)
            }, 0)
          : 0
      }
      
      // 发送性能更新
      emit('performance-update', performanceData.value)
    }
    
    // 渲染场景
    scene.render()
    
    lastFrameTime = currentTime
  }

  engine.runRenderLoop(renderLoop)
}

// 性能监控
const initBabylonPerformanceMonitoring = () => {
  try {
    // 初始化场景性能监控
    if (!scene.instrumentation) {
      scene.instrumentation = new SceneInstrumentation(scene)
    }
    
    // 启用Babylon.js内置性能监控
    scene.instrumentation.captureRenderTime = true
    scene.instrumentation.captureFrameTime = true
    
    // 初始化引擎性能监控
    if (!engine.instrumentation) {
      engine.instrumentation = new EngineInstrumentation(engine)
    }
    
    setInterval(() => {
      if (scene.instrumentation && scene.instrumentation.renderTimeCounter) {
        performanceData.value.renderTime = Math.round(scene.instrumentation.renderTimeCounter.average)
      }
    }, 1000)
    
    console.log('Babylon.js性能监控初始化完成')
  } catch (error) {
    console.warn('性能监控初始化失败，继续运行:', error)
  }
}

// 加载STL模型
const loadBabylonModel = async () => {
  if (!props.modelUrl || !scene) return
  
  isLoading.value = true
  loadingProgress.value = 0
  loadingStatus.value = '正在加载3D模型...'
  
  try {
    console.log('开始加载STL模型:', props.modelUrl)
    
    // 使用Babylon.js的SceneLoader加载STL
    const importResult = await SceneLoader.ImportMeshAsync(
      '',
      '',
      props.modelUrl,
      scene,
      (evt) => {
        if (evt.lengthComputable) {
          const progress = Math.round((evt.loaded / evt.total) * 80)
          loadingProgress.value = progress
          loadingStatus.value = `正在加载模型... ${progress}%`
        }
      }
    )

    loadingProgress.value = 85
    loadingStatus.value = '正在优化模型...'

    if (importResult.meshes.length > 0) {
      // 获取第一个mesh作为主模型
      model = importResult.meshes[0]
      
      // 如果有多个mesh，合并它们
      if (importResult.meshes.length > 1) {
        model = BABYLON.Mesh.MergeMeshes(importResult.meshes, true, true, undefined, false, true)
      }
      
      if (model) {
        // 应用Babylon.js优化
        optimizeBabylonModel(model)
        
        // 调整相机位置
        adjustCameraToModel(model)
        
        console.log('STL模型加载完成:', model)
      }
    }
    
    loadingProgress.value = 100
    loadingStatus.value = '加载完成'
    
    setTimeout(() => {
      isLoading.value = false
      ElMessage.success('Babylon.js模型加载完成！')
    }, 500)
    
  } catch (error) {
    console.error('Babylon.js加载STL失败:', error)
    isLoading.value = false
    ElMessage.error('模型加载失败: ' + error.message)
  }
}

// 优化Babylon.js模型
const optimizeBabylonModel = (mesh) => {
  console.log('正在应用Babylon.js优化...')
  
  if (!mesh) return
  
  // 创建优化材质
  const material = new StandardMaterial('optimizedMaterial', scene)
  material.diffuseColor = new Color3(0.29, 0.565, 0.886)
  material.specularColor = new Color3(0.1, 0.1, 0.1)
  material.emissiveColor = new Color3(0.05, 0.1, 0.2)
  
  // 性能优化设置
  material.freeze()
  mesh.material = material
  
  // 优化几何体
  if (mesh.geometry) {
    // 计算法向量
    if (!mesh.getVerticesData(VertexBuffer.NormalKind)) {
      VertexData.ComputeNormals(
        mesh.getVerticesData(VertexBuffer.PositionKind),
        mesh.getIndices(),
        mesh.getVerticesData(VertexBuffer.NormalKind)
      )
    }
  }
  
  // 启用实例化（如果适用）
  if (qualitySettings[qualityLevel.value].lod) {
    // LOD设置
    mesh.addLODLevel(50, null)
  }
  
  // 优化几何体
  mesh.optimizeIndices()
  mesh.freezeWorldMatrix()
  
  console.log('Babylon.js模型优化完成')
}

// 调整相机位置
const adjustCameraToModel = (mesh) => {
  if (!mesh || !camera) return
  
  try {
    const boundingInfo = mesh.getBoundingInfo()
    const boundingBox = boundingInfo.boundingBox
    const size = boundingBox.maximumWorld.subtract(boundingBox.minimumWorld)
    const maxSize = Math.max(size.x, size.y, size.z)
    
    camera.setTarget(boundingInfo.boundingBox.centerWorld)
    camera.radius = maxSize * 2.5
    camera.alpha = -Math.PI / 4
    camera.beta = Math.PI / 3
    
    console.log('相机位置已调整')
  } catch (error) {
    console.warn('调整相机位置时出错:', error)
  }
}

// 质量调整
const adjustQuality = () => {
  if (!engine || !scene) return
  
  const settings = qualitySettings[qualityLevel.value]
  
  // 调整渲染比例
  engine.setHardwareScalingLevel(1 / settings.renderScale)
  
  ElMessage.info(`已切换到${qualityLevel.value === 'high' ? '高' : qualityLevel.value === 'medium' ? '中' : '低'}质量模式`)
}

// 控制方法
const resetCamera = () => {
  if (camera && model) {
    adjustCameraToModel(model)
  } else if (camera) {
    camera.setTarget(BABYLON.Vector3.Zero())
    camera.radius = 10
    camera.alpha = -Math.PI / 2
    camera.beta = Math.PI / 2.5
  }
}

const toggleWireframe = () => {
  if (model && model.material) {
    model.material.wireframe = !model.material.wireframe
  }
}

const toggleInspector = async () => {
  if (!inspector && props.enableInspector) {
    try {
      await import('@babylonjs/inspector')
      inspector = scene.debugLayer
      await inspector.show({
        enableClose: true,
        enablePopup: false,
        embedMode: true
      })
    } catch (error) {
      console.warn('Inspector不可用:', error)
      ElMessage.warning('调试器不可用')
    }
  } else if (inspector) {
    inspector.hide()
  }
}

const toggleStats = () => {
  showPerformanceStats.value = !showPerformanceStats.value
}

// 获取FPS颜色类
const getFPSClass = (fps) => {
  if (fps >= 50) return 'fps-good'
  if (fps >= 30) return 'fps-medium'
  return 'fps-poor'
}

// 处理窗口大小变化
const handleResize = () => {
  if (engine) {
    engine.resize()
  }
}

// 监听URL变化
watch(() => props.modelUrl, () => {
  if (props.modelUrl && scene) {
    loadBabylonModel()
  }
})

// 生命周期
onMounted(async () => {
  console.log('正在启动Babylon.js渲染器...')
  
  try {
    await initBabylonEngine()
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('Babylon.js初始化失败:', error)
    ElMessage.error('Babylon.js引擎启动失败')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  
  // 清理Babylon.js资源
  if (scene) {
    scene.dispose()
  }
  
  if (engine) {
    engine.dispose()
  }
  
  console.log('Babylon.js资源已清理')
})
</script>

<style scoped>
.full-babylon-model-3d-viewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.viewer-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.viewer-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.viewer-controls .el-button-group {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.quality-controls {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.performance-stats {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-size: 12px;
  min-width: 150px;
  z-index: 10;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stat-label {
  color: #ccc;
}

.stat-value {
  font-weight: bold;
}

.engine-name {
  color: #f39c12;
}

.fps-good {
  color: #4CAF50;
}

.fps-medium {
  color: #FF9800;
}

.fps-poor {
  color: #F44336;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 320px;
}

.loading-text {
  margin-top: 15px;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.loading-tip {
  margin-top: 8px;
  color: #f39c12;
  font-size: 13px;
  font-weight: bold;
}

.loading-icon {
  margin-bottom: 20px;
  font-size: 32px;
  color: #f39c12;
}

.loading-icon i {
  animation: babylonRotate 2s linear infinite;
}

@keyframes babylonRotate {
  from {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  to {
    transform: rotate(360deg) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viewer-controls {
    top: 5px;
    left: 5px;
  }
  
  .performance-stats {
    top: 5px;
    right: 5px;
    padding: 8px;
    min-width: 120px;
  }
  
  .loading-content {
    margin: 20px;
    padding: 20px;
    min-width: auto;
  }
}
</style>
