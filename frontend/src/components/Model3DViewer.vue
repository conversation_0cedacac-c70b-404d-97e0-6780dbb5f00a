<template>
  <div class="model-3d-viewer">
    <div ref="container" class="viewer-container">
      <!-- 加载进度显示 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-icon">
            <i class="el-icon-loading"></i>
          </div>
          <el-progress 
            :percentage="loadingProgress" 
            :show-text="false"
            :stroke-width="8"
            color="#409EFF"
          />
          <p class="loading-text">{{ loadingStatus }}</p>
          <p class="loading-tip">大文件可能需要较长时间，请耐心等待</p>
        </div>
      </div>
    </div>
    <div class="viewer-controls">
      <el-button-group>
        <el-button size="small" @click="resetView">重置视图</el-button>
        <el-button size="small" @click="toggleWireframe">线框模式</el-button>
        <el-button size="small" @click="toggleGrid">网格</el-button>
        <el-button size="small" @click="toggleAxes">坐标轴</el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as THREE from 'three'
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelUrl: {
    type: String,
    required: true
  },
  modelType: {
    type: String,
    default: 'stl' // stl, obj, etc.
  }
})

// 响应式数据
const container = ref(null)
const loadingProgress = ref(0)
const loadingStatus = ref('')
const isLoading = ref(false)
let scene, camera, renderer, controls
let model = null
let wireframe = false
let showGrid = true
let showAxes = true
let resizeObserver = null

// 初始化Three.js场景
const initScene = () => {
  if (!container.value) {
    console.warn('Container not ready, retrying...')
    setTimeout(initScene, 100)
    return
  }

  // 检查容器尺寸
  const width = container.value.clientWidth
  const height = container.value.clientHeight
  
  if (width === 0 || height === 0) {
    console.warn('Container has zero dimensions, retrying...', { width, height })
    setTimeout(initScene, 100)
    return
  }

  // 检查是否已经初始化
  if (scene && renderer) {
    return
  }

  // 初始化3D场景

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xffffff) // 白色背景

  // 创建相机
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.set(0, 0, 5)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ 
    antialias: false, // 关闭抗锯齿以提高性能
    powerPreference: "high-performance" // 优先使用高性能GPU
  })
  renderer.setSize(width, height)
  renderer.shadowMap.enabled = false // 暂时关闭阴影以提高性能
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  renderer.setClearColor(0xffffff, 1) // 设置清除颜色为白色
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)) // 限制像素比以提高性能
  container.value.appendChild(renderer.domElement)

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.8) // 更强的环境光
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0) // 更强的方向光
  directionalLight.position.set(10, 10, 5)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 添加额外的光源以确保模型充分照明
  const pointLight = new THREE.PointLight(0xffffff, 0.5, 100)
  pointLight.position.set(-10, -10, -5)
  scene.add(pointLight)

  // 添加网格
  const gridHelper = new THREE.GridHelper(10, 10)
  gridHelper.name = 'grid'
  scene.add(gridHelper)

  // 添加坐标轴
  const axesHelper = new THREE.AxesHelper(5)
  axesHelper.name = 'axes'
  scene.add(axesHelper)

  // 添加控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05

  // 开始渲染循环
  animate()
  
  // 3D场景初始化完成
}

// 渲染循环
const animate = () => {
  requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

// 加载3D模型
const loadModel = async () => {
  if (!props.modelUrl) {
    console.warn('No model URL provided')
    showError('未提供模型URL')
    return
  }

  // 开始加载3D模型
  
  // 验证URL格式
  try {
    new URL(props.modelUrl)
  } catch (e) {
    console.error('URL格式错误:', e)
    showError('模型URL格式错误: ' + e.message)
    return
  }
  
  // 重置加载状态
  isLoading.value = true
  loadingProgress.value = 0
  loadingStatus.value = '正在加载3D模型...'

  try {
    // 尝试预检URL可访问性（失败时不阻断加载，避免HEAD被服务端禁用导致无法显示）
    try {
      // 检查模型URL可访问性（不阻断加载）
      const response = await fetch(props.modelUrl, { 
        method: 'HEAD',
        mode: 'cors'
      })
      if (!response.ok) {
        console.warn('URL预检失败，但继续尝试加载:', response.status, response.statusText)
      }
    } catch (e) {
      console.warn('URL预检失败，继续尝试直接加载:', e?.message || e)
    }
    
    let geometry

    if (props.modelType.toLowerCase() === 'stl') {
      // 加载STL模型
      loadingStatus.value = '正在加载STL文件...'
      const loader = new STLLoader()
      
      // 添加性能优化选项
      const startTime = performance.now()
      
      geometry = await new Promise((resolve, reject) => {
        loader.load(
          props.modelUrl,
          (geometry) => {
            const loadTime = performance.now() - startTime
            
            // 检查几何体是否有效
            if (!geometry.attributes.position || geometry.attributes.position.count === 0) {
              reject(new Error('STL文件中没有有效的几何数据'))
              return
            }
            
            loadingStatus.value = 'STL文件加载完成，正在处理几何体...'
            loadingProgress.value = 80
            resolve(geometry)
          },
          (progress) => {
            if (progress.lengthComputable) {
              const percent = Math.round((progress.loaded / progress.total) * 100)
              loadingProgress.value = Math.min(percent, 75) // 预留25%给后处理
              loadingStatus.value = `正在加载STL文件... ${percent}%`
            } else {
              // 如果无法计算进度，显示一个动态进度
              loadingProgress.value = Math.min(loadingProgress.value + 1, 70)
            }
          },
          (error) => {
            console.error('STL loading error:', error)
            loadingStatus.value = '加载失败: ' + error.message
            reject(error)
          }
        )
      })
    } else if (props.modelType.toLowerCase() === 'obj') {
      // 加载OBJ模型
      const loader = new OBJLoader()
      const object = await new Promise((resolve, reject) => {
        loader.load(
          props.modelUrl,
          (object) => {
            resolve(object)
          },
          (progress) => {},
          (error) => {
            console.error('OBJ loading error:', error)
            reject(error)
          }
        )
      })
      // 对于OBJ文件，我们需要提取几何体
      geometry = object.children[0]?.geometry || new THREE.BufferGeometry()
    } else {
      console.warn('Unsupported model type:', props.modelType)
      return
    }

    // 移除现有模型
    if (model) {
      scene.remove(model)
    }

    // 创建材质与网格
    loadingStatus.value = '正在创建3D模型...'
    loadingProgress.value = 85

    // 优化几何体
    // 优化几何体
    geometry.computeBoundingBox()
    geometry.computeBoundingSphere()
    
    // 如果顶点数过多，进行简化
    const vertexCount = geometry.attributes.position ? geometry.attributes.position.count : 0
    
    if (vertexCount > 100000) {
      // 可以在这里添加几何体简化逻辑
    }

    // 创建材质
    const material = new THREE.MeshPhongMaterial({
      color: 0x4a90e2, // 更亮的蓝色
      emissive: 0x1a4a7a, // 更亮的发光色
      side: THREE.DoubleSide, // 双面渲染，避免法线方向导致的不可见
      flatShading: false, // 使用平滑着色
      transparent: false,
      opacity: 1.0
    })

    // 创建网格
    model = new THREE.Mesh(geometry, material)
    model.castShadow = false // 暂时关闭阴影以提高性能
    model.receiveShadow = false

    // 模型创建完成
    
    loadingStatus.value = '正在调整模型位置...'
    loadingProgress.value = 90

    // 居中模型
    const box = new THREE.Box3().setFromObject(model)
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())
    
    // 原始边界框已计算
    
    // 检查模型是否有有效尺寸
    if (size.length() === 0) {
      throw new Error('模型没有有效的几何尺寸，可能是空的或损坏的文件')
    }
    
    model.position.sub(center)

    // 模型已居中
    loadingStatus.value = '正在调整相机位置...'
    loadingProgress.value = 95

    // 调整相机位置与裁剪面以适应模型（避免小模型被近裁剪面裁掉）
    const maxDim = Math.max(size.x, size.y, size.z)
    const fov = camera.fov * (Math.PI / 180)
    // 动态更新裁剪面
    camera.near = Math.max(0.001, maxDim / 1000)
    camera.far = Math.max(1000, maxDim * 20)
    camera.updateProjectionMatrix()

    let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2))
    cameraZ *= 2.5 // 增加边距，确保模型完全可见
    // 确保相机距离大于近裁剪面
    cameraZ = Math.max(cameraZ, camera.near * 5)
    camera.position.set(cameraZ, cameraZ, cameraZ)
    camera.lookAt(0, 0, 0)
    
    // 更新控制器目标
    if (controls) {
      controls.target.set(0, 0, 0)
      controls.update()
    }

    // 相机位置已设置
    loadingStatus.value = '正在添加到场景...'
    loadingProgress.value = 98

    scene.add(model)
    // 模型已加入场景
    
    // 输出调试信息
    // 移除大段调试日志
    
    // 等待一帧渲染完成后再隐藏加载界面
    loadingStatus.value = '正在渲染模型...'
    loadingProgress.value = 99
    
    // 使用requestAnimationFrame确保渲染完成
    requestAnimationFrame(() => {
      // 再等待一帧确保模型完全渲染
      requestAnimationFrame(() => {
        // 加载完成
        isLoading.value = false
        loadingProgress.value = 100
        loadingStatus.value = '加载完成'
        
        // 强制重新渲染
        if (renderer && scene && camera) {
          renderer.render(scene, camera)
        }
        
        // 显示加载完成提示
        ElMessage.success('3D模型加载完成！')
      })
    })

  } catch (error) {
    console.error('Error loading model:', error)
    isLoading.value = false
    loadingStatus.value = '加载失败: ' + error.message
    
    showError(error.message || '未知错误')
  }
}

// 控制方法
const resetView = () => {
  if (controls) {
    controls.reset()
  }
}

const toggleWireframe = () => {
  if (model) {
    wireframe = !wireframe
    model.material.wireframe = wireframe
  }
}

const toggleGrid = () => {
  showGrid = !showGrid
  const grid = scene.getObjectByName('grid')
  if (grid) {
    grid.visible = showGrid
  }
}

const toggleAxes = () => {
  showAxes = !showAxes
  const axes = scene.getObjectByName('axes')
  if (axes) {
    axes.visible = showAxes
  }
}

// 防抖函数
let resizeTimeout = null

// 处理窗口大小变化
const handleResize = () => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
  
  resizeTimeout = setTimeout(() => {
    if (!container.value || !camera || !renderer) return

    const width = container.value.clientWidth
    const height = container.value.clientHeight

    if (width > 0 && height > 0) {
      camera.aspect = width / height
      camera.updateProjectionMatrix()
      renderer.setSize(width, height)
    }
  }, 100) // 100ms防抖
}



// 重试加载
const retryLoad = () => {
  loadModel()
}

// 检查URL可访问性
const checkUrlAccessibility = async (url) => {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    return true
  } catch (error) {
    console.error('URL不可访问:', error)
    throw new Error(`无法访问模型文件: ${error.message}`)
  }
}

// 显示错误信息
const showError = (message) => {
  console.error('3D模型加载错误:', message)
  
  // 显示错误信息给用户
  if (container.value) {
    container.value.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #f56c6c; padding: 20px;">
        <div style="text-align: center; max-width: 500px;">
          <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
          <h3 style="margin-bottom: 16px;">3D模型加载失败</h3>
          <p style="margin-bottom: 16px;">错误信息: ${message}</p>
          <div style="background: #fdf6ec; border: 1px solid #f5dab1; border-radius: 4px; padding: 16px; margin: 16px 0; text-align: left;">
            <h4 style="margin: 0 0 8px 0; color: #e6a23c;">调试信息:</h4>
            <ul style="margin: 0; padding-left: 20px; line-height: 1.6; font-size: 12px;">
              <li>模型URL: ${props.modelUrl}</li>
              <li>模型类型: ${props.modelType}</li>
              <li>容器尺寸: ${container.value ? `${container.value.clientWidth}x${container.value.clientHeight}` : 'N/A'}</li>
              <li>浏览器: ${navigator.userAgent}</li>
            </ul>
            <h4 style="margin: 16px 0 8px 0; color: #e6a23c;">可能的解决方案:</h4>
            <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
              <li>检查网络连接是否正常</li>
              <li>确认文件格式是否正确(支持STL、OBJ等)</li>
              <li>如果文件较大，请耐心等待加载</li>
              <li>尝试刷新页面重新加载</li>
              <li>检查浏览器控制台是否有详细错误信息</li>
            </ul>
          </div>
          <button 
            onclick="window.location.reload()" 
            style="background: #409EFF; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px;"
          >
            刷新页面
          </button>
          <button 
            onclick="retryLoad()" 
            style="background: #67C23A; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;"
          >
            重试加载
          </button>
        </div>
      </div>
    `
  }
  
  ElMessage.error('3D模型加载失败: ' + message)
}

// 监听模型URL变化
watch(() => props.modelUrl, (newUrl, oldUrl) => {
  if (newUrl && newUrl !== oldUrl) {
    // model url changed, reload
    loadModel()
  }
})

// 生命周期
onMounted(() => {
  // 延迟初始化，确保容器尺寸已计算
  nextTick(() => {
    initScene()
    if (props.modelUrl) {
      loadModel()
    }
  })
  
  window.addEventListener('resize', handleResize)
  
  // 观察容器尺寸变化，解决弹窗初次打开时容器为0的问题
  if ('ResizeObserver' in window) {
    resizeObserver = new ResizeObserver(() => {
      handleResize()
    })
    if (container.value) resizeObserver.observe(container.value)
  }
  
  // 减少重试次数，避免过度刷新
  const resizeAttempts = [0, 200, 500]
  resizeAttempts.forEach(delay => {
    setTimeout(() => {
      handleResize()
    }, delay)
  })
})

onUnmounted(() => {
  
  window.removeEventListener('resize', handleResize)
  
  // 清理防抖定时器
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
  
  // 清理渲染器
  if (renderer && container.value) {
    try {
      container.value.removeChild(renderer.domElement)
    } catch (e) {}
  }
  
  if (renderer) {
    renderer.dispose()
  }
  
  // 清理ResizeObserver
  if (resizeObserver && container.value) {
    try { 
      resizeObserver.unobserve(container.value) 
      resizeObserver.disconnect()
    } catch (e) {}
  }
  
  // 清理场景对象
  if (scene) {
    scene.clear()
  }
  
  // 重置变量
  scene = null
  camera = null
  renderer = null
  controls = null
  model = null
})
</script>

<style scoped>
.model-3d-viewer {
  width: 100%;
  height: 100%;
  position: relative;
  min-height: 500px; /* 确保有最小高度 */
}

.viewer-container {
  width: 100%;
  height: 100%;
  min-height: 500px; /* 确保有最小高度 */
  background: #f0f0f0;
  position: relative;
}

.viewer-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
}

.viewer-controls .el-button-group {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 5px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 300px;
}

.loading-text {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.loading-tip {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.loading-icon {
  margin-bottom: 15px;
  font-size: 24px;
  color: #409EFF;
}

.loading-icon i {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
