import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/views/Layout.vue'
import Login from '@/views/Login.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      component: Layout,
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue')
        },
        {
          path: 'materials',
          name: 'Materials',
          redirect: '/materials/categories',
          children: [
            {
              path: 'categories',
              name: 'CategoryManagement',
              component: () => import('@/views/materials/CategoryManagement.vue')
            },
            {
              path: 'materials',
              name: 'MaterialManagement',
              component: () => import('@/views/materials/MaterialManagement.vue')
            },
            {
              path: 'preferred',
              name: 'PreferredMaterials',
              component: () => import('@/views/materials/PreferredMaterials.vue')
            }
          ]
        },
        {
          path: 'bom',
          name: 'BOM',
          redirect: '/bom/management',
          children: [
            {
              path: 'management',
              name: 'BOMManagement',
              component: () => import('@/views/bom/BOMManagement.vue')
            },
            {
              path: 'create',
              name: 'BOMCreate',
              component: () => import('@/views/bom/BOMCreate.vue')
            },
            {
              path: 'view/:id',
              name: 'BOMView',
              component: () => import('@/views/bom/BOMView.vue')
            },
            {
              path: 'edit/:id',
              name: 'BOMEdit',
              component: () => import('@/views/bom/BOMEdit.vue')
            }
          ]
        },
        {
          path: 'inventory',
          name: 'Inventory',
          redirect: '/inventory/management',
          children: [
            {
              path: 'management',
              name: 'InventoryManagement',
              component: () => import('@/views/inventory/InventoryManagement.vue')
            },
            {
              path: 'warehouse',
              name: 'WarehouseManagement',
              component: () => import('@/views/inventory/WarehouseManagement.vue')
            },
            {
              path: 'alerts',
              name: 'AlertManagement',
              component: () => import('@/views/inventory/AlertManagement.vue')
            }
          ]
        },
        {
          path: 'procurement',
          name: 'Procurement',
          redirect: '/procurement/requests',
          children: [
            {
              path: 'requests',
              name: 'RequestManagement',
              component: () => import('@/views/procurement/RequestManagement.vue')
            },
            {
              path: 'records',
              name: 'RequestRecords',
              component: () => import('@/views/procurement/RequestRecords.vue')
            },
            {
              path: 'suppliers',
              name: 'ProcurementSupplierManagement',
              component: () => import('@/views/procurement/SupplierManagement.vue')
            }
          ]
        },
        {
          path: 'users',
          name: 'Users',
          redirect: '/users/management',
          children: [
            {
              path: 'management',
              name: 'UserManagement',
              component: () => import('@/views/users/UserManagement.vue')
            },
            {
              path: 'departments',
              name: 'DepartmentManagement',
              component: () => import('@/views/users/DepartmentManagement.vue')
            }
          ]
        },

      ]
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)
  
  if (requiresAuth && !token) {
    // 需要认证但没有token，跳转到登录页
    next('/login')
  } else if (to.path === '/login' && token) {
    // 已登录用户访问登录页，跳转到首页
    next('/')
  } else {
    next()
  }
})

export default router
