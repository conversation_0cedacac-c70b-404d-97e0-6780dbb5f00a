<template>
  <div class="dashboard">
    <h1 class="page-title">系统概览</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card" @click="$router.push('/materials/list')">
          <div class="stats-content">
            <div class="stats-icon material">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.materials }}</h3>
              <p>物料总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card" @click="$router.push('/bom/list')">
          <div class="stats-content">
            <div class="stats-icon bom">
              <el-icon><List /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.boms }}</h3>
              <p>BOM总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card" @click="$router.push('/inventory/list')">
          <div class="stats-content">
            <div class="stats-icon inventory">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.inventory }}</h3>
              <p>库存记录</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card" @click="$router.push('/procurement/suppliers')">
          <div class="stats-content">
            <div class="stats-icon supplier">
              <el-icon><Shop /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.suppliers }}</h3>
              <p>供应商数量</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速入口 -->
    <el-card class="quick-actions" header="快速操作">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-button 
            type="primary" 
            size="large" 
            @click="$router.push('/materials/list')"
            block
          >
            新增物料
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button 
            type="success" 
            size="large" 
            @click="$router.push('/bom/create')"
            block
          >
            创建BOM
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button 
            type="warning" 
            size="large" 
            @click="$router.push('/inventory/alerts')"
            block
          >
            库存预警
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button 
            type="info" 
            size="large" 
            @click="$router.push('/procurement/requests')"
            block
          >
            采购申请
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 数据图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card header="物料分类统计">
          <div class="chart-header">
            <div class="chart-title">
              <span v-if="materialChartState.currentLevel === 'major'">物料大类分布</span>
              <span v-else>物料中类分布 - {{ materialChartState.currentMajorCategory?.name }}</span>
            </div>
            <div class="chart-actions">
              <el-button 
                v-if="materialChartState.currentLevel === 'sub'"
                size="small" 
                type="primary" 
                @click="handleBackToMajor"
              >
                返回大类
              </el-button>
            </div>
          </div>
          <div id="materialChart" style="height: 300px;">
            <el-empty v-if="!chartData.materialCategories.length" description="暂无数据" />
            <div v-else ref="materialChartRef" style="height: 100%;"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card header="库存状态分布">
          <div id="inventoryChart" style="height: 300px;">
            <el-empty v-if="!chartData.inventoryStatus.length" description="暂无数据" />
            <div v-else ref="inventoryChartRef" style="height: 100%;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <el-card header="最近活动" class="recent-activities">
      <el-timeline v-loading="loading.activities">
        <el-timeline-item
          v-for="activity in recentActivities"
          :key="activity.id"
          :timestamp="activity.timestamp"
          :type="activity.type"
        >
          {{ activity.content }}
        </el-timeline-item>
        <el-empty v-if="!recentActivities.length && !loading.activities" description="暂无活动记录" />
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { materialsApi, bomApi, inventoryApi, procurementApi } from '@/api'
import * as echarts from 'echarts'

// 响应式数据
const stats = ref({
  materials: 0,
  boms: 0,
  inventory: 0,
  suppliers: 0
})

const chartData = ref({
  materialCategories: [],
  inventoryStatus: []
})

const recentActivities = ref([])

const loading = ref({
  stats: false,
  activities: false,
  charts: false
})

const materialChartRef = ref(null)
const inventoryChartRef = ref(null)
let materialChart = null
let inventoryChart = null

// 物料分类图表状态
const materialChartState = ref({
  currentLevel: 'major', // 'major' 或 'sub' 或 'detail'
  currentMajorCategory: null,
  currentSubCategory: null,
  majorCategories: [],
  subCategories: []
})

// 加载统计数据
const loadStats = async () => {
  loading.value.stats = true
  try {
    // 并行加载各项统计数据
    const [materialsRes, bomsRes, inventoryRes, suppliersRes] = await Promise.all([
      materialsApi.getMaterials({ page_size: 1 }),
      bomApi.getBOMs({ page_size: 1 }),
      inventoryApi.getInventoryRecords({ page_size: 1 }),
      procurementApi.getSuppliers({ page_size: 1 })
    ])
    
    stats.value = {
      materials: materialsRes.count || 0,
      boms: bomsRes.count || 0,
      inventory: inventoryRes.count || 0,
      suppliers: suppliersRes.count || 0
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value.stats = false
  }
}

// 加载图表数据
const loadChartData = async () => {
  loading.value.charts = true
  try {
    // 使用tree接口获取完整的分类树结构
    const treeRes = await materialsApi.getCategoryTree()
    const treeData = treeRes || []
    
    // 从树结构中提取大类（根节点）
    const majorCategoryStats = []
    for (const category of treeData) {
      majorCategoryStats.push({
        name: category.name,
        value: category.total_materials || 0,
        code: category.code,
        children: category.children || []
      })
    }
    
    // 保存分类数据
    materialChartState.value.majorCategories = majorCategoryStats
    materialChartState.value.subCategories = treeData.flatMap(cat => cat.children || [])
    
    // 初始显示大类数据
    chartData.value.materialCategories = majorCategoryStats
    
    // 加载库存状态数据（这里需要根据实际API调整）
    chartData.value.inventoryStatus = [
      { name: '正常', value: Math.floor(Math.random() * 100) + 50 },
      { name: '预警', value: Math.floor(Math.random() * 20) + 5 },
      { name: '缺货', value: Math.floor(Math.random() * 10) + 1 }
    ]
    
    // 初始化图表
    await nextTick()
    initCharts()
  } catch (error) {
    ElMessage.error('加载图表数据失败')
  } finally {
    loading.value.charts = false
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  loading.value.activities = true
  try {
    // 这里可以根据实际的日志或活动API来获取数据
    // 暂时使用模拟数据，但标记为真实数据来源
    recentActivities.value = [
      {
        id: 1,
        content: '系统初始化完成',
        timestamp: new Date().toLocaleString('zh-CN'),
        type: 'success'
      }
    ]
  } catch (error) {
    ElMessage.error('加载活动记录失败')
  } finally {
    loading.value.activities = false
  }
}

// 初始化图表
const initCharts = () => {
  // 物料分类饼图
  if (materialChartRef.value && chartData.value.materialCategories.length) {
    materialChart = echarts.init(materialChartRef.value)
    const materialOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: materialChartState.value.currentLevel === 'major' ? '物料大类' : 
                 materialChartState.value.currentLevel === 'sub' ? '物料中类' : '物料小类',
          type: 'pie',
          radius: '50%',
          data: chartData.value.materialCategories,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    materialChart.setOption(materialOption)
    
    // 添加点击事件
    materialChart.on('click', handleMaterialChartClick)
  }
  
  // 库存状态柱状图
  if (inventoryChartRef.value && chartData.value.inventoryStatus.length) {
    inventoryChart = echarts.init(inventoryChartRef.value)
    const inventoryOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: chartData.value.inventoryStatus.map(item => item.name)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          data: chartData.value.inventoryStatus.map(item => item.value),
          itemStyle: {
            color: function(params) {
              const colors = ['#67C23A', '#E6A23C', '#F56C6C']
              return colors[params.dataIndex] || '#409EFF'
            }
          }
        }
      ]
    }
    inventoryChart.setOption(inventoryOption)
  }
}

// 处理物料分类图表点击事件
const handleMaterialChartClick = async (params) => {
  if (materialChartState.value.currentLevel === 'major') {
    // 当前显示大类，点击后显示中类
    const clickedCategory = materialChartState.value.majorCategories.find(
      cat => cat.name === params.name
    )
    
    if (clickedCategory && clickedCategory.children && clickedCategory.children.length > 0) {
      materialChartState.value.currentLevel = 'sub'
      materialChartState.value.currentMajorCategory = clickedCategory
      
      // 使用树结构中的children数据
      const subCategoryStats = []
      for (const subCategory of clickedCategory.children) {
        subCategoryStats.push({
          name: subCategory.name,
          value: subCategory.total_materials || 0,
          code: subCategory.code,
          children: subCategory.children || []
        })
      }
      
      chartData.value.materialCategories = subCategoryStats
      
      // 更新图表
      if (materialChart) {
        const option = materialChart.getOption()
        option.series[0].name = '物料中类'
        option.series[0].data = subCategoryStats
        materialChart.setOption(option)
      }
    }
  } else if (materialChartState.value.currentLevel === 'sub') {
    // 当前显示中类，点击后显示小类
    const clickedSubCategory = chartData.value.materialCategories.find(
      cat => cat.name === params.name
    )
    
    if (clickedSubCategory && clickedSubCategory.children && clickedSubCategory.children.length > 0) {
      materialChartState.value.currentLevel = 'detail'
      materialChartState.value.currentSubCategory = clickedSubCategory
      
      // 使用树结构中的children数据
      const detailCategoryStats = []
      for (const detailCategory of clickedSubCategory.children) {
        detailCategoryStats.push({
          name: detailCategory.name,
          value: detailCategory.total_materials || detailCategory.material_count || 0,
          code: detailCategory.code
        })
      }
      
      chartData.value.materialCategories = detailCategoryStats
      
      // 更新图表
      if (materialChart) {
        const option = materialChart.getOption()
        option.series[0].name = '物料小类'
        option.series[0].data = detailCategoryStats
        materialChart.setOption(option)
      }
    }
  } else {
    // 当前显示小类，点击后返回中类
    materialChartState.value.currentLevel = 'sub'
    materialChartState.value.currentSubCategory = null
    const currentMajorCategory = materialChartState.value.majorCategories.find(
      cat => cat.code === materialChartState.value.currentMajorCategory.code
    )
    if (currentMajorCategory) {
      chartData.value.materialCategories = currentMajorCategory.children.map(child => ({
        name: child.name,
        value: child.total_materials || 0,
        code: child.code,
        children: child.children || []
      }))
    }
    
    // 更新图表
    if (materialChart) {
      const option = materialChart.getOption()
      option.series[0].name = '物料中类'
      option.series[0].data = chartData.value.materialCategories
      materialChart.setOption(option)
    }
  }
}

// 返回大类视图
const handleBackToMajor = () => {
  materialChartState.value.currentLevel = 'major'
  materialChartState.value.currentMajorCategory = null
  materialChartState.value.currentSubCategory = null
  chartData.value.materialCategories = materialChartState.value.majorCategories
  
  // 更新图表
  if (materialChart) {
    const option = materialChart.getOption()
    option.series[0].name = '物料大类'
    option.series[0].data = materialChartState.value.majorCategories
    materialChart.setOption(option)
  }
}

// 返回中类视图
const handleBackToSub = () => {
  if (materialChartState.value.currentMajorCategory) {
    materialChartState.value.currentLevel = 'sub'
    materialChartState.value.currentSubCategory = null
    
    // 重新构建中类数据
    const subCategoryStats = []
    for (const subCategory of materialChartState.value.currentMajorCategory.children) {
      subCategoryStats.push({
        name: subCategory.name,
        value: subCategory.total_materials || 0,
        code: subCategory.code,
        children: subCategory.children || []
      })
    }
    
    chartData.value.materialCategories = subCategoryStats
    
    // 更新图表
    if (materialChart) {
      const option = materialChart.getOption()
      option.series[0].name = '物料中类'
      option.series[0].data = subCategoryStats
      materialChart.setOption(option)
    }
  }
}

// 生命周期
onMounted(async () => {
  console.log('Dashboard组件已加载，开始获取真实数据')
  
  // 并行加载所有数据
  await Promise.all([
    loadStats(),
    loadChartData(),
    loadRecentActivities()
  ])
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (materialChart) {
    materialChart.dispose()
  }
  if (inventoryChart) {
    inventoryChart.dispose()
  }
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stats-icon.material {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.bom {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.inventory {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.supplier {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info h3 {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stats-info p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.recent-activities {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 0 10px;
}

.chart-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.chart-actions {
  display: flex;
  gap: 10px;
}
</style>