<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
  >
    <el-form-item label="仓库" prop="warehouse_code">
      <el-select
        v-model="form.warehouse_code"
        placeholder="选择仓库"
        style="width: 100%"
      >
        <el-option
          v-for="warehouse in warehouses"
          :key="warehouse.code"
          :label="`${warehouse.code} - ${warehouse.name}`"
          :value="warehouse.code"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="物料" prop="material_code">
      <el-select
        v-model="form.material_code"
        placeholder="选择物料"
        filterable
        remote
        :remote-method="searchMaterials"
        :loading="materialsLoading"
        style="width: 100%"
        @change="onMaterialChange"
      >
        <el-option
          v-for="material in materialOptions"
          :key="material.code"
          :label="`${material.code} - ${material.name}`"
          :value="material.code"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="批次号">
      <el-input
        v-model="form.batch_no"
        placeholder="可选，留空表示无批次"
      />
    </el-form-item>
    
    <el-form-item label="数量" prop="quantity">
      <el-input-number
        v-model="form.quantity"
        :min="0.000001"
        :precision="6"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item v-if="operation === 'IN'" label="单价">
      <el-input-number
        v-model="form.unit_cost"
        :min="0"
        :precision="4"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item label="参考单号">
      <el-input
        v-model="form.reference_no"
        placeholder="采购单号、工单号等"
      />
    </el-form-item>
    
    <el-form-item label="操作人" prop="operator">
      <el-input v-model="form.operator" />
    </el-form-item>
    
    <el-form-item label="备注">
      <el-input
        v-model="form.notes"
        type="textarea"
        :rows="3"
        placeholder="操作备注"
      />
    </el-form-item>
    
    <!-- 显示当前库存信息（仅出库时） -->
    <el-form-item v-if="operation === 'OUT' && currentStock" label="当前库存">
      <div class="stock-info">
        <div>总库存: {{ formatNumber(currentStock.quantity) }}</div>
        <div>可用库存: {{ formatNumber(currentStock.available_quantity) }}</div>
        <div>锁定库存: {{ formatNumber(currentStock.locked_quantity) }}</div>
      </div>
    </el-form-item>
    
    <div class="form-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button 
        type="primary" 
        :loading="submitting" 
        @click="handleSubmit"
      >
        {{ operation === 'IN' ? '入库' : '出库' }}
      </el-button>
    </div>
  </el-form>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { materialsApi, inventoryApi } from '@/api'

const props = defineProps({
  operation: {
    type: String,
    required: true,
    validator: (value) => ['IN', 'OUT'].includes(value)
  },
  warehouses: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const materialsLoading = ref(false)
const materialOptions = ref([])
const currentStock = ref(null)

const form = reactive({
  warehouse_code: '',
  material_code: '',
  batch_no: '',
  quantity: 1,
  unit_cost: null,
  reference_no: '',
  operator: '当前用户',
  notes: ''
})

// 表单验证规则
const rules = {
  warehouse_code: [
    { required: true, message: '请选择仓库', trigger: 'change' }
  ],
  material_code: [
    { required: true, message: '请选择物料', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 0.000001, message: '数量必须大于0', trigger: 'blur' }
  ],
  operator: [
    { required: true, message: '请输入操作人', trigger: 'blur' }
  ]
}

// 监听仓库和物料变化，获取当前库存信息
watch([() => form.warehouse_code, () => form.material_code, () => form.batch_no], 
  async ([warehouseCode, materialCode, batchNo]) => {
    if (props.operation === 'OUT' && warehouseCode && materialCode) {
      try {
        const response = await inventoryApi.getInventoryRecords({
          warehouse_code: warehouseCode,
          material_code: materialCode
        })
        
        const records = response.results || []
        const record = records.find(r => r.batch_no === (batchNo || ''))
        currentStock.value = record || null
      } catch (error) {
        currentStock.value = null
      }
    }
  }
)

// 方法
const searchMaterials = async (query) => {
  if (!query) return
  
  materialsLoading.value = true
  try {
    const response = await materialsApi.getMaterials({ 
      search: query,
      status: 'ACTIVE',
      page_size: 20
    })
    materialOptions.value = response.results || []
  } catch (error) {
    ElMessage.error('搜索物料失败')
  } finally {
    materialsLoading.value = false
  }
}

const onMaterialChange = () => {
  // 清空当前库存信息
  currentStock.value = null
}

const validateForm = async () => {
  try {
    await formRef.value.validate()
    
    // 额外验证
    if (props.operation === 'OUT' && currentStock.value) {
      if (form.quantity > currentStock.value.available_quantity) {
        ElMessage.error(`出库数量不能超过可用库存 ${formatNumber(currentStock.value.available_quantity)}`)
        return false
      }
    }
    
    return true
  } catch (error) {
    return false
  }
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  
  submitting.value = true
  try {
    emit('submit', form)
  } catch (error) {
    // 错误处理由父组件处理
  } finally {
    submitting.value = false
  }
}

const formatNumber = (value) => {
  if (value === null || value === undefined) return '-'
  return Number(value).toLocaleString()
}
</script>

<style scoped>
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.stock-info {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
}

.stock-info div {
  margin-bottom: 5px;
}

.stock-info div:last-child {
  margin-bottom: 0;
}
</style>