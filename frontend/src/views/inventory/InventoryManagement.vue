<template>
  <div class="inventory-management">
    <h1 class="page-title">库存管理</h1>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索物料编码或批次号"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.warehouse_code"
            placeholder="选择仓库"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.code"
              :label="warehouse.name"
              :value="warehouse.code"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-switch
            v-model="searchForm.low_stock_only"
            active-text="仅显示低库存"
            @change="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="success" :icon="Plus" @click="showStockInDialog = true">
            入库
          </el-button>
          <el-button type="warning" :icon="Minus" @click="showStockOutDialog = true">
            出库
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="warehouse_code" label="仓库编码" width="120" />
        <el-table-column prop="warehouse_name" label="仓库名称" width="150" />
        <el-table-column prop="material_code" label="物料编码" width="150" />
        <el-table-column prop="material_name" label="物料名称" min-width="200" />
        <el-table-column prop="batch_no" label="批次号" width="120" />
        <el-table-column prop="quantity" label="总库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.quantity) }}
          </template>
        </el-table-column>
        <el-table-column prop="available_quantity" label="可用库存" width="120">
          <template #default="{ row }">
            <span :class="{ 'low-stock': row.is_low_stock }">
              {{ formatNumber(row.available_quantity) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="locked_quantity" label="锁定库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.locked_quantity) }}
          </template>
        </el-table-column>
        <el-table-column prop="safety_stock" label="安全库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.safety_stock) }}
          </template>
        </el-table-column>
        <el-table-column prop="unit_cost" label="单价" width="120">
          <template #default="{ row }">
            {{ row.unit_cost ? '¥' + formatNumber(row.unit_cost) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_value" label="总价值" width="120">
          <template #default="{ row }">
            {{ row.total_value ? '¥' + formatNumber(row.total_value) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="last_updated" label="最后更新" width="180">
          <template #default="{ row }">
            {{ formatDate(row.last_updated) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewTransactions(row)">
              事务记录
            </el-button>
            <el-button size="small" type="primary" @click="handleAdjust(row)">
              调整
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleSetSafetyStock(row)"
            >
              设置安全库存
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 入库对话框 -->
    <el-dialog
      v-model="showStockInDialog"
      title="库存入库"
      width="50%"
      :before-close="handleCloseStockIn"
    >
      <div class="stock-operation-placeholder">
        <p>库存入库功能开发中...</p>
        <div style="text-align: center; margin-top: 20px;">
          <el-button @click="handleCloseStockIn">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 出库对话框 -->
    <el-dialog
      v-model="showStockOutDialog"
      title="库存出库"
      width="50%"
      :before-close="handleCloseStockOut"
    >
      <div class="stock-operation-placeholder">
        <p>库存出库功能开发中...</p>
        <div style="text-align: center; margin-top: 20px;">
          <el-button @click="handleCloseStockOut">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 设置安全库存对话框 -->
    <el-dialog
      v-model="showSafetyStockDialog"
      title="设置安全库存"
      width="40%"
    >
      <el-form
        ref="safetyStockFormRef"
        :model="safetyStockForm"
        label-width="120px"
      >
        <el-form-item label="仓库">
          <el-text>{{ currentRecord?.warehouse_name }}</el-text>
        </el-form-item>
        <el-form-item label="物料">
          <el-text>{{ currentRecord?.material_name }}</el-text>
        </el-form-item>
        <el-form-item label="当前库存">
          <el-text>{{ formatNumber(currentRecord?.available_quantity) }}</el-text>
        </el-form-item>
        <el-form-item label="安全库存" prop="safety_stock">
          <el-input-number
            v-model="safetyStockForm.safety_stock"
            :min="0"
            :precision="6"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSafetyStockDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSafetyStockSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 事务记录对话框 -->
    <el-dialog
      v-model="showTransactionsDialog"
      title="库存事务记录"
      width="80%"
    >
      <el-table
        v-loading="transactionsLoading"
        :data="transactionsData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="transaction_no" label="事务单号" width="180" />
        <el-table-column prop="transaction_type" label="事务类型" width="100">
          <template #default="{ row }">
            {{ getTransactionTypeText(row.transaction_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.quantity) }}
          </template>
        </el-table-column>
        <el-table-column prop="unit_cost" label="单价" width="120">
          <template #default="{ row }">
            {{ row.unit_cost ? '¥' + formatNumber(row.unit_cost) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="reference_no" label="参考单号" width="150" />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="notes" label="备注" min-width="150" />
        <el-table-column prop="created_at" label="操作时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Plus, Minus } from '@element-plus/icons-vue'
import { inventoryApi } from '@/api'
import dayjs from 'dayjs'
// import StockOperationForm from './components/StockOperationForm.vue'

// 响应式数据
const loading = ref(false)
const transactionsLoading = ref(false)
const tableData = ref([])
const transactionsData = ref([])
const warehouseOptions = ref([])
const showStockInDialog = ref(false)
const showStockOutDialog = ref(false)
const showSafetyStockDialog = ref(false)
const showTransactionsDialog = ref(false)
const safetyStockFormRef = ref()
const currentRecord = ref(null)

const searchForm = reactive({
  search: '',
  warehouse_code: '',
  low_stock_only: false
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const safetyStockForm = reactive({
  safety_stock: 0
})

// 计算属性
const tableDataFiltered = computed(() => {
  if (!searchForm.low_stock_only) {
    return tableData.value
  }
  return tableData.value.filter(item => item.is_low_stock)
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.search || undefined,
      warehouse_code: searchForm.warehouse_code || undefined
    }
    
    const response = await inventoryApi.getInventoryRecords(params)
    tableData.value = response.results || []
    pagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadWarehouses = async () => {
  try {
    const response = await inventoryApi.getWarehouses()
    warehouseOptions.value = response.results || []
  } catch (error) {
    ElMessage.error('加载仓库列表失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    warehouse_code: '',
    low_stock_only: false
  })
  handleSearch()
}

const handleViewTransactions = async (row) => {
  currentRecord.value = row
  transactionsLoading.value = true
  showTransactionsDialog.value = true
  
  try {
    const response = await inventoryApi.getInventoryTransactions({
      warehouse_code: row.warehouse_code,
      material_code: row.material_code
    })
    transactionsData.value = response.results || []
  } catch (error) {
    ElMessage.error('加载事务记录失败')
  } finally {
    transactionsLoading.value = false
  }
}

const handleAdjust = (row) => {
  ElMessage.info('库存调整功能开发中...')
}

const handleSetSafetyStock = (row) => {
  currentRecord.value = row
  safetyStockForm.safety_stock = row.safety_stock
  showSafetyStockDialog.value = true
}

const handleSafetyStockSubmit = async () => {
  try {
    await inventoryApi.updateInventoryRecord(currentRecord.value.id, {
      safety_stock: safetyStockForm.safety_stock
    })
    ElMessage.success('安全库存设置成功')
    showSafetyStockDialog.value = false
    loadData()
  } catch (error) {
    ElMessage.error('设置失败')
  }
}

const handleCloseStockIn = () => {
  showStockInDialog.value = false
}

const handleCloseStockOut = () => {
  showStockOutDialog.value = false
}

// const handleStockInSubmit = async (data) => {
//   try {
//     await inventoryApi.stockIn(data)
//     ElMessage.success('入库成功')
//     showStockInDialog.value = false
//     loadData()
//   } catch (error) {
//     ElMessage.error('入库失败: ' + (error.response?.data?.error || '未知错误'))
//   }
// }

// const handleStockOutSubmit = async (data) => {
//   try {
//     await inventoryApi.stockOut(data)
//     ElMessage.success('出库成功')
//     showStockOutDialog.value = false
//     loadData()
//   } catch (error) {
//     ElMessage.error('出库失败: ' + (error.response?.data?.error || '未知错误'))
//   }
// }

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}

const getRowClassName = ({ row }) => {
  return row.is_low_stock ? 'low-stock-row' : ''
}

const getTransactionTypeText = (type) => {
  const typeMap = {
    IN: '入库',
    OUT: '出库',
    TRANSFER: '调拨',
    ADJUST: '调整',
    COUNT: '盘点'
  }
  return typeMap[type] || '未知'
}

const formatNumber = (value) => {
  if (value === null || value === undefined) return '-'
  return Number(value).toLocaleString()
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadData()
  loadWarehouses()
})
</script>

<style scoped>
.inventory-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.low-stock {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

:deep(.low-stock-row) {
  background-color: #fef0f0;
}

:deep(.low-stock-row:hover > td) {
  background-color: #fde2e2 !important;
}
</style>