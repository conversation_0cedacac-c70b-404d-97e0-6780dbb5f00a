<template>
  <div class="alert-management">
    <h1 class="page-title">库存预警管理</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number low-stock">{{ stats.lowStockCount }}</div>
            <div class="stat-label">低库存物料</div>
          </div>
          <el-icon class="stat-icon" color="#f56c6c">
            <Warning />
          </el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number zero-stock">{{ stats.zeroStockCount }}</div>
            <div class="stat-label">零库存物料</div>
          </div>
          <el-icon class="stat-icon" color="#e6a23c">
            <Close />
          </el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number over-stock">{{ stats.overStockCount }}</div>
            <div class="stat-label">超量库存物料</div>
          </div>
          <el-icon class="stat-icon" color="#909399">
            <Upload />
          </el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number total">{{ stats.totalItems }}</div>
            <div class="stat-label">总库存记录</div>
          </div>
          <el-icon class="stat-icon" color="#67c23a">
            <Box />
          </el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索物料编码或名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.warehouse_code"
            placeholder="选择仓库"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.code"
              :label="warehouse.name"
              :value="warehouse.code"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.alert_type"
            placeholder="预警类型"
            clearable
            @change="handleSearch"
          >
            <el-option label="低库存" value="low_stock" />
            <el-option label="零库存" value="zero_stock" />
            <el-option label="超量库存" value="over_stock" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="warning" :icon="Bell" @click="refreshAlerts">
            刷新预警
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 预警列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>库存预警列表</span>
          <el-button 
            type="primary" 
            size="small" 
            :icon="Setting"
            @click="showSettingsDialog = true"
          >
            预警设置
          </el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="alertData"
        stripe
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <el-table-column label="预警级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getAlertType(row.alert_level)" size="small">
              {{ getAlertLevelText(row.alert_level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="warehouse_code" label="仓库编码" width="120" />
        <el-table-column prop="warehouse_name" label="仓库名称" width="150" />
        <el-table-column prop="material_code" label="物料编码" width="150" />
        <el-table-column prop="material_name" label="物料名称" min-width="200" />
        <el-table-column prop="batch_no" label="批次号" width="120" />
        <el-table-column label="当前库存" width="120">
          <template #default="{ row }">
            <span :class="getStockClass(row)">
              {{ formatNumber(row.available_quantity) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="safety_stock" label="安全库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.safety_stock) }}
          </template>
        </el-table-column>
        <el-table-column label="库存状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStockStatusType(row)" size="small">
              {{ getStockStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="预警描述" min-width="200">
          <template #default="{ row }">
            {{ getAlertDescription(row) }}
          </template>
        </el-table-column>
        <el-table-column prop="last_updated" label="最后更新" width="180">
          <template #default="{ row }">
            {{ formatDate(row.last_updated) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleQuickPurchase(row)"
            >
              快速采购
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleAdjustSafetyStock(row)"
            >
              调整安全库存
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 预警设置对话框 -->
    <el-dialog
      v-model="showSettingsDialog"
      title="预警设置"
      width="60%"
    >
      <el-form
        :model="settingsForm"
        label-width="150px"
      >
        <el-form-item label="全局预警开关">
          <el-switch
            v-model="settingsForm.enabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="低库存预警阈值">
          <el-input-number
            v-model="settingsForm.lowStockThreshold"
            :min="0"
            :max="100"
            :precision="0"
            class="setting-input"
          />
          <span class="setting-unit">%（低于安全库存的百分比）</span>
        </el-form-item>
        
        <el-form-item label="零库存预警">
          <el-switch
            v-model="settingsForm.zeroStockAlert"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        
        <el-form-item label="超量库存阈值">
          <el-input-number
            v-model="settingsForm.overStockThreshold"
            :min="100"
            :precision="0"
            class="setting-input"
          />
          <span class="setting-unit">%（超过安全库存的百分比）</span>
        </el-form-item>
        
        <el-form-item label="预警通知方式">
          <el-checkbox-group v-model="settingsForm.notificationMethods">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="system">系统通知</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="检查频率">
          <el-select v-model="settingsForm.checkFrequency" style="width: 200px">
            <el-option label="实时" value="realtime" />
            <el-option label="每小时" value="hourly" />
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettingsDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveSettings">保存设置</el-button>
      </template>
    </el-dialog>

    <!-- 安全库存调整对话框 -->
    <el-dialog
      v-model="showAdjustDialog"
      title="调整安全库存"
      width="40%"
    >
      <el-form
        ref="adjustFormRef"
        :model="adjustForm"
        label-width="120px"
      >
        <el-form-item label="仓库">
          <el-text>{{ currentRecord?.warehouse_name }}</el-text>
        </el-form-item>
        <el-form-item label="物料">
          <el-text>{{ currentRecord?.material_name }}</el-text>
        </el-form-item>
        <el-form-item label="当前库存">
          <el-text>{{ formatNumber(currentRecord?.available_quantity) }}</el-text>
        </el-form-item>
        <el-form-item label="当前安全库存">
          <el-text>{{ formatNumber(currentRecord?.safety_stock) }}</el-text>
        </el-form-item>
        <el-form-item label="新安全库存" prop="safety_stock">
          <el-input-number
            v-model="adjustForm.safety_stock"
            :min="0"
            :precision="6"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因">
          <el-input
            v-model="adjustForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请说明调整原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAdjustDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSafetyStockSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, Refresh, Bell, Setting, Warning, Close, Upload, Box 
} from '@element-plus/icons-vue'
import { inventoryApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const alertData = ref([])
const warehouseOptions = ref([])
const showSettingsDialog = ref(false)
const showAdjustDialog = ref(false)
const adjustFormRef = ref()
const currentRecord = ref(null)

const searchForm = reactive({
  search: '',
  warehouse_code: '',
  alert_type: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const settingsForm = reactive({
  enabled: true,
  lowStockThreshold: 80,
  zeroStockAlert: true,
  overStockThreshold: 200,
  notificationMethods: ['system'],
  checkFrequency: 'daily'
})

const adjustForm = reactive({
  safety_stock: 0,
  reason: ''
})

// 统计数据
const stats = computed(() => {
  const lowStock = alertData.value.filter(item => item.is_low_stock).length
  const zeroStock = alertData.value.filter(item => item.available_quantity <= 0).length
  const overStock = alertData.value.filter(item => 
    item.safety_stock > 0 && item.available_quantity > item.safety_stock * 2
  ).length
  
  return {
    lowStockCount: lowStock,
    zeroStockCount: zeroStock,
    overStockCount: overStock,
    totalItems: alertData.value.length
  }
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.search || undefined,
      warehouse_code: searchForm.warehouse_code || undefined
    }
    
    let response
    if (searchForm.alert_type === 'low_stock') {
      response = await inventoryApi.getLowStockRecords()
      alertData.value = response
      pagination.total = response.length
    } else {
      response = await inventoryApi.getInventoryRecords(params)
      let allData = response.results || []
      
      // 根据预警类型过滤
      if (searchForm.alert_type === 'zero_stock') {
        allData = allData.filter(item => item.available_quantity <= 0)
      } else if (searchForm.alert_type === 'over_stock') {
        allData = allData.filter(item => 
          item.safety_stock > 0 && item.available_quantity > item.safety_stock * 2
        )
      }
      
      alertData.value = allData
      pagination.total = allData.length
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadWarehouses = async () => {
  try {
    const response = await inventoryApi.getWarehouses()
    warehouseOptions.value = response.results || []
  } catch (error) {
    ElMessage.error('加载仓库列表失败')
  }
}

const refreshAlerts = () => {
  ElMessage.success('预警数据已刷新')
  loadData()
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    warehouse_code: '',
    alert_type: ''
  })
  handleSearch()
}

const handleViewDetail = (row) => {
  ElMessage.info('详情功能开发中...')
}

const handleQuickPurchase = (row) => {
  ElMessage.info('快速采购功能开发中...')
}

const handleAdjustSafetyStock = (row) => {
  currentRecord.value = row
  adjustForm.safety_stock = row.safety_stock
  adjustForm.reason = ''
  showAdjustDialog.value = true
}

const handleSafetyStockSubmit = async () => {
  try {
    await inventoryApi.updateInventoryRecord(currentRecord.value.id, {
      safety_stock: adjustForm.safety_stock
    })
    ElMessage.success('安全库存调整成功')
    showAdjustDialog.value = false
    loadData()
  } catch (error) {
    ElMessage.error('调整失败')
  }
}

const handleSaveSettings = () => {
  ElMessage.success('预警设置已保存')
  showSettingsDialog.value = false
}

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}

const getRowClassName = ({ row }) => {
  if (row.available_quantity <= 0) return 'zero-stock-row'
  if (row.is_low_stock) return 'low-stock-row'
  if (row.safety_stock > 0 && row.available_quantity > row.safety_stock * 2) {
    return 'over-stock-row'
  }
  return ''
}

const getAlertType = (level) => {
  const typeMap = {
    critical: 'danger',
    warning: 'warning',
    info: 'info'
  }
  return typeMap[level] || 'info'
}

const getAlertLevelText = (level) => {
  const levelMap = {
    critical: '严重',
    warning: '警告',
    info: '信息'
  }
  return levelMap[level] || '未知'
}

const getStockClass = (row) => {
  if (row.available_quantity <= 0) return 'zero-stock'
  if (row.is_low_stock) return 'low-stock'
  return ''
}

const getStockStatusType = (row) => {
  if (row.available_quantity <= 0) return 'danger'
  if (row.is_low_stock) return 'warning'
  if (row.safety_stock > 0 && row.available_quantity > row.safety_stock * 2) return 'info'
  return 'success'
}

const getStockStatusText = (row) => {
  if (row.available_quantity <= 0) return '零库存'
  if (row.is_low_stock) return '低库存'
  if (row.safety_stock > 0 && row.available_quantity > row.safety_stock * 2) return '超量库存'
  return '正常'
}

const getAlertDescription = (row) => {
  if (row.available_quantity <= 0) {
    return '库存已耗尽，请立即补货'
  }
  if (row.is_low_stock) {
    return `库存低于安全库存 ${formatNumber(row.safety_stock)}，建议补货`
  }
  if (row.safety_stock > 0 && row.available_quantity > row.safety_stock * 2) {
    return '库存过量，可能存在资金占用问题'
  }
  return '库存正常'
}

const formatNumber = (value) => {
  if (value === null || value === undefined) return '-'
  return Number(value).toLocaleString()
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadData()
  loadWarehouses()
})
</script>

<style scoped>
.alert-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-number.low-stock {
  color: #f56c6c;
}

.stat-number.zero-stock {
  color: #e6a23c;
}

.stat-number.over-stock {
  color: #909399;
}

.stat-number.total {
  color: #67c23a;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  opacity: 0.2;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.setting-input {
  width: 150px;
  margin-right: 10px;
}

.setting-unit {
  color: #909399;
  font-size: 14px;
}

.low-stock {
  color: #f56c6c;
  font-weight: bold;
}

.zero-stock {
  color: #e6a23c;
  font-weight: bold;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

:deep(.low-stock-row) {
  background-color: #fef0f0;
}

:deep(.zero-stock-row) {
  background-color: #fdf6ec;
}

:deep(.over-stock-row) {
  background-color: #f4f4f5;
}

:deep(.low-stock-row:hover > td) {
  background-color: #fde2e2 !important;
}

:deep(.zero-stock-row:hover > td) {
  background-color: #faecd8 !important;
}

:deep(.over-stock-row:hover > td) {
  background-color: #e9e9eb !important;
}
</style>