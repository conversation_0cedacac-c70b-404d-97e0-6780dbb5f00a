<template>
  <div class="warehouse-management">
    <h1 class="page-title">仓库管理</h1>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索仓库编码或名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>

        <el-col :span="6">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="success" :icon="Plus" @click="showCreateDialog = true">
            新建仓库
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="code" label="仓库编码" width="150" />
        <el-table-column prop="name" label="仓库名称" min-width="100" />

        <el-table-column prop="location" label="位置" min-width="200" />
        <el-table-column prop="manager" label="管理员" width="100" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '活跃' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="info" 
              @click="handleViewInventory(row)"
            >
              查看库存
            </el-button>
            <el-button 
              size="small" 
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑仓库对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEdit ? '编辑仓库' : '新建仓库'"
      width="50%"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="仓库编码" prop="code">
          <el-input 
            v-model="form.code" 
            placeholder="请输入仓库编码"
            :disabled="isEdit"
          />
        </el-form-item>
        
        <el-form-item label="仓库名称" prop="name">
          <el-input 
            v-model="form.name" 
            placeholder="请输入仓库名称"
          />
        </el-form-item>
        

        
        <el-form-item label="位置">
          <el-input 
            v-model="form.location" 
            placeholder="请输入仓库位置"
          />
        </el-form-item>
        
        <el-form-item label="管理员">
          <el-input 
            v-model="form.manager" 
            placeholder="请输入仓库管理员"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="form.is_active"
            active-text="活跃"
            inactive-text="停用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 仓库详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="仓库详情"
      width="60%"
    >
      <div v-if="currentWarehouse" class="warehouse-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="仓库编码">
            {{ currentWarehouse.code }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库名称">
            {{ currentWarehouse.name }}
          </el-descriptions-item>

          <el-descriptions-item label="状态">
            <el-tag :type="currentWarehouse.is_active ? 'success' : 'danger'">
              {{ currentWarehouse.is_active ? '活跃' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="位置" :span="2">
            {{ currentWarehouse.location || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="管理员">
            {{ currentWarehouse.manager || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(currentWarehouse.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">
            {{ formatDate(currentWarehouse.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 仓库库存对话框 -->
    <el-dialog
      v-model="showInventoryDialog"
      title="仓库库存"
      width="80%"
    >
      <el-table
        v-loading="inventoryLoading"
        :data="inventoryData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="material_code" label="物料编码" width="150" />
        <el-table-column prop="material_name" label="物料名称" min-width="200" />
        <el-table-column prop="batch_no" label="批次号" width="120" />
        <el-table-column prop="quantity" label="总库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.quantity) }}
          </template>
        </el-table-column>
        <el-table-column prop="available_quantity" label="可用库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.available_quantity) }}
          </template>
        </el-table-column>
        <el-table-column prop="locked_quantity" label="锁定库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.locked_quantity) }}
          </template>
        </el-table-column>
        <el-table-column prop="safety_stock" label="安全库存" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.safety_stock) }}
          </template>
        </el-table-column>
        <el-table-column prop="unit_cost" label="单价" width="120">
          <template #default="{ row }">
            {{ row.unit_cost ? '¥' + formatNumber(row.unit_cost) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_value" label="总价值" width="120">
          <template #default="{ row }">
            {{ row.total_value ? '¥' + formatNumber(row.total_value) : '-' }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { inventoryApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const inventoryLoading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const inventoryData = ref([])
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showInventoryDialog = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentWarehouse = ref(null)

const searchForm = reactive({
  search: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const form = reactive({
  code: '',
  name: '',
  location: '',
  manager: '',
  is_active: true
})

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入仓库编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入仓库名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ]
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.search || undefined
    }
    
    const response = await inventoryApi.getWarehouses(params)
    tableData.value = response.results || []
    pagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: ''
  })
  handleSearch()
}

const handleView = (row) => {
  currentWarehouse.value = row
  showDetailDialog.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(form, {
    code: row.code,
    name: row.name,
    location: row.location || '',
    manager: row.manager || '',
    is_active: row.is_active
  })
  currentWarehouse.value = row
  showCreateDialog.value = true
}

const handleViewInventory = async (row) => {
  currentWarehouse.value = row
  inventoryLoading.value = true
  showInventoryDialog.value = true
  
  try {
    const response = await inventoryApi.getInventoryRecords({
      warehouse_code: row.code
    })
    inventoryData.value = response.results || []
  } catch (error) {
    ElMessage.error('加载库存数据失败')
  } finally {
    inventoryLoading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除仓库 "${row.name}" 吗？此操作不可恢复！`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await inventoryApi.deleteWarehouse(row.id)
    ElMessage.success('仓库删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleCloseDialog = () => {
  showCreateDialog.value = false
  resetForm()
}

const resetForm = () => {
  isEdit.value = false
  currentWarehouse.value = null
  Object.assign(form, {
    code: '',
    name: '',
    location: '',
    manager: '',
    is_active: true
  })
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    if (isEdit.value) {
      // 更新时需要包含 code 字段，但不修改它
      const updateData = {
        code: currentWarehouse.value.code, // 使用原来的 code
        name: form.name,
        location: form.location || '',
        manager: form.manager || '',
        is_active: form.is_active
      }
      await inventoryApi.updateWarehouse(currentWarehouse.value.id, updateData)
      ElMessage.success('仓库更新成功')
    } else {
      // 创建时只传入必要字段，不包含 created_by
      const createData = {
        code: form.code,
        name: form.name,
        location: form.location || '',
        manager: form.manager || '',
        is_active: form.is_active
      }
      await inventoryApi.createWarehouse(createData)
      ElMessage.success('仓库创建成功')
    }
    
    handleCloseDialog()
    loadData()
  } catch (error) {
    if (error.response) {
      ElMessage.error('操作失败: ' + (error.response.data?.detail || '未知错误'))
    }
  } finally {
    submitting.value = false
  }
}

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}



const formatNumber = (value) => {
  if (value === null || value === undefined) return '-'
  return Number(value).toLocaleString()
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.warehouse-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.warehouse-detail {
  margin-top: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>