<template>
  <div class="request-management">
    <h1 class="page-title">采购申请</h1>
    
    <!-- 采购申请表单 -->
    <el-card class="form-card">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="120px"
        class="purchase-form"
      >
        <!-- 需求日期 -->
        <el-form-item label="需求日期" prop="required_date" required>
          <el-date-picker
            v-model="form.required_date"
            type="date"
            placeholder="请选择"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 申请部门 -->
        <el-form-item label="申请部门" prop="department" required>
          <el-input 
            v-model="form.department_name" 
            placeholder="申请部门"
            :disabled="true"
          />
        </el-form-item>

        <!-- 优先级 -->
        <el-form-item label="优先级" prop="priority">
          <el-select 
            v-model="form.priority" 
            placeholder="请选择优先级"
            style="width: 100%"
          >
            <el-option label="低" value="LOW" />
            <el-option label="普通" value="NORMAL" />
            <el-option label="高" value="HIGH" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>

        <!-- 采购明细 -->
        <div class="purchase-details">
          <h3>采购明细</h3>
          <div 
            v-for="(detail, index) in form.purchase_details" 
            :key="index" 
            class="detail-item"
          >
            <div class="detail-header">
              <h4>采购明细 {{ index + 1 }}</h4>
              <el-button 
                v-if="form.purchase_details.length > 1"
                type="danger" 
                size="small" 
                @click="removeDetail(index)"
              >
                删除
              </el-button>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item 
                  label="名称" 
                  :prop="`purchase_details.${index}.name`"
                  :rules="{ required: true, message: '请输入名称', trigger: 'blur' }"
                >
                  <el-input 
                    v-model="detail.name" 
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="紧急程度">
                  <el-select 
                    v-model="detail.urgency_level" 
                    placeholder="请选择紧急程度"
                    style="width: 100%"
                  >
                    <el-option label="普通" value="NORMAL" />
                    <el-option label="紧急" value="URGENT" />
                    <el-option label="非常紧急" value="VERY_URGENT" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="规格">
                  <el-input 
                    v-model="detail.specification" 
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="数量">
                  <el-input-number 
                    v-model="detail.quantity" 
                    :min="1"
                    placeholder="请输入"
                    style="width: 100%"
                    @change="handleQuantityChange(detail)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="单位">
                  <el-input 
                    v-model="detail.unit" 
                    placeholder="请输入"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="预估单价">
                  <el-input-number 
                    v-model="detail.estimated_price" 
                    :min="0"
                    :precision="2"
                    placeholder="请输入"
                    style="width: 100%"
                    @change="handlePriceChange(detail)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="小计金额">
                  <el-input-number 
                    v-model="detail.total_amount" 
                    :min="0"
                    :precision="2"
                    placeholder="自动计算"
                    style="width: 100%"
                    :disabled="true"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          
          <div class="add-detail-tip">
            如需采购多种产品,请点击增加明细
          </div>
          
          <el-button type="primary" @click="addDetail" class="add-detail-btn">
            + 增加明细
          </el-button>
        </div>

        <!-- 统计信息 -->
        <div class="summary-info">
          <div class="summary-item">
            <span class="label">总数量:</span>
            <span class="value">{{ totalQuantity }}</span>
          </div>
          <div class="summary-item">
            <span class="label">总金额:</span>
            <span class="value">¥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </div>

        <!-- 申请原因 -->
        <el-form-item label="申请原因" prop="reason" required>
          <el-input 
            v-model="form.reason" 
            type="textarea" 
            :rows="4"
            placeholder="请输入申请原因"
          />
        </el-form-item>

        <!-- 附件 -->
        <el-form-item label="附件">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :data="{ type: 'purchase_request' }"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="form.attachments"
            multiple
          >
            <el-button type="primary">
              <el-icon><Plus /></el-icon>
              添加附件
            </el-button>
          </el-upload>
        </el-form-item>



        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            提交
          </el-button>
          <el-button @click="handleSaveDraft" :loading="saving">
            保存草稿
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { procurementApi, authApi, userApi } from '@/api'

// 表单引用
const formRef = ref()
const uploadRef = ref()

// 响应式数据
const submitting = ref(false)
const saving = ref(false)
const departments = ref([])

// 表单数据
const form = reactive({
  required_date: '',
  purchase_details: [
    {
      material_code: '',
      name: '',
      urgency_level: 'NORMAL',
      specification: '',
      quantity: 1,
      unit: '',
      estimated_price: 0,
      total_amount: 0,
      required_date: '',
      suggested_supplier: '',
      notes: ''
    }
  ],
  reason: '',
  department: '',
  department_name: '',
  requested_by: '',
  priority: 'NORMAL',
  attachments: []
})

// 表单验证规则
const rules = {
  required_date: [
    { required: true, message: '请选择需求日期', trigger: 'change' }
  ],
  department: [
    { required: true, message: '申请部门不能为空', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入申请原因', trigger: 'blur' }
  ]
}

// 计算属性
const totalQuantity = computed(() => {
  return form.purchase_details.reduce((sum, detail) => sum + (detail.quantity || 0), 0)
})

const totalAmount = computed(() => {
  return form.purchase_details.reduce((sum, detail) => sum + (detail.total_amount || 0), 0)
})

// 上传相关
const uploadUrl = '/api/procurement/requests/upload/'

const getCsrfToken = () => {
  const csrfToken = document.cookie.split('; ').find(row => row.startsWith('csrftoken='))
  return csrfToken ? csrfToken.split('=')[1] : ''
}

const uploadHeaders = {
  'Authorization': `Token ${localStorage.getItem('token')}`,
  'X-CSRFToken': getCsrfToken()
}

// 方法
const addDetail = () => {
  form.purchase_details.push({
    material_code: '',
    name: '',
    urgency_level: 'NORMAL',
    specification: '',
    quantity: 1,
    unit: '',
    estimated_price: 0,
    total_amount: 0,
    required_date: '',
    suggested_supplier: '',
    notes: ''
  })
}

const removeDetail = (index) => {
  if (form.purchase_details.length > 1) {
    form.purchase_details.splice(index, 1)
  }
}

// 监听明细变化，自动计算小计
const calculateItemTotal = (detail) => {
  if (detail.quantity && detail.estimated_price) {
    detail.total_amount = detail.quantity * detail.estimated_price
  } else {
    detail.total_amount = 0
  }
}

// 监听数量或单价变化
const handleQuantityChange = (detail) => {
  calculateItemTotal(detail)
}

const handlePriceChange = (detail) => {
  calculateItemTotal(detail)
}

const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response, file) => {
  form.attachments.push({
    name: file.name,
    url: response.url,
    size: file.size
  })
  ElMessage.success('附件上传成功')
}

const handleUploadError = (error) => {
  ElMessage.error('附件上传失败')
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 创建请求数据，不包含申请人信息（由后端自动设置）
    const requestData = {
      required_date: form.required_date,
      department: form.department,
      priority: form.priority,
      reason: form.reason,
      purchase_details: form.purchase_details,
      attachments: form.attachments,
      total_quantity: totalQuantity.value,
      total_amount: totalAmount.value,
      status: 'SUBMITTED'  // 使用正确的状态值
    }
    
    await procurementApi.createPurchaseRequest(requestData)
    
    ElMessage.success('采购申请提交成功')
    
    // 重置表单
    resetForm()
    
  } catch (error) {
    console.error('提交采购申请失败:', error)
    ElMessage.error('提交失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

const handleSaveDraft = async () => {
  try {
    saving.value = true
    
    // 创建请求数据，不包含申请人信息（由后端自动设置）
    const requestData = {
      required_date: form.required_date,
      department: form.department,
      priority: form.priority,
      reason: form.reason,
      purchase_details: form.purchase_details,
      attachments: form.attachments,
      total_quantity: totalQuantity.value,
      total_amount: totalAmount.value,
      status: 'DRAFT'  // 草稿状态
    }
    
    await procurementApi.createPurchaseRequest(requestData)
    
    ElMessage.success('草稿保存成功')
    
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  formRef.value.resetFields()
  
  // 重置采购明细
  form.purchase_details = [{
    material_code: '',
    name: '',
    urgency_level: 'NORMAL',
    specification: '',
    quantity: 1,
    unit: '',
    estimated_price: 0,
    total_amount: 0,
    required_date: '',
    suggested_supplier: '',
    notes: ''
  }]
  
  // 重置其他字段，但保留部门和申请人信息
  form.required_date = ''
  form.reason = ''
  form.priority = 'NORMAL'
  form.attachments = []
  
  // 重新设置部门信息
  if (form.department) {
    const userDept = departments.value.find(dept => dept.code === form.department)
    if (userDept) {
      form.department_name = `${userDept.code} - ${userDept.name}`
    }
  }
}

// 加载部门数据
const loadDepartments = async () => {
  try {
    const response = await userApi.getDepartments()
    departments.value = response.results || []
  } catch (error) {
    console.error('加载部门数据失败:', error)
    ElMessage.error('加载部门数据失败: ' + error.message)
  }
}

// 生命周期
onMounted(async () => {
  // 加载部门数据
  await loadDepartments()
  
  // 获取当前登录用户信息并设置部门
  try {
    const userProfile = await authApi.getProfile()
    
    // 设置申请人信息
    form.requested_by = userProfile.user?.username || userProfile.user?.first_name || '当前用户'
    
    // 根据用户部门设置申请部门
    const userDepartment = userProfile.profile?.department
    
    if (userDepartment) {
      form.department = userDepartment
      // 查找对应的部门名称
      const userDept = departments.value.find(dept => dept.code === userDepartment)
      if (userDept) {
        form.department_name = `${userDept.code} - ${userDept.name}`
      } else {
        form.department_name = userDepartment
      }
    } else {
      form.department = '未设置'
      form.department_name = '未设置'
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    form.requested_by = '当前用户'
    form.department = '未设置'
    form.department_name = '未设置'
  }
})
</script>

<style scoped>
.request-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.form-card {
  margin-bottom: 20px;
}

.purchase-form {
  max-width: 1200px;
}

.purchase-details {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.purchase-details h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
}

.detail-item {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.detail-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.add-detail-tip {
  margin: 15px 0;
  color: #909399;
  font-size: 14px;
  text-align: center;
}

.add-detail-btn {
  width: 100%;
  margin-top: 10px;
}

.summary-info {
  display: flex;
  gap: 40px;
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item .label {
  font-weight: 500;
  color: #303133;
}

.summary-item .value {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}



.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item.is-required .el-form-item__label::before) {
  color: #f56c6c;
}
</style>
