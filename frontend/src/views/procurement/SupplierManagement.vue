<template>
  <div class="supplier-management">
    <h1 class="page-title">供应商管理</h1>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索供应商编码或名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.supplier_type"
            placeholder="供应商类型"
            clearable
            @change="handleSearch"
          >
            <el-option label="制造商" value="MANUFACTURER" />
            <el-option label="经销商" value="DISTRIBUTOR" />
            <el-option label="代理商" value="AGENT" />
            <el-option label="服务商" value="SERVICE" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.rating"
            placeholder="供应商评级"
            clearable
            @change="handleSearch"
          >
            <el-option label="A级（优秀）" value="A" />
            <el-option label="B级（良好）" value="B" />
            <el-option label="C级（一般）" value="C" />
            <el-option label="D级（较差）" value="D" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="success" :icon="Plus" @click="showCreateDialog = true">
            新建供应商
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="code" label="供应商编码" width="150" />
        <el-table-column prop="name" label="供应商名称" min-width="200" />
        <el-table-column prop="supplier_type" label="类型" width="100">
          <template #default="{ row }">
            {{ getSupplierTypeText(row.supplier_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="rating" label="评级" width="100">
          <template #default="{ row }">
            <el-tag :type="getRatingType(row.rating)" size="small">
              {{ row.rating }}级
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="contact_person" label="联系人" width="120" />
        <el-table-column prop="phone" label="电话" width="130" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '活跃' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              :type="row.is_active ? 'danger' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.is_active ? '停用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑供应商对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEdit ? '编辑供应商' : '新建供应商'"
      width="70%"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="供应商编码" prop="code">
                  <el-input 
                    v-model="form.code" 
                    placeholder="请输入供应商编码"
                    :disabled="isEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="供应商名称" prop="name">
                  <el-input 
                    v-model="form.name" 
                    placeholder="请输入供应商名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="供应商类型" prop="supplier_type">
                  <el-select v-model="form.supplier_type" style="width: 100%">
                    <el-option label="制造商" value="MANUFACTURER" />
                    <el-option label="经销商" value="DISTRIBUTOR" />
                    <el-option label="代理商" value="AGENT" />
                    <el-option label="服务商" value="SERVICE" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="供应商评级" prop="rating">
                  <el-select v-model="form.rating" style="width: 100%">
                    <el-option label="A级（优秀）" value="A" />
                    <el-option label="B级（良好）" value="B" />
                    <el-option label="C级（一般）" value="C" />
                    <el-option label="D级（较差）" value="D" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="交期（天）">
                  <el-input-number
                    v-model="form.lead_time"
                    :min="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="联系人">
                  <el-input v-model="form.contact_person" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="电话">
                  <el-input v-model="form.phone" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="邮箱">
                  <el-input v-model="form.email" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="地址">
              <el-input
                v-model="form.address"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
            
            <el-form-item label="网站">
              <el-input v-model="form.website" />
            </el-form-item>
          </el-tab-pane>
          
          <el-tab-pane label="财务信息" name="finance">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="税号">
                  <el-input v-model="form.tax_number" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="银行账号">
                  <el-input v-model="form.bank_account" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开户银行">
                  <el-input v-model="form.bank_name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="付款条件">
                  <el-input v-model="form.payment_terms" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        
        <el-form-item label="状态">
          <el-switch
            v-model="form.is_active"
            active-text="活跃"
            inactive-text="停用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 供应商详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="供应商详情"
      width="80%"
    >
      <div v-if="currentSupplier" class="supplier-detail">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="供应商编码">
            {{ currentSupplier.code }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商名称">
            {{ currentSupplier.name }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商类型">
            {{ getSupplierTypeText(currentSupplier.supplier_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="评级">
            <el-tag :type="getRatingType(currentSupplier.rating)">
              {{ currentSupplier.rating }}级
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="交期">
            {{ currentSupplier.lead_time }} 天
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentSupplier.is_active ? 'success' : 'danger'">
              {{ currentSupplier.is_active ? '活跃' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="联系人">
            {{ currentSupplier.contact_person || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="电话">
            {{ currentSupplier.phone || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ currentSupplier.email || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="地址" :span="3">
            {{ currentSupplier.address || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="网站" :span="3">
            <el-link 
              v-if="currentSupplier.website" 
              :href="currentSupplier.website" 
              target="_blank"
            >
              {{ currentSupplier.website }}
            </el-link>
            <span v-else>未设置</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { procurementApi } from '@/api'
import dayjs from 'dayjs'
// import SupplierEvaluationForm from './components/SupplierEvaluationForm.vue'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const isEdit = ref(false)
const formRef = ref()
const currentSupplier = ref(null)
const activeTab = ref('basic')

const searchForm = reactive({
  search: '',
  supplier_type: '',
  rating: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const form = reactive({
  code: '',
  name: '',
  supplier_type: 'MANUFACTURER',
  rating: 'B',
  contact_person: '',
  phone: '',
  email: '',
  address: '',
  website: '',
  tax_number: '',
  bank_account: '',
  bank_name: '',
  payment_terms: '',
  lead_time: 7,
  is_active: true
})

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入供应商编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  supplier_type: [
    { required: true, message: '请选择供应商类型', trigger: 'change' }
  ],
  rating: [
    { required: true, message: '请选择供应商评级', trigger: 'change' }
  ]
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.search || undefined,
      supplier_type: searchForm.supplier_type || undefined,
      rating: searchForm.rating || undefined
    }
    
    const response = await procurementApi.getSuppliers(params)
    tableData.value = response.results || []
    pagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    supplier_type: '',
    rating: ''
  })
  handleSearch()
}

const handleView = (row) => {
  currentSupplier.value = row
  showDetailDialog.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(form, {
    code: row.code,
    name: row.name,
    supplier_type: row.supplier_type,
    rating: row.rating,
    contact_person: row.contact_person || '',
    phone: row.phone || '',
    email: row.email || '',
    address: row.address || '',
    website: row.website || '',
    tax_number: row.tax_number || '',
    bank_account: row.bank_account || '',
    bank_name: row.bank_name || '',
    payment_terms: row.payment_terms || '',
    lead_time: row.lead_time,
    is_active: row.is_active
  })
  currentSupplier.value = row
  showCreateDialog.value = true
}

const handleToggleStatus = async (row) => {
  try {
    const action = row.is_active ? '停用' : '启用'
    await ElMessageBox.confirm(`确定要${action}供应商 "${row.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 传递完整的供应商数据，只更新is_active字段
    const updateData = {
      code: row.code,
      name: row.name,
      supplier_type: row.supplier_type,
      rating: row.rating,
      contact_person: row.contact_person || '',
      phone: row.phone || '',
      email: row.email || '',
      address: row.address || '',
      website: row.website || '',
      tax_number: row.tax_number || '',
      bank_account: row.bank_account || '',
      bank_name: row.bank_name || '',
      payment_terms: row.payment_terms || '',
      lead_time: row.lead_time,
      is_active: !row.is_active
    }
    
    await procurementApi.updateSupplier(row.id, updateData)
    ElMessage.success(`${action}成功`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleCloseDialog = () => {
  showCreateDialog.value = false
  resetForm()
}

const resetForm = () => {
  isEdit.value = false
  currentSupplier.value = null
  activeTab.value = 'basic'
  Object.assign(form, {
    code: '',
    name: '',
    supplier_type: 'MANUFACTURER',
    rating: 'B',
    contact_person: '',
    phone: '',
    email: '',
    address: '',
    website: '',
    tax_number: '',
    bank_account: '',
    bank_name: '',
    payment_terms: '',
    lead_time: 7,
    is_active: true
  })
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    if (isEdit.value) {
      await procurementApi.updateSupplier(currentSupplier.value.id, form)
      ElMessage.success('供应商更新成功')
    } else {
      await procurementApi.createSupplier(form)
      ElMessage.success('供应商创建成功')
    }
    
    handleCloseDialog()
    loadData()
  } catch (error) {
    if (error.response) {
      ElMessage.error('操作失败: ' + (error.response.data?.detail || '未知错误'))
    }
  } finally {
    submitting.value = false
  }
}

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}

const getSupplierTypeText = (type) => {
  const typeMap = {
    MANUFACTURER: '制造商',
    DISTRIBUTOR: '经销商',
    AGENT: '代理商',
    SERVICE: '服务商'
  }
  return typeMap[type] || '未知'
}

const getRatingType = (rating) => {
  const typeMap = {
    A: 'success',
    B: 'primary',
    C: 'warning',
    D: 'danger'
  }
  return typeMap[rating] || 'info'
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.supplier-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.supplier-detail {
  margin-top: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

:deep(.el-progress-bar__outer) {
  background-color: #f5f7fa;
}
</style>