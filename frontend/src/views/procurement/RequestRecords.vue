<template>
  <div class="request-records">
    <h1 class="page-title">采购申请记录</h1>
    
    <!-- 用户权限提示 -->
    <el-alert
      v-if="userRole"
      :title="getRoleTitle()"
      :description="getRoleDescription()"
      type="info"
      show-icon
      :closable="false"
      class="role-alert"
    />
    
    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="申请单号">
          <el-input v-model="searchForm.request_no" placeholder="请输入申请单号" clearable />
        </el-form-item>
        <el-form-item label="申请部门">
          <el-select v-model="searchForm.department" placeholder="请选择部门" clearable>
            <el-option
              v-for="dept in departments"
              :key="dept.code"
              :label="`${dept.code} - ${dept.name}`"
              :value="dept.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已提交" value="SUBMITTED" />
            <el-option label="已批准" value="APPROVED" />
            <el-option label="已拒绝" value="REJECTED" />
            <el-option label="已下单" value="ORDERED" />
            <el-option label="部分到货" value="PARTIAL_RECEIVED" />
            <el-option label="已到货" value="RECEIVED" />
            <el-option label="已关闭" value="CLOSED" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请日期">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 申请记录表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="request_no" label="申请单号" width="180" />
        <el-table-column prop="department" label="申请部门" width="120">
          <template #default="{ row }">
            {{ getDepartmentName(row.department) }}
          </template>
        </el-table-column>
        <el-table-column prop="requested_by" label="申请人" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="required_date" label="需求日期" width="120" />
        <el-table-column prop="total_amount" label="总金额" width="120">
          <template #default="{ row }">
            ¥{{ formatAmount(row.total_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" fixed="right">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button size="small" type="primary" @click.stop="handleView(row)">
                查看
              </el-button>
              <el-button 
                v-if="row.status === 'DRAFT'" 
                size="small" 
                type="warning" 
                @click.stop="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button 
                v-if="row.status === 'DRAFT'" 
                size="small" 
                type="danger" 
                @click.stop="handleDelete(row)"
              >
                删除
              </el-button>
              <el-button 
                v-if="canApproveCurrentStatus(row) && hasApprovalPermission(row)" 
                size="small" 
                type="success" 
                @click.stop="handleApprove(row)"
              >
                {{ userRole === 'admin' ? '管理员批准' : '部门批准' }}
              </el-button>
              <el-button 
                v-if="canApproveCurrentStatus(row) && hasApprovalPermission(row)" 
                size="small" 
                type="danger" 
                @click.stop="handleReject(row)"
              >
                {{ userRole === 'admin' ? '管理员拒绝' : '部门拒绝' }}
              </el-button>
              <el-button 
                v-if="row.status === 'REJECTED' && row.requested_by === userProfile?.user?.username" 
                size="small" 
                type="warning" 
                @click.stop="handleResubmit(row)"
              >
                重新申请
              </el-button>
              <el-button 
                v-if="row.requested_by === userProfile?.user?.username" 
                size="small" 
                type="info" 
                @click.stop="handleEditNotes(row)"
              >
                编辑备注
              </el-button>
              <el-button 
                v-if="userRole === 'admin'" 
                size="small" 
                type="danger" 
                @click.stop="handleAdminDelete(row)"
              >
                删除记录
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 拒绝申请对话框 -->
    <el-dialog
      v-model="rejectVisible"
      title="拒绝申请"
      width="500px"
    >
      <el-form :model="rejectForm" ref="rejectFormRef" :rules="rejectRules">
        <el-form-item label="拒绝原因" prop="rejected_reason" required>
          <el-input
            v-model="rejectForm.rejected_reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmReject" :loading="rejecting">
            确认拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑备注对话框 -->
    <el-dialog
      v-model="notesVisible"
      title="编辑备注"
      width="500px"
    >
      <el-form :model="notesForm" ref="notesFormRef">
        <el-form-item label="备注内容">
          <el-input
            v-model="notesForm.notes"
            type="textarea"
            :rows="6"
            placeholder="请输入备注内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="notesVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUpdateNotes" :loading="updatingNotes">
            保存备注
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 申请详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="采购申请详情"
      width="80%"
      class="detail-dialog"
    >
      <div v-if="currentRequest" class="request-detail">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>申请单号：</label>
                <span>{{ currentRequest.request_no }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>申请部门：</label>
                <span>{{ getDepartmentName(currentRequest.department) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>申请人：</label>
                <span>{{ currentRequest.requested_by }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>状态：</label>
                <el-tag :type="getStatusType(currentRequest.status)">
                  {{ getStatusText(currentRequest.status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>优先级：</label>
                <el-tag :type="getPriorityType(currentRequest.priority)">
                  {{ getPriorityText(currentRequest.priority) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>需求日期：</label>
                <span>{{ currentRequest.required_date }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <label>申请原因：</label>
                <span>{{ currentRequest.reason }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="currentRequest.notes">
            <el-col :span="24">
              <div class="info-item">
                <label>申请人备注：</label>
                <span class="notes-content">{{ currentRequest.notes }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 采购明细 -->
        <el-card class="details-card">
          <template #header>
            <div class="card-header">
              <span>采购明细</span>
            </div>
          </template>
          <el-table :data="currentRequest.purchase_details || []" stripe>
            <el-table-column prop="item_no" label="项次" width="80" />
            <el-table-column prop="material_code" label="物料编码" width="150" />
            <el-table-column prop="name" label="物料名称" min-width="200" />
            <el-table-column prop="quantity" label="数量" width="100" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="estimated_price" label="预估单价" width="120">
              <template #default="{ row }">
                ¥{{ formatAmount(row.estimated_price) }}
              </template>
            </el-table-column>
            <el-table-column prop="total_amount" label="小计" width="120">
              <template #default="{ row }">
                ¥{{ formatAmount(row.total_amount) }}
              </template>
            </el-table-column>
            <el-table-column prop="required_date" label="需求日期" width="120" />
            <el-table-column prop="suggested_supplier" label="建议供应商" width="150" />
          </el-table>
        </el-card>

        <!-- 审批信息 -->
        <el-card v-if="currentRequest.dept_approved_by || currentRequest.admin_approved_by || currentRequest.dept_rejected_reason || currentRequest.admin_rejected_reason" class="approval-card">
          <template #header>
            <div class="card-header">
              <span>审批信息</span>
            </div>
          </template>
          
          <!-- 部门经理审批信息 -->
          <div v-if="currentRequest.dept_approved_by || currentRequest.dept_rejected_reason" class="approval-section">
            <h4>部门经理审批</h4>
            <el-row :gutter="20">
              <el-col :span="8" v-if="currentRequest.dept_approved_by">
                <div class="info-item">
                  <label>审批人：</label>
                  <span>{{ currentRequest.dept_approved_by }}</span>
                </div>
              </el-col>
              <el-col :span="8" v-if="currentRequest.dept_approved_at">
                <div class="info-item">
                  <label>审批时间：</label>
                  <span>{{ formatDate(currentRequest.dept_approved_at) }}</span>
                </div>
              </el-col>
              <el-col :span="24" v-if="currentRequest.dept_rejected_reason">
                <div class="info-item">
                  <label>拒绝原因：</label>
                  <span class="rejected-reason">{{ currentRequest.dept_rejected_reason }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <!-- 管理员审批信息 -->
          <div v-if="currentRequest.admin_approved_by || currentRequest.admin_rejected_reason" class="approval-section">
            <h4>管理员审批</h4>
            <el-row :gutter="20">
              <el-col :span="8" v-if="currentRequest.admin_approved_by">
                <div class="info-item">
                  <label>审批人：</label>
                  <span>{{ currentRequest.admin_approved_by }}</span>
                </div>
              </el-col>
              <el-col :span="8" v-if="currentRequest.admin_approved_at">
                <div class="info-item">
                  <label>审批时间：</label>
                  <span>{{ formatDate(currentRequest.admin_approved_at) }}</span>
                </div>
              </el-col>
              <el-col :span="24" v-if="currentRequest.admin_rejected_reason">
                <div class="info-item">
                  <label>拒绝原因：</label>
                  <span class="rejected-reason">{{ currentRequest.admin_rejected_reason }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { procurementApi, userApi, authApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const departments = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const detailVisible = ref(false)
const currentRequest = ref(null)
const userRole = ref(null)
const userProfile = ref(null)
const rejectVisible = ref(false)
const rejecting = ref(false)
const currentRejectRequest = ref(null)
const notesVisible = ref(false)
const updatingNotes = ref(false)
const currentNotesRequest = ref(null)

// 搜索表单
const searchForm = reactive({
  request_no: '',
  department: '',
  status: '',
  date_range: []
})

// 拒绝表单
const rejectForm = reactive({
  rejected_reason: ''
})

// 拒绝表单验证规则
const rejectRules = {
  rejected_reason: [
    { required: true, message: '请输入拒绝原因', trigger: 'blur' },
    { min: 5, message: '拒绝原因至少5个字符', trigger: 'blur' }
  ]
}

// 备注表单
const notesForm = reactive({
  notes: ''
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...searchForm
    }
    
    // 处理日期范围
    if (searchForm.date_range && searchForm.date_range.length === 2) {
      params.created_at__gte = searchForm.date_range[0]
      params.created_at__lte = searchForm.date_range[1]
    }
    
    const response = await procurementApi.getPurchaseRequests(params)
    tableData.value = response.results || []
    total.value = response.count || 0
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadDepartments = async () => {
  try {
    const response = await userApi.getDepartments()
    departments.value = response.results || []
  } catch (error) {
    console.error('加载部门数据失败:', error)
  }
}

const loadUserRole = async () => {
  try {
    const profile = await authApi.getProfile()
    userProfile.value = profile
    
    // 判断用户角色
    if (profile.user?.is_superuser || profile.user?.is_staff) {
      userRole.value = 'admin'
    } else {
      // 检查是否为部门经理
      const userDept = profile.profile?.department
      
      if (userDept) {
        const dept = departments.value.find(d => d.code === userDept)
        
        if (dept && dept.manager === profile.user?.username) {
          userRole.value = 'manager'
        } else {
          userRole.value = 'user'
        }
      } else {
        userRole.value = 'user'
      }
    }
  } catch (error) {
    console.error('加载用户角色失败:', error)
    userRole.value = 'user'
  }
}

const getRoleTitle = () => {
  const roleMap = {
    'admin': '管理员权限',
    'manager': '部门经理权限',
    'user': '普通用户权限'
  }
  return roleMap[userRole.value] || '用户权限'
}

const getRoleDescription = () => {
  const descMap = {
    'admin': '您可以查看系统中所有的采购申请记录，并可以审批所有申请',
    'manager': `您可以查看${userProfile.value?.profile?.department || '本部门'}的所有采购申请记录，并可以审批本部门的申请`,
    'user': '您只能查看自己提交的采购申请记录'
  }
  return descMap[userRole.value] || '您只能查看自己提交的采购申请记录'
}

const hasApprovalPermission = (row) => {
  // 管理员可以审批所有申请
  if (userRole.value === 'admin') {
    return true
  }
  
  // 部门经理只能审批本部门的申请
  if (userRole.value === 'manager') {
    return row.department === userProfile.value?.profile?.department
  }
  
  return false
}

const canApproveCurrentStatus = (row) => {
  // 管理员只能审批部门已批准的申请
  if (userRole.value === 'admin') {
    return row.status === 'DEPT_APPROVED'
  }
  
  // 部门经理只能审批已提交的申请
  if (userRole.value === 'manager') {
    return row.status === 'SUBMITTED'
  }
  
  return false
}

const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    request_no: '',
    department: '',
    status: '',
    date_range: []
  })
  currentPage.value = 1
  loadData()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

const handleRowClick = (row) => {
  handleView(row)
}

const handleView = (row) => {
  currentRequest.value = row
  detailVisible.value = true
}

const handleEdit = (row) => {
  // 跳转到编辑页面
  ElMessage.info('编辑功能待实现')
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除申请单 "${row.request_no}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await procurementApi.deletePurchaseRequest(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleAdminDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除申请单 "${row.request_no}" 吗？\n\n注意：此操作将永久删除该记录，无法恢复！`, 
      '管理员删除确认', 
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: false
      }
    )
    
    await procurementApi.deletePurchaseRequest(row.id)
    ElMessage.success('记录删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('管理员删除失败:', error)
      ElMessage.error('删除失败: ' + (error.message || '未知错误'))
    }
  }
}

const handleApprove = async (row) => {
  const approvalText = userRole.value === 'admin' ? '管理员批准' : '部门批准'
  try {
    await ElMessageBox.confirm(`确定要${approvalText}申请单 "${row.request_no}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await procurementApi.approvePurchaseRequest(row.id)
    ElMessage.success(`${approvalText}成功`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${approvalText}失败`)
    }
  }
}

const handleReject = (row) => {
  currentRejectRequest.value = row
  rejectForm.rejected_reason = ''
  rejectVisible.value = true
}

const rejectFormRef = ref(null)
const notesFormRef = ref(null)

const confirmReject = async () => {
  if (!rejectFormRef.value) return
  
  try {
    await rejectFormRef.value.validate()
    rejecting.value = true
    
    await procurementApi.rejectPurchaseRequest(
      currentRejectRequest.value.id, 
      { rejected_reason: rejectForm.rejected_reason }
    )
    
    ElMessage.success('拒绝成功')
    rejectVisible.value = false
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('拒绝失败')
    }
  } finally {
    rejecting.value = false
  }
}

const handleResubmit = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要重新提交申请单 "${row.request_no}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await procurementApi.resubmitPurchaseRequest(row.id)
    ElMessage.success('重新申请成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重新申请失败')
    }
  }
}

const handleEditNotes = (row) => {
  currentNotesRequest.value = row
  notesForm.notes = row.notes || ''
  notesVisible.value = true
}

const confirmUpdateNotes = async () => {
  try {
    updatingNotes.value = true
    
    await procurementApi.updatePurchaseRequestNotes(
      currentNotesRequest.value.id, 
      { notes: notesForm.notes }
    )
    
    ElMessage.success('备注更新成功')
    notesVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error('备注更新失败')
  } finally {
    updatingNotes.value = false
  }
}

// 工具方法
const getDepartmentName = (code) => {
  const dept = departments.value.find(d => d.code === code)
  return dept ? `${dept.code} - ${dept.name}` : code
}

const getStatusType = (status) => {
  const statusMap = {
    'DRAFT': 'info',
    'SUBMITTED': 'warning',
    'DEPT_APPROVED': 'success',
    'ADMIN_APPROVED': 'success',
    'REJECTED': 'danger',
    'ORDERED': 'primary',
    'PARTIAL_RECEIVED': 'warning',
    'RECEIVED': 'success',
    'CLOSED': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'SUBMITTED': '已提交',
    'DEPT_APPROVED': '部门已批准',
    'ADMIN_APPROVED': '管理员已批准',
    'REJECTED': '已拒绝',
    'ORDERED': '已下单',
    'PARTIAL_RECEIVED': '部分到货',
    'RECEIVED': '已到货',
    'CLOSED': '已关闭'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority) => {
  const priorityMap = {
    'LOW': 'info',
    'NORMAL': 'success',
    'HIGH': 'warning',
    'URGENT': 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    'LOW': '低',
    'NORMAL': '普通',
    'HIGH': '高',
    'URGENT': '紧急'
  }
  return priorityMap[priority] || priority
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return parseFloat(amount).toFixed(2)
}

// 生命周期
onMounted(async () => {
  await loadDepartments()
  await loadUserRole()
  loadData()
})
</script>

<style scoped>
.request-records {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.role-alert {
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.request-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card,
.details-card,
.approval-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 10px;
}

.rejected-reason {
  color: #f56c6c;
  font-weight: 500;
}

.approval-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.approval-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.approval-section:last-child {
  margin-bottom: 0;
}

.notes-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  color: #606266;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

.operation-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  align-items: center;
  white-space: nowrap;
}

.operation-buttons .el-button {
  flex-shrink: 0;
  margin: 0;
}
</style>
