<template>
  <div class="bom-create">
    <h1 class="page-title">创建BOM</h1>
    
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="default"
    >
      <!-- 基本信息 -->
      <el-card class="form-card">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="BOM编码" prop="code">
              <el-input 
                v-model="form.code" 
                placeholder="留空自动生成"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BOM名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入BOM名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="成品编码" prop="product_code">
              <el-select
                v-model="form.product_code"
                placeholder="选择成品物料"
                filterable
                remote
                :remote-method="searchProductMaterials"
                :loading="productMaterialsLoading"
                clearable
                style="width: 100%"
                @change="onProductChange"
              >
                <el-option
                  v-for="material in productMaterialOptions"
                  :key="material.code"
                  :label="`${material.code} - ${material.name}`"
                  :value="material.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="openProductSelectionDialog" style="margin-top:">
              从成品库选择
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-form-item label="版本号" prop="version">
              <el-input 
                v-model="form.version" 
                placeholder="例如: 1.0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="BOM类型" prop="bom_type">
              <el-select v-model="form.bom_type" style="width: 100%">
                <el-option label="工程BOM" value="EBOM" />
                <el-option label="制造BOM" value="MBOM" />
                <el-option label="销售BOM" value="SBOM" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="form.status" style="width: 100%">
                <el-option label="草稿" value="DRAFT" />
                <el-option label="待审批" value="PENDING" />
                <el-option label="已批准" value="APPROVED" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="说明">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入BOM说明"
          />
        </el-form-item>
      </el-card>

      <!-- BOM明细 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>BOM明细</span>
            <el-button type="primary" size="small" @click="addItem">
              添加明细
            </el-button>
          </div>
        </template>
        
        <el-table :data="form.items" stripe style="width: 100%">
          <el-table-column prop="item_no" label="项次" width="80">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="物料编码" min-width="150">
            <template #default="{ row, $index }">
              <div style="display: flex; gap: 8px; align-items: center;">
                <el-select
                  v-model="row.material_code"
                  placeholder="选择物料"
                  filterable
                  remote
                  :remote-method="(query) => searchMaterials(query, $index)"
                  clearable
                  style="width: 100%"
                  @change="onMaterialChange(row)"
                >
                  <el-option
                    v-for="material in materialOptions"
                    :key="material.code"
                    :label="`${material.code} - ${material.name}`"
                    :value="material.code"
                  />
                </el-select>
                <el-button 
                  type="success" 
                  size="small" 
                  @click="loadPreferredMaterialsForItem($index)"
                  title="从优选库选择"
                >
                  优选库
                </el-button>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="loadAllMaterialsForItem($index)"
                  title="从物料库选择"
                >
                  物料库
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="物料名称" min-width="150">
            <template #default="{ row }">
              {{ getMaterialNameByCode(row.material_code) }}
            </template>
          </el-table-column>
          <el-table-column label="用量" width="120">
            <template #default="{ row }">
              <el-input-number
                v-model="row.quantity"
                :min="0"
                :precision="6"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="单位" width="80">
            <template #default="{ row }">
              <el-input v-model="row.unit" readonly />
            </template>
          </el-table-column>
          <el-table-column label="单价" width="120">
            <template #default="{ row }">
              <el-input-number
                v-model="row.unit_cost"
                :min="0"
                :precision="4"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="虚拟件" width="80">
            <template #default="{ row }">
              <el-checkbox v-model="row.phantom" disabled />
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150">
            <template #default="{ row }">
              <el-input v-model="row.notes" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                @click="removeItem($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="$router.back()">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          保存
        </el-button>
      </div>
    </el-form>

    <!-- 成品选择对话框 -->
    <el-dialog
      v-model="productSelectionDialogVisible"
      :title="productSelectionDialogTitle"
      width="80%"
      @close="handleProductSelectionDialogClose"
    >
      <!-- 查询条件 -->
      <div class="search-section" style="margin-bottom: 20px;">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="productDialogSearch.search"
              placeholder="搜索成品编码或名称"
              clearable
              @clear="handleProductDialogSearch"
              @keyup.enter="handleProductDialogSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="productDialogSearch.middle_category"
              placeholder="选择中类"
              clearable
              @change="handleProductDialogMiddleCategoryChange"
            >
              <el-option
                v-for="category in productDialogMiddleCategories"
                :key="category.code"
                :label="category.name"
                :value="category.code"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="productDialogSearch.category_code"
              placeholder="选择子类"
              clearable
              :disabled="!productDialogSearch.middle_category"
              @change="handleProductDialogSearch"
            >
              <el-option
                v-for="category in productDialogSubCategories"
                :key="category.code"
                :label="category.name"
                :value="category.code"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleProductDialogSearch">
              查询
            </el-button>
            <el-button @click="handleProductDialogReset">
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>

      <el-table
        v-loading="productDialogLoading"
        :data="productDialogData"
        stripe
        style="width: 100%"
        @selection-change="handleProductDialogSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="成品编码" width="150" />
        <el-table-column prop="name" label="成品名称" min-width="200" />
        <el-table-column prop="category_name" label="分类" width="200" />
        <el-table-column prop="specification" label="规格" min-width="200" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper" style="margin-top: 20px;">
        <el-pagination
          v-model:current-page="productDialogPagination.page"
          v-model:page-size="productDialogPagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="productDialogPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleProductDialogSizeChange"
          @current-change="handleProductDialogPageChange"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="productSelectionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSelectProduct" :loading="productDialogSubmitting">
            选择成品
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 物料选择对话框 -->
    <el-dialog
      v-model="materialDialogVisible"
      :title="materialDialogTitle"
      width="80%"
      @close="handleMaterialDialogClose"
    >
      <!-- 查询条件 -->
      <div class="search-section" style="margin-bottom: 20px;">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="materialDialogSearch.search"
              placeholder="搜索物料编码或名称"
              clearable
              @clear="handleMaterialDialogSearch"
              @keyup.enter="handleMaterialDialogSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="materialDialogSearch.major_category"
              placeholder="选择大类"
              clearable
              @change="handleMaterialDialogMajorCategoryChange"
            >
              <el-option
                v-for="category in materialDialogMajorCategories"
                :key="category.code"
                :label="category.name"
                :value="category.code"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="materialDialogSearch.middle_category"
              placeholder="选择中类"
              clearable
              :disabled="!materialDialogSearch.major_category"
              @change="handleMaterialDialogMiddleCategoryChange"
            >
              <el-option
                v-for="category in materialDialogMiddleCategories"
                :key="category.code"
                :label="category.name"
                :value="category.code"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="materialDialogSearch.category_code"
              placeholder="选择子类"
              clearable
              :disabled="!materialDialogSearch.middle_category"
              @change="handleMaterialDialogSearch"
            >
              <el-option
                v-for="category in materialDialogSubCategories"
                :key="category.code"
                :label="category.name"
                :value="category.code"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleMaterialDialogSearch">
              查询
            </el-button>
            <el-button @click="handleMaterialDialogReset">
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>

      <el-table
        v-loading="materialDialogLoading"
        :data="materialDialogData"
        stripe
        style="width: 100%"
        @selection-change="handleMaterialDialogSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="物料编码" width="150" />
        <el-table-column prop="name" label="物料名称" min-width="200" />
        <el-table-column prop="category_name" label="分类" width="200" />
        <el-table-column prop="specification" label="规格" min-width="200" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper" style="margin-top: 20px;">
        <el-pagination
          v-model:current-page="materialDialogPagination.page"
          v-model:page-size="materialDialogPagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="materialDialogPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleMaterialDialogSizeChange"
          @current-change="handleMaterialDialogPageChange"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="materialDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSelectMaterial" :loading="materialDialogSubmitting">
            选择物料
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { bomApi, materialsApi } from '@/api'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const materialsLoading = ref(false)
const materialOptions = ref([])

// 成品相关数据
const productMaterialsLoading = ref(false)
const productMaterialOptions = ref([])

// 成品选择对话框相关数据
const productSelectionDialogVisible = ref(false)
const productDialogLoading = ref(false)
const productDialogSubmitting = ref(false)
const productDialogData = ref([])
const productDialogSelectedRows = ref([])
const productDialogCategories = ref([]) // 用于存储所有成品分类
const productDialogPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})
const productDialogType = ref('product') // 'product', 'preferred', 'all'
const productDialogItemIndex = ref(-1) // 用于记录当前编辑的是哪个明细项
const productDialogSearch = reactive({
  search: '',
  category_code: '', // 子类编码
  middle_category: '' // 中类编码
})

// 物料选择对话框相关数据
const materialDialogVisible = ref(false)
const materialDialogLoading = ref(false)
const materialDialogSubmitting = ref(false)
const materialDialogData = ref([])
const materialDialogSelectedRows = ref([])
const materialDialogCategories = ref([]) // 用于存储所有分类
const materialDialogPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})
const materialDialogType = ref('product') // 'product', 'preferred', 'all'
const materialDialogItemIndex = ref(-1) // 用于记录当前编辑的是哪个明细项
const materialDialogSearch = reactive({
  search: '',
  category_code: '',
  major_category: '', // 大类编码
  middle_category: '' // 中类编码
})

const form = reactive({
  code: '',
  name: '',
  product_code: '',
  version: '1.0',
  bom_type: 'EBOM',
  status: 'DRAFT',
  description: '',
  items: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入BOM名称', trigger: 'blur' }
  ],
  product_code: [
    { required: true, message: '请选择成品物料', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ]
}

// 计算属性
const materialDialogTitle = computed(() => {
  if (materialDialogType.value === 'product') {
    return '从成品库选择物料'
  } else if (materialDialogType.value === 'preferred') {
    return '从优选库选择物料'
  } else {
    return '从物料库选择物料'
  }
})

// 获取成品对话框中类
const productDialogMiddleCategories = computed(() => {
  if (!productDialogCategories.value.length) {
    return []
  }
  
  // 从所有成品分类中提取中类（level=2或parent_code='1'）
  return productDialogCategories.value.filter(cat => 
    cat.level === 2 || cat.parent_code === '1'
  )
})

// 获取成品对话框子类
const productDialogSubCategories = computed(() => {
  if (!productDialogSearch.middle_category || !productDialogCategories.value.length) {
    return []
  }
  
  // 根据选中的中类，找到对应的子类
  const middleCategory = productDialogCategories.value.find(cat => 
    cat.code === productDialogSearch.middle_category
  )
  
  if (middleCategory && middleCategory.children) {
    return middleCategory.children
  }
  
  return []
})

// 获取物料对话框大类
const materialDialogMajorCategories = computed(() => {
  if (!materialDialogCategories.value.length) {
    return []
  }
  
  // 从所有分类中提取大类（level=1或parent_code为空）
  return materialDialogCategories.value.filter(cat => 
    cat.level === 1 || !cat.parent_code
  )
})

// 获取物料对话框中类
const materialDialogMiddleCategories = computed(() => {
  if (!materialDialogSearch.major_category || !materialDialogCategories.value.length) {
    return []
  }
  
  // 根据选中的大类，找到对应的中类
  const majorCategory = materialDialogCategories.value.find(cat => 
    cat.code === materialDialogSearch.major_category
  )
  
  if (majorCategory && majorCategory.children) {
    return majorCategory.children
  }
  
  return []
})

// 获取物料对话框子类
const materialDialogSubCategories = computed(() => {
  if (!materialDialogSearch.middle_category || !materialDialogCategories.value.length) {
    return []
  }
  
  // 根据选中的中类，找到对应的子类
  const middleCategory = materialDialogCategories.value.find(cat => 
    cat.code === materialDialogSearch.middle_category
  )
  
  if (middleCategory && middleCategory.children) {
    return middleCategory.children
  }
  
  return []
})

const productSelectionDialogTitle = computed(() => {
  return '从成品库选择成品'
})

// 方法
const searchMaterials = async (query, index) => {
  if (!query) return
  
  materialsLoading.value = true
  try {
    const params = {
      search: query,
      status: 'ACTIVE',
      page_size: 20
    }
    
    // 根据当前编辑的明细项，设置不同的分类筛选
    if (index !== undefined) {
      const item = form.items[index]
      if (item.material_code) {
        const material = materialOptions.value.find(m => m.code === item.material_code)
        if (material && material.category_code) {
          params.category_code = material.category_code
        }
      }
    }
    
    const response = await materialsApi.getMaterials(params)
    materialOptions.value = response.results || []
    
    // 缓存搜索到的物料信息
    if (response.results) {
      response.results.forEach(material => {
        selectedMaterials.value.set(material.code, material)
      })
    }
  } catch (error) {
    ElMessage.error('搜索物料失败')
  } finally {
    materialsLoading.value = false
  }
}

// 搜索成品物料
const searchProductMaterials = async (query) => {
  if (!query) return
  
  productMaterialsLoading.value = true
  try {
    // 构建所有成品分类编码
    const productCategoryCodes = ['1'] // 成品大类
    
    // 如果有成品分类数据，添加中类和子类编码
    if (productDialogCategories.value.length > 0) {
      productDialogCategories.value.forEach(category => {
        productCategoryCodes.push(category.code)
        if (category.children) {
          category.children.forEach(subCategory => {
            productCategoryCodes.push(subCategory.code)
          })
        }
      })
    }
    
    const response = await materialsApi.getMaterials({ 
      search: query,
      status: 'ACTIVE',
      category_codes: productCategoryCodes.join(','), // 搜索所有成品分类
      page_size: 20
    })
    productMaterialOptions.value = response.results || []
  } catch (error) {
    ElMessage.error('搜索成品失败')
  } finally {
    productMaterialsLoading.value = false
  }
}

// 加载分类数据
const loadCategories = async () => {
  try {
    const response = await materialsApi.getCategories()
    materialDialogCategories.value = response.results || []
    productDialogCategories.value = response.results || []
  } catch (error) {
    ElMessage.error('加载分类失败')
  }
}

// 加载所有分类数据（用于物料对话框）
const loadAllCategories = async () => {
  try {
    const response = await materialsApi.getCategoryTree()
    const categoryTree = response || []
    
    // 将所有分类展平为一维数组
    const allCategories = []
    
    const flattenCategories = (categories) => {
      categories.forEach(category => {
        allCategories.push(category)
        if (category.children && category.children.length > 0) {
          flattenCategories(category.children)
        }
      })
    }
    
    flattenCategories(categoryTree)
    materialDialogCategories.value = allCategories
  } catch (error) {
    ElMessage.error('加载分类失败')
  }
}

// 加载成品分类数据（只包含成品的中类和小类）
const loadProductCategories = async () => {
  try {
    const response = await materialsApi.getCategoryTree()
    const categoryTree = response || []
    
    // 找到成品大类（编码为'1'）
    const productMajorCategory = categoryTree.find(cat => cat.code === '1')
    if (productMajorCategory && productMajorCategory.children) {
      // 直接使用成品大类的子分类（中类）
      productDialogCategories.value = productMajorCategory.children
    } else {
      productDialogCategories.value = []
    }
  } catch (error) {
    ElMessage.error('加载成品分类失败')
  }
}

// 加载成品库物料
const loadProductMaterials = async () => {
  materialDialogType.value = 'product'
  materialDialogVisible.value = true
  materialDialogPagination.page = 1
  await loadCategories()
  await loadMaterialDialogData()
}

// 为指定明细项加载优选库物料
const loadPreferredMaterialsForItem = async (itemIndex) => {
  materialDialogType.value = 'preferred'
  materialDialogItemIndex.value = itemIndex
  materialDialogVisible.value = true
  materialDialogPagination.page = 1
  
  // 清空查询条件，确保默认展示所有优选物料
  materialDialogSearch.search = ''
  materialDialogSearch.category_code = ''
  materialDialogSearch.major_category = ''
  materialDialogSearch.middle_category = ''
  
  try {
    // 先加载所有分类
    await loadAllCategories()
    // 再加载物料数据
    await loadMaterialDialogData()
  } catch (error) {
    ElMessage.error('加载优选物料数据失败')
  }
}

// 为指定明细项加载所有物料
const loadAllMaterialsForItem = async (itemIndex) => {
  materialDialogType.value = 'all'
  materialDialogItemIndex.value = itemIndex
  materialDialogVisible.value = true
  materialDialogPagination.page = 1
  
  // 清空查询条件，确保默认展示所有物料
  materialDialogSearch.search = ''
  materialDialogSearch.category_code = ''
  materialDialogSearch.major_category = ''
  materialDialogSearch.middle_category = ''
  
  try {
    // 先加载所有分类
    await loadAllCategories()
    // 再加载物料数据
    await loadMaterialDialogData()
  } catch (error) {
    ElMessage.error('加载物料数据失败')
  }
}

// 加载成品对话框数据
const loadProductDialogData = async () => {
  productDialogLoading.value = true
  try {
    const params = {
      page: productDialogPagination.page,
      page_size: productDialogPagination.size,
      status: 'ACTIVE'
    }
    
    // 如果指定了具体的中类，则获取该中类下所有子类的编码
    if (productDialogSearch.middle_category) {
      const middleCategory = productDialogCategories.value.find(cat => 
        cat.code === productDialogSearch.middle_category
      )
      
      if (middleCategory && middleCategory.children && middleCategory.children.length > 0) {
        // 收集该中类下所有子类的编码
        const subCategoryCodes = middleCategory.children.map(child => child.code)
        // 如果没有指定具体子类，则查询该中类下所有子类的物料
        if (!productDialogSearch.category_code) {
          params.category_codes = subCategoryCodes.join(',')
        } else {
          // 如果指定了具体子类，则使用该子类编码
          params.category_code = productDialogSearch.category_code
        }
      } else if (middleCategory) {
        // 如果中类没有子类，则直接使用中类编码
        params.category_code = middleCategory.code
      }
    } else {
      // 如果没有选择中类，则查询所有成品分类的物料
      const productCategoryCodes = ['1'] // 成品大类
      productDialogCategories.value.forEach(category => {
        productCategoryCodes.push(category.code)
        if (category.children) {
          category.children.forEach(child => {
            productCategoryCodes.push(child.code)
          })
        }
      })
      params.category_codes = productCategoryCodes.join(',')
    }
    
    // 添加查询条件
    if (productDialogSearch.search) {
      params.search = productDialogSearch.search
    }
    
    const response = await materialsApi.getMaterials(params)
    productDialogData.value = response.results || []
    productDialogPagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载成品失败')
  } finally {
    productDialogLoading.value = false
  }
}

// 处理成品对话框选择变化
const handleProductDialogSelectionChange = (rows) => {
  productDialogSelectedRows.value = rows
}

// 处理成品对话框分页
const handleProductDialogPageChange = () => {
  loadProductDialogData()
}

const handleProductDialogSizeChange = () => {
  productDialogPagination.page = 1
  loadProductDialogData()
}

// 处理成品对话框查询
const handleProductDialogSearch = () => {
  productDialogPagination.page = 1
  loadProductDialogData()
}

// 处理成品对话框重置
const handleProductDialogReset = () => {
  productDialogSearch.search = ''
  productDialogSearch.category_code = ''
  productDialogSearch.middle_category = ''
  productDialogPagination.page = 1
  loadProductDialogData()
}

// 处理成品对话框中类选择变化
const handleProductDialogMiddleCategoryChange = () => {
  productDialogSearch.category_code = '' // 清空子类，以便重新加载
  productDialogPagination.page = 1
  loadProductDialogData()
}

// 处理成品对话框关闭
const handleProductSelectionDialogClose = () => {
  productDialogSelectedRows.value = []
  productDialogItemIndex.value = -1
  // 重置查询条件
  productDialogSearch.search = ''
  productDialogSearch.category_code = ''
  productDialogSearch.middle_category = ''
}

// 处理选择成品
const handleSelectProduct = () => {
  if (productDialogSelectedRows.value.length === 0) {
    ElMessage.warning('请选择成品')
    return
  }
  
  if (productDialogSelectedRows.value.length > 1) {
    ElMessage.warning('只能选择一个成品')
    return
  }
  
  const selectedProduct = productDialogSelectedRows.value[0]
  
  // 选择成品物料
  form.product_code = selectedProduct.code
  // 更新成品选项
  productMaterialOptions.value = [selectedProduct]
  
  productSelectionDialogVisible.value = false
  ElMessage.success('成品选择成功')
}

// 处理成品编码变化
const onProductChange = (code) => {
  const product = productMaterialOptions.value.find(p => p.code === code)
  if (product) {
    form.product_code = product.code
  }
}

// 打开成品选择对话框
const openProductSelectionDialog = async () => {
  productDialogType.value = 'product'
  productSelectionDialogVisible.value = true
  productDialogPagination.page = 1
  
  // 清空查询条件，确保默认展示所有成品
  productDialogSearch.search = ''
  productDialogSearch.category_code = ''
  productDialogSearch.middle_category = ''
  
  // 清空成品库物料选项，避免重复加载
  productMaterialOptions.value = []
  
  try {
    // 先加载成品分类
    await loadProductCategories()
    // 再加载成品数据
    await loadProductDialogData()
  } catch (error) {
    ElMessage.error('加载成品数据失败')
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    DRAFT: 'info',
    ACTIVE: 'success',
    INACTIVE: 'warning',
    OBSOLETE: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    DRAFT: '草稿',
    ACTIVE: '活跃',
    INACTIVE: '停用',
    OBSOLETE: '废弃'
  }
  return statusMap[status] || '未知'
}

// 存储所有已选择的物料信息，用于显示物料名称
const selectedMaterials = ref(new Map())

const getMaterialNameByCode = (code) => {
  // 首先从已选择的物料中查找
  if (selectedMaterials.value.has(code)) {
    return selectedMaterials.value.get(code).name
  }
  
  // 然后从当前选项列表中查找
  const material = materialOptions.value.find(m => m.code === code)
  if (material) {
    // 缓存到已选择的物料中
    selectedMaterials.value.set(code, material)
    return material.name
  }
  
  // 最后从对话框数据中查找
  const dialogMaterial = materialDialogData.value.find(m => m.code === code)
  if (dialogMaterial) {
    selectedMaterials.value.set(code, dialogMaterial)
    return dialogMaterial.name
  }
  
  return ''
}

const onMaterialChange = (row) => {
  const material = materialOptions.value.find(m => m.code === row.material_code)
  if (material) {
    row.unit = material.unit || ''
    row.phantom = material.is_virtual || false
    // 缓存物料信息
    selectedMaterials.value.set(material.code, material)
  }
}

const addItem = () => {
  form.items.push({
    item_no: form.items.length + 1,
    material_code: '',
    quantity: 1,
    unit: '',
    unit_cost: null,
    phantom: false,
    notes: ''
  })
}

const removeItem = (index) => {
  form.items.splice(index, 1)
  // 重新排序项次号
  form.items.forEach((item, idx) => {
    item.item_no = idx + 1
  })
}

const validateForm = () => {
  if (!form.name) {
    ElMessage.error('请输入BOM名称')
    return false
  }
  if (!form.product_code) {
    ElMessage.error('请选择成品物料')
    return false
  }
  if (form.items.length === 0) {
    ElMessage.error('请至少添加一个BOM明细')
    return false
  }
  
  for (let i = 0; i < form.items.length; i++) {
    const item = form.items[i]
    if (!item.material_code) {
      ElMessage.error(`第${i + 1}行：请选择物料`)
      return false
    }
    if (!item.quantity || item.quantity <= 0) {
      ElMessage.error(`第${i + 1}行：请输入正确的用量`)
      return false
    }
  }
  
  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return
  
  submitting.value = true
  try {
    // 准备提交数据
    const submitData = {
      ...form
    }
    
    // 如果没有提供BOM编码，让后端自动生成
    if (!submitData.code) {
      delete submitData.code
    }
    
    // 确保BOM明细数据格式正确
    if (submitData.items && submitData.items.length > 0) {
      submitData.bomitem_set = submitData.items.map((item, index) => ({
        ...item,
        item_no: index + 1, // 确保项次号正确
        quantity: parseFloat(item.quantity) || 0,
        unit_cost: item.unit_cost ? parseFloat(item.unit_cost) : null,
        phantom: Boolean(item.phantom)
      }))
      delete submitData.items // 删除items字段，使用bomitem_set
    }
    
    // 提交BOM数据
    
    await bomApi.createBOM(submitData)
    ElMessage.success('BOM创建成功')
    router.push('/bom')
  } catch (error) {
    console.error('BOM创建失败:', error)
    if (error.response?.data) {
      console.error('错误详情:', error.response.data)
      ElMessage.error('创建失败: ' + JSON.stringify(error.response.data))
    } else {
      ElMessage.error('创建失败: ' + (error.message || '未知错误'))
    }
  } finally {
    submitting.value = false
  }
}

// 复制BOM功能
const loadCopyData = async (bomId) => {
  try {
    const bom = await bomApi.getBOM(bomId)
    const items = await bomApi.getBOMItems(bomId)
    
    // 复制基本信息
    Object.assign(form, {
      code: '',  // 清空编码，让系统自动生成
      name: bom.name + ' (副本)',
      product_code: bom.product_code,
      version: bom.version,
      bom_type: bom.bom_type,
      status: 'DRAFT',
      description: bom.description,
      items: items.map((item, index) => ({
        item_no: index + 1,
        material_code: item.material_code,
        quantity: item.quantity,
        unit: item.unit,
        unit_cost: item.unit_cost,
        phantom: item.phantom,
        notes: item.notes
      }))
    })
  } catch (error) {
    ElMessage.error('加载复制数据失败')
  }
}

// 加载物料对话框数据
const loadMaterialDialogData = async () => {
  materialDialogLoading.value = true
  try {
    const params = {
      page: materialDialogPagination.page,
      page_size: materialDialogPagination.size,
      status: 'ACTIVE'
    }
    
    // 根据对话框类型设置不同的筛选条件
    if (materialDialogType.value === 'product') {
      // 成品库：只显示成品物料（非虚拟件）
      params.is_virtual = false
    } else if (materialDialogType.value === 'preferred') {
      // 优选库：只显示优选库物料
      params.is_preferred = true
    }
    // 如果是 'all' 类型，不添加额外筛选条件，显示所有活跃物料
    
    // 处理分类筛选
    if (materialDialogSearch.major_category) {
      // 如果选择了大类
      const majorCategory = materialDialogCategories.value.find(cat => 
        cat.code === materialDialogSearch.major_category
      )
      
      if (majorCategory && majorCategory.children && majorCategory.children.length > 0) {
        // 收集该大类下所有中类和子类的编码
        const categoryCodes = [majorCategory.code]
        
        majorCategory.children.forEach(middleCategory => {
          categoryCodes.push(middleCategory.code)
          if (middleCategory.children) {
            middleCategory.children.forEach(subCategory => {
              categoryCodes.push(subCategory.code)
            })
          }
        })
        
        if (materialDialogSearch.middle_category) {
          // 如果选择了中类
          const middleCategory = majorCategory.children.find(cat => 
            cat.code === materialDialogSearch.middle_category
          )
          
          if (middleCategory && middleCategory.children && middleCategory.children.length > 0) {
            // 收集该中类下所有子类的编码
            const subCategoryCodes = middleCategory.children.map(child => child.code)
            
            if (materialDialogSearch.category_code) {
              // 如果选择了具体子类
              params.category_code = materialDialogSearch.category_code
            } else {
              // 查询该中类下所有子类的物料
              params.category_codes = subCategoryCodes.join(',')
            }
          } else if (middleCategory) {
            // 如果中类没有子类，直接使用中类编码
            params.category_code = middleCategory.code
          }
        } else {
          // 查询该大类下所有分类的物料
          params.category_codes = categoryCodes.join(',')
        }
      } else if (majorCategory) {
        // 如果大类没有子分类，直接使用大类编码
        params.category_code = majorCategory.code
      }
    }
    
    // 添加查询条件
    if (materialDialogSearch.search) {
      params.search = materialDialogSearch.search
    }
    
    const response = await materialsApi.getMaterials(params)
    materialDialogData.value = response.results || []
    materialDialogPagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载物料失败')
  } finally {
    materialDialogLoading.value = false
  }
}

// 处理物料对话框选择变化
const handleMaterialDialogSelectionChange = (rows) => {
  materialDialogSelectedRows.value = rows
}

// 处理物料对话框分页
const handleMaterialDialogPageChange = () => {
  loadMaterialDialogData()
}

const handleMaterialDialogSizeChange = () => {
  materialDialogPagination.page = 1
  loadMaterialDialogData()
}

// 处理物料对话框查询
const handleMaterialDialogSearch = () => {
  materialDialogPagination.page = 1
  loadMaterialDialogData()
}

// 处理物料对话框重置
const handleMaterialDialogReset = () => {
  materialDialogSearch.search = ''
  materialDialogSearch.category_code = ''
  materialDialogSearch.major_category = ''
  materialDialogSearch.middle_category = ''
  materialDialogPagination.page = 1
  loadMaterialDialogData()
}

// 处理物料对话框大类选择变化
const handleMaterialDialogMajorCategoryChange = () => {
  materialDialogSearch.middle_category = '' // 清空中类，以便重新加载
  materialDialogSearch.category_code = '' // 清空子类，以便重新加载
  materialDialogPagination.page = 1
  loadMaterialDialogData()
}

// 处理物料对话框中类选择变化
const handleMaterialDialogMiddleCategoryChange = () => {
  materialDialogSearch.category_code = '' // 清空子类，以便重新加载
  materialDialogPagination.page = 1
  loadMaterialDialogData()
}

// 处理物料对话框关闭
const handleMaterialDialogClose = () => {
  materialDialogSelectedRows.value = []
  materialDialogItemIndex.value = -1
  // 重置查询条件
  materialDialogSearch.search = ''
  materialDialogSearch.category_code = ''
  materialDialogSearch.major_category = ''
  materialDialogSearch.middle_category = ''
}

// 处理选择物料
const handleSelectMaterial = () => {
  if (materialDialogSelectedRows.value.length === 0) {
    ElMessage.warning('请选择物料')
    return
  }
  
  if (materialDialogSelectedRows.value.length > 1) {
    ElMessage.warning('只能选择一个物料')
    return
  }
  
  const selectedMaterial = materialDialogSelectedRows.value[0]
  
  // 选择明细物料
  const itemIndex = materialDialogItemIndex.value
  if (itemIndex >= 0 && itemIndex < form.items.length) {
    form.items[itemIndex].material_code = selectedMaterial.code
    // 直接设置物料信息，不依赖onMaterialChange
    form.items[itemIndex].unit = selectedMaterial.unit || ''
    form.items[itemIndex].phantom = selectedMaterial.is_virtual || false
    // 缓存物料信息
    selectedMaterials.value.set(selectedMaterial.code, selectedMaterial)
  }
  
  materialDialogVisible.value = false
  ElMessage.success('物料选择成功')
}

// 生命周期
onMounted(async () => {
  // 加载分类数据
  await loadCategories()
  // 加载成品分类数据
  await loadProductCategories()
  
  // 如果有复制参数，加载复制数据
  if (route.query.copyFrom) {
    loadCopyData(route.query.copyFrom)
  }
})
</script>

<style scoped>
.bom-create {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>