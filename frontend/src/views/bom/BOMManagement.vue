<template>
  <div class="bom-management">
    <h1 class="page-title">BOM管理</h1>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索BOM编码或名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.bom_type"
            placeholder="选择类型"
            clearable
            @change="handleSearch"
          >
            <el-option label="工程BOM" value="EBOM" />
            <el-option label="制造BOM" value="MBOM" />
            <el-option label="销售BOM" value="SBOM" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="草稿" value="DRAFT" />
            <el-option label="待审批" value="PENDING" />
            <el-option label="已批准" value="APPROVED" />
            <el-option label="生效中" value="ACTIVE" />
            <el-option label="停用" value="INACTIVE" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="success" :icon="Plus" @click="$router.push('/bom/create')">
            创建BOM
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="code" label="BOM编码" width="200" />
        <el-table-column prop="name" label="BOM名称" min-width="200" />
        <el-table-column prop="product_code" label="成品编码" width="150" />
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="bom_type" label="类型" width="100">
          <template #default="{ row }">
            {{ getBOMTypeText(row.bom_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_items" label="明细数" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { bomApi } from '@/api'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])

const searchForm = reactive({
  search: '',
  bom_type: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.search || undefined,
      bom_type: searchForm.bom_type || undefined,
      status: searchForm.status || undefined
    }
    
    const response = await bomApi.getBOMs(params)
    tableData.value = response.results || []
    pagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    bom_type: '',
    status: ''
  })
  handleSearch()
}

const handleView = (row) => {
  router.push(`/bom/view/${row.id}`)
}

const handleEdit = (row) => {
  router.push(`/bom/edit/${row.id}`)
}



const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个BOM吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await bomApi.deleteBOM(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}

const getBOMTypeText = (type) => {
  const typeMap = {
    EBOM: '工程BOM',
    MBOM: '制造BOM',
    SBOM: '销售BOM'
  }
  return typeMap[type] || '未知'
}

const getStatusType = (status) => {
  const statusMap = {
    DRAFT: 'info',
    PENDING: 'warning',
    APPROVED: 'primary',
    ACTIVE: 'success',
    INACTIVE: 'warning',
    OBSOLETE: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    DRAFT: '草稿',
    PENDING: '待审批',
    APPROVED: '已批准',
    ACTIVE: '生效中',
    INACTIVE: '停用',
    OBSOLETE: '废弃'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.bom-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>