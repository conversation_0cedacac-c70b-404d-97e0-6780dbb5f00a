<template>
  <div class="bom-edit">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h1 class="page-title">编辑BOM</h1>
    </div>
    
    <div v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="default"
      >
        <!-- 基本信息 -->
        <el-card class="form-card">
          <template #header>
            <span>基本信息</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="BOM编码" prop="code">
                <el-input 
                  v-model="form.code" 
                  readonly
                  placeholder="BOM编码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="BOM名称" prop="name">
                <el-input 
                  v-model="form.name" 
                  placeholder="请输入BOM名称"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="成品编码" prop="product_code">
                <el-input 
                  v-model="form.product_code" 
                  readonly
                  placeholder="成品编码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="版本号" prop="version">
                <el-input 
                  v-model="form.version" 
                  placeholder="例如: 1.0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="BOM类型" prop="bom_type">
                <el-select v-model="form.bom_type" style="width: 100%">
                  <el-option label="工程BOM" value="EBOM" />
                  <el-option label="制造BOM" value="MBOM" />
                  <el-option label="销售BOM" value="SBOM" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态">
                <el-select v-model="form.status" style="width: 100%">
                  <el-option label="草稿" value="DRAFT" />
                  <el-option label="待审批" value="PENDING" />
                  <el-option label="已批准" value="APPROVED" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="说明">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入BOM说明"
            />
          </el-form-item>
        </el-card>

        <!-- BOM明细 -->
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>BOM明细</span>
              <el-button type="primary" size="small" @click="addItem">
                添加明细
              </el-button>
            </div>
          </template>
          
          <el-table :data="form.items" stripe style="width: 100%">
            <el-table-column prop="item_no" label="项次" width="80">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="物料编码" min-width="150">
              <template #default="{ row, $index }">
                <div style="display: flex; gap: 8px; align-items: center;">
                  <el-select
                    v-model="row.material_code"
                    placeholder="选择物料"
                    filterable
                    remote
                    :remote-method="(query) => searchMaterials(query, $index)"
                    clearable
                    style="width: 100%"
                    @change="onMaterialChange(row)"
                  >
                    <el-option
                      v-for="material in materialOptions"
                      :key="material.code"
                      :label="`${material.code} - ${material.name}`"
                      :value="material.code"
                    />
                  </el-select>
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="loadPreferredMaterialsForItem($index)"
                    title="从优选库选择"
                  >
                    优选库
                  </el-button>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="loadAllMaterialsForItem($index)"
                    title="从物料库选择"
                  >
                    物料库
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="物料名称" min-width="150">
              <template #default="{ row }">
                {{ getMaterialNameByCode(row.material_code) }}
              </template>
            </el-table-column>
            <el-table-column label="用量" width="120">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.quantity"
                  :min="0"
                  :precision="6"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="单位" width="80">
              <template #default="{ row }">
                <el-input v-model="row.unit" readonly />
              </template>
            </el-table-column>
            <el-table-column label="单价" width="120">
              <template #default="{ row }">
                <el-input-number
                  v-model="row.unit_cost"
                  :min="0"
                  :precision="4"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="虚拟件" width="80">
              <template #default="{ row }">
                <el-checkbox v-model="row.phantom" disabled />
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="150">
              <template #default="{ row }">
                <el-input v-model="row.notes" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeItem($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="$router.back()">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="handleSubmit">
            保存
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { bomApi, materialsApi } from '@/api'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const materialsLoading = ref(false)
const materialOptions = ref([])

// 存储所有已选择的物料信息，用于显示物料名称
const selectedMaterials = ref(new Map())

const form = reactive({
  code: '',
  name: '',
  product_code: '',
  version: '1.0',
  bom_type: 'EBOM',
  status: 'DRAFT',
  description: '',
  items: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入BOM名称', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ]
}

// 方法
const loadBOMData = async () => {
  const bomId = route.params.id
  if (!bomId) {
    ElMessage.error('BOM ID不存在')
    return
  }
  
  loading.value = true
  try {
    const bom = await bomApi.getBOM(bomId)
    const items = await bomApi.getBOMItems(bomId)
    
    // 填充表单数据
    Object.assign(form, {
      code: bom.code,
      name: bom.name,
      product_code: bom.product_code,
      version: bom.version,
      bom_type: bom.bom_type,
      status: bom.status,
      description: bom.description,
      items: items.map((item, index) => ({
        item_no: index + 1,
        material_code: item.material_code,
        quantity: item.quantity,
        unit: item.unit,
        unit_cost: item.unit_cost,
        phantom: item.phantom,
        notes: item.notes
      }))
    })
    
    // 缓存物料信息
    items.forEach(item => {
      if (item.material_code) {
        selectedMaterials.value.set(item.material_code, {
          code: item.material_code,
          name: item.material_name || '',
          unit: item.unit,
          is_virtual: item.phantom
        })
      }
    })
  } catch (error) {
    ElMessage.error('加载BOM数据失败')
    console.error('加载BOM数据失败:', error)
  } finally {
    loading.value = false
  }
}

const searchMaterials = async (query, index) => {
  if (!query) return
  
  materialsLoading.value = true
  try {
    const params = {
      search: query,
      status: 'ACTIVE',
      page_size: 20
    }
    
    const response = await materialsApi.getMaterials(params)
    materialOptions.value = response.results || []
    
    // 缓存搜索到的物料信息
    if (response.results) {
      response.results.forEach(material => {
        selectedMaterials.value.set(material.code, material)
      })
    }
  } catch (error) {
    ElMessage.error('搜索物料失败')
  } finally {
    materialsLoading.value = false
  }
}

const getMaterialNameByCode = (code) => {
  if (selectedMaterials.value.has(code)) {
    return selectedMaterials.value.get(code).name
  }
  
  const material = materialOptions.value.find(m => m.code === code)
  if (material) {
    selectedMaterials.value.set(code, material)
    return material.name
  }
  
  return ''
}

const onMaterialChange = (row) => {
  const material = materialOptions.value.find(m => m.code === row.material_code)
  if (material) {
    row.unit = material.unit || ''
    row.phantom = material.is_virtual || false
    selectedMaterials.value.set(material.code, material)
  }
}

const addItem = () => {
  form.items.push({
    item_no: form.items.length + 1,
    material_code: '',
    quantity: 1,
    unit: '',
    unit_cost: null,
    phantom: false,
    notes: ''
  })
}

const removeItem = (index) => {
  form.items.splice(index, 1)
  form.items.forEach((item, idx) => {
    item.item_no = idx + 1
  })
}

const validateForm = () => {
  if (!form.name) {
    ElMessage.error('请输入BOM名称')
    return false
  }
  if (form.items.length === 0) {
    ElMessage.error('请至少添加一个BOM明细')
    return false
  }
  
  for (let i = 0; i < form.items.length; i++) {
    const item = form.items[i]
    if (!item.material_code) {
      ElMessage.error(`第${i + 1}行：请选择物料`)
      return false
    }
    if (!item.quantity || item.quantity <= 0) {
      ElMessage.error(`第${i + 1}行：请输入正确的用量`)
      return false
    }
  }
  
  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return
  
  submitting.value = true
  try {
    const submitData = {
      ...form
    }
    
    // 确保BOM明细数据格式正确
    if (submitData.items && submitData.items.length > 0) {
      submitData.bomitem_set = submitData.items.map((item, index) => ({
        ...item,
        item_no: index + 1,
        quantity: parseFloat(item.quantity) || 0,
        unit_cost: item.unit_cost ? parseFloat(item.unit_cost) : null,
        phantom: Boolean(item.phantom)
      }))
      delete submitData.items
    }
    
    await bomApi.updateBOM(route.params.id, submitData)
    ElMessage.success('BOM更新成功')
    router.push('/bom/management')
  } catch (error) {
    console.error('BOM更新失败:', error)
    if (error.response?.data) {
      ElMessage.error('更新失败: ' + JSON.stringify(error.response.data))
    } else {
      ElMessage.error('更新失败: ' + (error.message || '未知错误'))
    }
  } finally {
    submitting.value = false
  }
}

// 简化的物料选择方法
const loadPreferredMaterialsForItem = async (itemIndex) => {
  ElMessage.info('优选库功能开发中...')
}

const loadAllMaterialsForItem = async (itemIndex) => {
  ElMessage.info('物料库功能开发中...')
}

// 生命周期
onMounted(async () => {
  await loadBOMData()
})
</script>

<style scoped>
.bom-edit {
  padding: 0;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>
