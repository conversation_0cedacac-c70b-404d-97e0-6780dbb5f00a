<template>
  <div class="bom-view">
    <div class="page-header">
      <el-button @click="$router.back()" :icon="ArrowLeft">返回</el-button>
      <h1 class="page-title">查看BOM</h1>
    </div>
    
    <div v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-descriptions :column="3" border>
          <el-descriptions-item label="BOM编码">{{ bomData.code }}</el-descriptions-item>
          <el-descriptions-item label="BOM名称">{{ bomData.name }}</el-descriptions-item>
          <el-descriptions-item label="版本号">{{ bomData.version }}</el-descriptions-item>
          <el-descriptions-item label="成品编码">{{ bomData.product_code }}</el-descriptions-item>
          <el-descriptions-item label="成品名称">{{ bomData.product_name }}</el-descriptions-item>
          <el-descriptions-item label="BOM类型">{{ getBOMTypeText(bomData.bom_type) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(bomData.status)">
              {{ getStatusText(bomData.status) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="创建人">{{ bomData.created_by }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(bomData.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(bomData.updated_at) }}</el-descriptions-item>
          <el-descriptions-item label="说明" :span="3">{{ bomData.description || '无' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- BOM明细 -->
      <el-card class="items-card">
        <template #header>
          <span>BOM明细 (共{{ bomData.items?.length || 0 }}项)</span>
        </template>
        
        <el-table :data="bomData.items || []" stripe style="width: 100%">
          <el-table-column prop="item_no" label="项次" width="80" />
          <el-table-column prop="material_code" label="物料编码" width="150" />
          <el-table-column prop="material_name" label="物料名称" min-width="200" />
          <el-table-column prop="quantity" label="用量" width="120" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="unit_cost" label="单价" width="120">
            <template #default="{ row }">
              {{ row.unit_cost ? `¥${row.unit_cost}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="extended_cost" label="扩展成本" width="120">
            <template #default="{ row }">
              {{ row.extended_cost ? `¥${row.extended_cost}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="phantom" label="虚拟件" width="80">
            <template #default="{ row }">
              <el-tag :type="row.phantom ? 'warning' : 'info'" size="small">
                {{ row.phantom ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" min-width="150" />
        </el-table>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleCopy">复制</el-button>
        <el-button type="info" @click="handleCostAnalysis">成本分析</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { bomApi } from '@/api'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const bomData = ref({})

// 方法
const loadBOMData = async () => {
  const bomId = route.params.id
  if (!bomId) {
    ElMessage.error('BOM ID不存在')
    return
  }
  
  loading.value = true
  try {
    const bom = await bomApi.getBOM(bomId)
    const items = await bomApi.getBOMItems(bomId)
    
    bomData.value = {
      ...bom,
      items: items
    }
  } catch (error) {
    ElMessage.error('加载BOM数据失败')
    console.error('加载BOM数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push(`/bom/edit/${route.params.id}`)
}

const handleCopy = () => {
  router.push({
    path: '/bom/create',
    query: { copyFrom: route.params.id }
  })
}

const handleCostAnalysis = async () => {
  try {
    const analysis = await bomApi.getCostAnalysis(route.params.id)
    
    // 构建详细的成本分析信息
    let costInfo = `总成本: ¥${analysis.total_cost}\n明细数: ${analysis.item_count}项\n\n成本明细:\n`
    
    if (analysis.cost_breakdown && analysis.cost_breakdown.length > 0) {
      analysis.cost_breakdown.forEach((item, index) => {
        costInfo += `${index + 1}. ${item.material_name} (${item.material_code})\n`
        costInfo += `   用量: ${item.quantity} ${item.unit || '个'}\n`
        costInfo += `   单价: ¥${item.unit_cost}\n`
        costInfo += `   小计: ¥${item.extended_cost}\n`
        costInfo += `   占比: ${item.percentage.toFixed(2)}%\n\n`
      })
    } else {
      costInfo += '暂无成本明细数据'
    }
    
    ElMessageBox.alert(costInfo, '成本分析', {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: false,
      customClass: 'cost-analysis-dialog'
    })
  } catch (error) {
    ElMessage.error('获取成本分析失败')
  }
}

const getBOMTypeText = (type) => {
  const typeMap = {
    EBOM: '工程BOM',
    MBOM: '制造BOM',
    SBOM: '销售BOM'
  }
  return typeMap[type] || '未知'
}

const getStatusType = (status) => {
  const statusMap = {
    DRAFT: 'info',
    PENDING: 'warning',
    APPROVED: 'primary',
    ACTIVE: 'success',
    INACTIVE: 'warning',
    OBSOLETE: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    DRAFT: '草稿',
    PENDING: '待审批',
    APPROVED: '已批准',
    ACTIVE: '生效中',
    INACTIVE: '停用',
    OBSOLETE: '废弃'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY-MM-DD')
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadBOMData()
})
</script>

<style scoped>
.bom-view {
  padding: 0;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.info-card {
  margin-bottom: 20px;
}

.items-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 30px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

:deep(.cost-analysis-dialog) {
  max-width: 600px;
}

:deep(.cost-analysis-dialog .el-message-box__content) {
  white-space: pre-line;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>
