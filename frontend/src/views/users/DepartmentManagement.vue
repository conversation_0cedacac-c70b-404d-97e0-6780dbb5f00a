<template>
  <div class="department-management">
    <h1 class="page-title">部门管理</h1>
    
    <!-- 操作栏 -->
    <el-card class="toolbar-card">
      <el-button type="primary" :icon="Plus" @click="handleCreate">
        新增部门
      </el-button>
      <el-button :icon="Refresh" @click="loadData">刷新</el-button>
    </el-card>

    <!-- 部门表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        row-key="code"
        stripe
        style="width: 100%"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="code" label="部门编码" width="200" />
        <el-table-column prop="name" label="部门名称" min-width="200" />
        <el-table-column prop="manager" label="部门经理" width="250">
          <template #default="{ row }">
            <span v-if="row.manager">
              {{ getUserDisplayName(row.manager) }}
            </span>
            <el-tag v-else size="small" type="info">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '活跃' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              :type="row.is_active ? 'warning' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.is_active ? '停用' : '激活' }}
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="部门编码" prop="code">
          <el-input v-model="form.code" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="部门经理" prop="manager">
          <el-select 
            v-model="form.manager" 
            placeholder="请选择部门经理"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="user in (isEdit ? departmentUsers : users)"
              :key="user.id"
              :label="`${user.username} - ${getUserDisplayName(user.username)}`"
              :value="user.username"
            />
          </el-select>
          <div v-if="isEdit && departmentUsers.length === 0" class="form-tip">
            该部门暂无用户，请先添加用户到该部门
          </div>
        </el-form-item>
        <el-form-item label="部门描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { userApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const users = ref([])
const departmentUsers = ref([]) // 当前部门的用户列表

const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

const form = reactive({
  code: '',
  name: '',
  description: '',
  manager: ''
})

const formRules = {
  code: [{ required: true, message: '请输入部门编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑部门' : '新增部门')



// 获取用户显示名称
const getUserDisplayName = (username) => {
  // 先从部门用户列表中查找
  let user = departmentUsers.value.find(u => u.username === username)
  if (!user) {
    // 如果没找到，从全局用户列表中查找
    user = users.value.find(u => u.username === username)
  }
  
  if (user) {
    // 去掉姓名中的"经理"两个字
    let firstName = user.first_name || ''
    let lastName = user.last_name || ''
    
    // 去掉"经理"两个字
    firstName = firstName.replace('经理', '')
    lastName = lastName.replace('经理', '')
    
    const fullName = [firstName, lastName].filter(Boolean).join(' ')
    return fullName ? `${fullName}` : user.username
  }
  return username
}

// 加载用户数据
const loadUsers = async () => {
  try {
    const response = await userApi.getUsers()
    users.value = response.results || []
  } catch (error) {
    console.error('加载用户数据失败:', error)
    ElMessage.error('加载用户数据失败')
  }
}

// 加载部门用户数据
const loadDepartmentUsers = async (departmentId) => {
  try {
    const response = await userApi.getDepartmentUsers(departmentId)
    departmentUsers.value = response.results || []
  } catch (error) {
    console.error('加载部门用户数据失败:', error)
    departmentUsers.value = []
  }
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await userApi.getDepartments()
    tableData.value = response.results || []
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

const handleEdit = async (row) => {
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(form, {
    id: row.id,
    code: row.code,
    name: row.name,
    description: row.description || '',
    manager: row.manager || ''
  })
  
  // 加载该部门的用户列表
  await loadDepartmentUsers(row.id)
}

const handleToggleStatus = async (row) => {
  try {
    const action = row.is_active ? '停用' : '激活'
    await ElMessageBox.confirm(`确定要${action}部门 "${row.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    if (row.is_active) {
      await userApi.deactivateDepartment(row.id)
      ElMessage.success('部门已停用')
    } else {
      await userApi.activateDepartment(row.id)
      ElMessage.success('部门已激活')
    }
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除部门 "${row.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userApi.deleteDepartment(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 准备提交数据，确保空值正确处理
    const submitData = {
      code: form.code,
      name: form.name,
      description: form.description,
      manager: form.manager || ''  // 如果为空或undefined，设置为空字符串
    }
    
    if (isEdit.value) {
      await userApi.updateDepartment(form.id, submitData)
      ElMessage.success('更新成功')
    } else {
      await userApi.createDepartment(submitData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('保存失败:', error)
    if (error !== false) {
      ElMessage.error('保存失败: ' + (error.message || '未知错误'))
    }
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  resetForm()
  formRef.value?.resetFields()
}

const resetForm = () => {
  Object.assign(form, {
    code: '',
    name: '',
    description: '',
    manager: ''
  })
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(async () => {
  await loadUsers()
  loadData()
})
</script>

<style scoped>
.department-management {
  padding: 0;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.toolbar-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>
