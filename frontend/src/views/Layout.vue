<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
      <div class="logo">
        <img src="/favicon.ico" alt="PLM" />
        <h1 v-show="!isCollapse">艾控智慧生产系统-PLM</h1>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="false"
        class="sidebar-menu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <el-menu-item index="/">
          <el-icon><DataBoard /></el-icon>
          <template #title>系统概览</template>
        </el-menu-item>
        
        <el-sub-menu index="/materials">
          <template #title>
            <el-icon><Box /></el-icon>
            <span>物料管理</span>
          </template>
          <el-menu-item index="/materials/categories">分类管理</el-menu-item>
          <el-menu-item index="/materials/materials">物料清单</el-menu-item>
          <el-menu-item index="/materials/preferred">优选库</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/bom">
          <template #title>
            <el-icon><List /></el-icon>
            <span>BOM管理</span>
          </template>
          <el-menu-item index="/bom/management">BOM清单</el-menu-item>
          <el-menu-item index="/bom/create">创建BOM</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/inventory">
          <template #title>
            <el-icon><House /></el-icon>
            <span>库存管理</span>
          </template>
          <el-menu-item index="/inventory/management">库存记录</el-menu-item>
          <el-menu-item index="/inventory/warehouse">仓库管理</el-menu-item>
          <el-menu-item index="/inventory/alerts">库存预警</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/procurement">
          <template #title>
            <el-icon><ShoppingCart /></el-icon>
            <span>采购管理</span>
          </template>
          <el-menu-item index="/procurement/requests">采购申请</el-menu-item>
          <el-menu-item index="/procurement/suppliers">供应商管理</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/users">
          <template #title>
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </template>
          <el-menu-item index="/users/management">用户管理</el-menu-item>
          <el-menu-item index="/users/departments">部门管理</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            :icon="isCollapse ? 'Expand' : 'Fold'"
            circle
            @click="toggleSidebar"
          />
          
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item 
              v-for="item in breadcrumbList" 
              :key="item.path"
              :to="{ path: item.path }"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <el-dropdown>
            <div class="user-info">
              <!-- 去掉头像，只显示用户名和下拉箭头 -->
              <span class="username">{{ username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showChangePwdDialog = true">修改密码</el-dropdown-item>
                <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 页面内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>

  <el-dialog v-model="showChangePwdDialog" title="修改密码" width="400px" @close="resetPwdForm">
    <el-form :model="pwdForm" :rules="pwdRules" ref="pwdFormRef" label-width="100px">
      <el-form-item label="原密码" prop="old_password">
        <el-input v-model="pwdForm.old_password" type="password" show-password />
      </el-form-item>
      <el-form-item label="新密码" prop="new_password">
        <el-input v-model="pwdForm.new_password" type="password" show-password />
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirm_password">
        <el-input v-model="pwdForm.confirm_password" type="password" show-password />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showChangePwdDialog = false">取消</el-button>
      <el-button type="primary" @click="handleChangePassword" :loading="pwdSubmitting">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api'

const route = useRoute()
const router = useRouter()

// 响应式数据
const isCollapse = ref(false)
const username = ref('')
const showChangePwdDialog = ref(false)
const pwdForm = ref({ old_password: '', new_password: '', confirm_password: '' })
const pwdFormRef = ref()
const pwdSubmitting = ref(false)
const pwdRules = {
  old_password: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '新密码不能少于6位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== pwdForm.value.new_password) {
          callback(new Error('两次输入的新密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const activeMenu = computed(() => route.path)

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  const breadcrumbs = []
  
  matched.forEach(item => {
    if (item.meta?.title) {
      breadcrumbs.push({
        path: item.path,
        title: item.meta.title
      })
    }
  })
  
  return breadcrumbs
})

// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const logout = async () => {
  try {
    await authApi.logout()
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    // 即使API调用失败，也要清除本地存储并跳转
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    router.push('/login')
  }
}

const handleChangePassword = async () => {
  if (!pwdFormRef.value) return
  try {
    await pwdFormRef.value.validate()
    pwdSubmitting.value = true
    // 获取当前用户id
    const profile = await authApi.getProfile()
    const userId = profile.user?.id
    if (!userId) throw new Error('未获取到用户ID')
    await authApi.changePassword(userId, {
      old_password: pwdForm.value.old_password,
      new_password: pwdForm.value.new_password
    })
    ElMessage.success('密码修改成功，请重新登录')
    showChangePwdDialog.value = false
    logout()
  } catch (error) {
    // 优先显示具体的验证错误信息
    if (error?.response?.data?.details) {
      const details = error.response.data.details
      if (typeof details === 'object') {
        const errorMessages = Object.values(details).flat()
        ElMessage.error(errorMessages.join(', '))
      } else {
        ElMessage.error(details)
      }
    } else if (error?.response?.data?.error) {
      ElMessage.error(error.response.data.error)
    } else if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('密码修改失败')
    }
  } finally {
    pwdSubmitting.value = false
  }
}
const resetPwdForm = () => {
  pwdForm.value = { old_password: '', new_password: '', confirm_password: '' }
  pwdFormRef.value?.resetFields()
}

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await authApi.getProfile()
    if (response.user) {
      username.value = response.user.username
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  background-color: #2b3749;
  color: white;
  border-bottom: 1px solid #404040;
}

.logo img {
  width: 32px;
  height: 32px;
}

.logo h1 {
  margin: 0 0 0 10px;
  font-size: 18px;
  font-weight: bold;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.breadcrumb {
  color: #606266;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  color: #606266;
  font-size: 14px;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}

/* 深色主题样式 */
:deep(.el-menu) {
  background-color: #304156 !important;
}

:deep(.el-menu-item) {
  color: #bfcbd9 !important;
}

:deep(.el-menu-item:hover) {
  background-color: #404040 !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #409EFF !important;
  color: #fff !important;
}

:deep(.el-sub-menu__title) {
  color: #bfcbd9 !important;
}

:deep(.el-sub-menu__title:hover) {
  background-color: #404040 !important;
}
</style>