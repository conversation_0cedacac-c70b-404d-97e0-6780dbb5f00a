<template>
  <div class="preferred-materials">
    <h1 class="page-title">优选库</h1>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索物料编码或名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="searchForm.middle_category"
            placeholder="选择中类"
            clearable
            @change="handleMiddleCategoryChange"
          >
            <el-option
              v-for="category in currentMiddleCategories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="searchForm.category_code"
            placeholder="选择子类"
            clearable
            :disabled="!searchForm.middle_category"
            @change="handleSearch"
          >
            <el-option
              v-for="category in currentSubCategories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="草稿" value="DRAFT" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="停用" value="INACTIVE" />
            <el-option label="废弃" value="OBSOLETE" />
          </el-select>
        </el-col>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="success" :icon="Plus" @click="handleAddFromMaterials">
            从物料清单添加
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchRemoveFromPreferred"
          >
            批量移出优选库
          </el-button>
      </el-row>
    </el-card>

    <!-- 统计信息 -->
    <el-card class="stats-card" v-if="tableData.length > 0">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-number">{{ pagination.total }}</div>
            <div class="stat-label">优选库物料总数</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-number">{{ activeMaterialsCount }}</div>
            <div class="stat-label">活跃物料</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-number">{{ selectedRows.length }}</div>
            <div class="stat-label">已选择物料</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="物料编码" width="150" />
        <el-table-column prop="name" label="物料名称" min-width="200" />
        <el-table-column prop="category_name" label="分类" width="200">
          <template #default="{ row }">
            <div class="category-path">
              <span class="middle-category">{{ getMiddleCategoryName(row.category_code) }}</span>
              <span class="separator">/</span>
              <span class="sub-category">{{ getSubCategoryName(row.category_code) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="specification" label="规格" min-width="200" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="suppliers" label="供应商" width="200">
          <template #default="{ row }">
            <div v-if="row.supplier_ids && row.supplier_ids.length > 0">
              <el-tag
                v-for="supplierId in row.supplier_ids.slice(0, 2)"
                :key="supplierId"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px;"
              >
                {{ getSupplierName(supplierId) }}
              </el-tag>
              <el-tag
                v-if="row.supplier_ids.length > 2"
                size="small"
                type="info"
              >
                +{{ row.supplier_ids.length - 2 }}
              </el-tag>
            </div>
            <span v-else style="color: #909399;">暂无供应商</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleRemoveFromPreferred(row)">
              移出优选库
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 物料查看对话框 -->
    <MaterialView
      :visible="viewDialogVisible"
      @update:visible="viewDialogVisible = $event"
      :material="currentMaterial"
      :suppliers="suppliers"
      @edit="handleEditFromView"
    />

    <!-- 从物料清单添加对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="从物料清单添加物料到优选库"
      width="80%"
      @close="handleAddDialogClose"
    >
      <el-table
        v-loading="addLoading"
        :data="availableMaterials"
        stripe
        style="width: 100%"
        @selection-change="handleAddSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="物料编码" width="150" />
        <el-table-column prop="name" label="物料名称" min-width="200" />
        <el-table-column prop="category_name" label="分类" width="200" />
        <el-table-column prop="specification" label="规格" min-width="200" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper" style="margin-top: 20px;">
        <el-pagination
          v-model:current-page="addPagination.page"
          v-model:page-size="addPagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="addPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleAddSizeChange"
          @current-change="handleAddPageChange"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddToPreferred" :loading="addSubmitting">
            添加到优选库
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { materialsApi, procurementApi } from '@/api'
import dayjs from 'dayjs'
import MaterialView from './MaterialView.vue'

// 响应式数据
const loading = ref(false)
const addLoading = ref(false)
const addSubmitting = ref(false)
const tableData = ref([])
const availableMaterials = ref([])
const selectedRows = ref([])
const addSelectedRows = ref([])
const categories = ref([])
const suppliers = ref([])

// 大类tab相关数据
const majorCategories = ref([])
const activeMajorCategory = ref('')
const categoryTree = ref([])

const searchForm = reactive({
  search: '',
  middle_category: '',
  category_code: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const addPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const addDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const currentMaterial = ref({})

// 计算属性
// 获取当前大类的中类
const currentMiddleCategories = computed(() => {
  if (!activeMajorCategory.value || !categoryTree.value.length) {
    return []
  }
  
  const majorCategory = categoryTree.value.find(cat => cat.code === activeMajorCategory.value)
  return majorCategory ? majorCategory.children || [] : []
})

// 获取当前选中的中类的子类
const currentSubCategories = computed(() => {
  if (!searchForm.middle_category || !categoryTree.value.length) {
    return []
  }
  
  const middleCategory = categoryTree.value
    .flatMap(cat => cat.children || [])
    .find(cat => cat.code === searchForm.middle_category)
  
  return middleCategory ? middleCategory.children || [] : []
})

// 统计信息计算属性
const activeMaterialsCount = computed(() => {
  return tableData.value.filter(item => item.status === 'ACTIVE').length
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.search || undefined,
      category_code: searchForm.category_code || undefined,
      status: searchForm.status || undefined,
      is_preferred: true // 只加载优选库中的物料
    }
    
    // 暂时注释掉分类筛选逻辑，先测试基本功能
    /*
    // 如果选择了中类，则获取该中类下所有子类的编码
    if (searchForm.middle_category) {
      const middleCategory = categoryTree.value
        .flatMap(cat => cat.children || [])
        .find(cat => cat.code === searchForm.middle_category)
      
      if (middleCategory && middleCategory.children && middleCategory.children.length > 0) {
        // 收集该中类下所有子类的编码
        const subCategoryCodes = middleCategory.children.map(child => child.code)
        // 如果没有指定具体子类，则查询该中类下所有子类的物料
        if (!params.category_code) {
          params.category_codes = subCategoryCodes.join(',')
        }
      } else if (middleCategory) {
        // 如果中类没有子类，则直接使用中类编码
        params.category_code = middleCategory.code
      }
    } else if (activeMajorCategory.value) {
      // 如果当前选中的大类，则只加载该大类下的物料
      const majorCategory = categoryTree.value.find(cat => cat.code === activeMajorCategory.value)
      if (majorCategory) {
        const allCategoryCodes = [majorCategory.code]
        if (majorCategory.children && majorCategory.children.length > 0) {
          // 递归收集所有子分类编码
          const collectChildCodes = (children) => {
            children.forEach(child => {
              allCategoryCodes.push(child.code)
              if (child.children && child.children.length > 0) {
                collectChildCodes(child.children)
              }
            })
          }
          collectChildCodes(majorCategory.children)
        }
        
        // 如果没有指定具体分类，则查询该大类下所有分类的物料
        if (!params.category_code) {
          params.category_codes = allCategoryCodes.join(',')
        }
      }
    }
    */
    
    const response = await materialsApi.getMaterials(params)
    tableData.value = response.results || []
    pagination.total = response.count || 0
  } catch (error) {
    console.error('加载优选库数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadAvailableMaterials = async () => {
  addLoading.value = true
  try {
    const params = {
      page: addPagination.page,
      page_size: addPagination.size,
      is_preferred: false // 只加载不在优选库中的物料
    }
    
    const response = await materialsApi.getMaterials(params)
    availableMaterials.value = response.results || []
    addPagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载可用物料失败')
  } finally {
    addLoading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await materialsApi.getCategories()
    categories.value = response.results || []
  } catch (error) {
    ElMessage.error('加载分类失败')
  }
}

const loadMajorCategories = async () => {
  try {
    const response = await materialsApi.getCategoryTree()
    categoryTree.value = response || []
    
    // 提取大类（根分类）
    majorCategories.value = categoryTree.value.filter(cat => cat.level === 1 || !cat.parent_code)
    
    // 设置默认选中第一个大类
    if (majorCategories.value.length > 0 && !activeMajorCategory.value) {
      activeMajorCategory.value = majorCategories.value[0].code
    }
  } catch (error) {
    ElMessage.error('加载大类分类失败')
  }
}

const loadSuppliers = async () => {
  try {
    const response = await procurementApi.getSuppliers({ page_size: 1000 })
    suppliers.value = response.results || []
  } catch (error) {
    ElMessage.error('加载供应商失败')
  }
}

const handleMiddleCategoryChange = () => {
  // 清空子类选择
  searchForm.category_code = ''
  // 重新加载数据
  pagination.page = 1
  loadData()
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    middle_category: '',
    category_code: '',
    status: ''
  })
  handleSearch()
}

const handleAddFromMaterials = () => {
  addDialogVisible.value = true
  addPagination.page = 1
  loadAvailableMaterials()
}

const handleAddSelectionChange = (rows) => {
  addSelectedRows.value = rows
}

const handleAddToPreferred = async () => {
  if (addSelectedRows.value.length === 0) {
    ElMessage.warning('请选择要添加到优选库的物料')
    return
  }
  
  try {
    addSubmitting.value = true
    
    // 批量添加到优选库
    const materialIds = addSelectedRows.value.map(row => row.id)
    const response = await materialsApi.addToPreferred(materialIds)
    
    ElMessage.success(`成功添加 ${materialIds.length} 个物料到优选库`)
    addDialogVisible.value = false
    loadData() // 重新加载优选库数据
  } catch (error) {
    console.error('添加到优选库失败:', error)
    ElMessage.error('添加到优选库失败')
  } finally {
    addSubmitting.value = false
  }
}

const handleRemoveFromPreferred = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要将物料 "${row.name}" 从优选库中移除吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await materialsApi.removeFromPreferred([row.id])
    ElMessage.success('已从优选库移除')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除失败')
    }
  }
}

const handleBatchRemoveFromPreferred = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要移出的物料')
    return
  }
  
  try {
    const materialNames = selectedRows.value.map(row => row.name).join('、')
    await ElMessageBox.confirm(
      `确定要将以下 ${selectedRows.value.length} 个物料从优选库中移除吗？\n\n${materialNames}`, 
      '批量移除确认', 
      {
        confirmButtonText: '确定移除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const materialIds = selectedRows.value.map(row => row.id)
    await materialsApi.removeFromPreferred(materialIds)
    ElMessage.success(`成功移除 ${materialIds.length} 个物料`)
    selectedRows.value = []
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量移除失败')
    }
  }
}

const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}

const handleAddPageChange = () => {
  loadAvailableMaterials()
}

const handleAddSizeChange = () => {
  addPagination.page = 1
  loadAvailableMaterials()
}

const handleAddDialogClose = () => {
  addSelectedRows.value = []
}

const handleView = async (row) => {
  try {
    // 获取物料的详细信息
    const response = await materialsApi.getMaterial(row.id)
    currentMaterial.value = response
    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取物料详情失败:', error)
    ElMessage.error('获取物料详情失败')
  }
}

const handleEdit = (row) => {
  // 跳转到物料编辑页面
  window.open(`/materials/materials?edit=${row.id}`, '_blank')
}

const handleEditFromView = (material) => {
  // 从查看对话框跳转到编辑页面
  window.open(`/materials/materials?edit=${material.id}`, '_blank')
  viewDialogVisible.value = false
}





const getStatusType = (status) => {
  const statusMap = {
    DRAFT: 'info',
    ACTIVE: 'success',
    INACTIVE: 'warning',
    OBSOLETE: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    DRAFT: '草稿',
    ACTIVE: '活跃',
    INACTIVE: '停用',
    OBSOLETE: '废弃'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 获取中类名称
const getMiddleCategoryName = (categoryCode) => {
  if (!categoryCode || !categoryTree.value.length) return ''
  
  // 在所有大类中查找该分类
  for (const majorCategory of categoryTree.value) {
    const middleCategory = majorCategory.children?.find(cat => cat.code === categoryCode)
    if (middleCategory) {
      return middleCategory.name
    }
    
    // 在子类中查找
    for (const middleCat of majorCategory.children || []) {
      const subCategory = middleCat.children?.find(cat => cat.code === categoryCode)
      if (subCategory) {
        return middleCat.name
      }
    }
  }
  
  return ''
}

// 获取子类名称
const getSubCategoryName = (categoryCode) => {
  if (!categoryCode || !categoryTree.value.length) return ''
  
  // 在所有大类中查找该分类
  for (const majorCategory of categoryTree.value) {
    // 在子类中查找
    for (const middleCat of majorCategory.children || []) {
      const subCategory = middleCat.children?.find(cat => cat.code === categoryCode)
      if (subCategory) {
        return subCategory.name
      }
    }
  }
  
  return ''
}

// 根据供应商ID获取供应商名称
const getSupplierName = (supplierId) => {
  const supplier = suppliers.value.find(s => s.id === supplierId)
  return supplier ? supplier.name : '未知供应商'
}

// 生命周期
onMounted(async () => {
  await loadMajorCategories()
  await loadCategories()
  await loadSuppliers()
  loadData()
})
</script>

<style scoped>
.preferred-materials {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 20px;
}

.stats-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

.category-path {
  display: flex;
  align-items: center;
  gap: 4px;
}

.middle-category {
  color: #409eff;
  font-weight: 500;
}

.separator {
  color: #909399;
  font-size: 12px;
}

.sub-category {
  color: #606266;
}
</style>
