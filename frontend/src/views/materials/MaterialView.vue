<template>
  <div class="material-view">
    <el-dialog
      :model-value="visible"
      @update:model-value="$emit('update:visible', $event)"
      :title="`物料详情 - ${material.code}`"
      width="80%"
      :before-close="handleClose"
      class="material-view-dialog"
    >
      <div v-loading="loading" class="material-content">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>物料编码：</label>
                <span>{{ material.code }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>物料名称：</label>
                <span>{{ material.name }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>分类：</label>
                <span>{{ material.category_name }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>规格：</label>
                <span>{{ material.specification || '暂无' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>单位：</label>
                <span>{{ material.unit }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>状态：</label>
                <el-tag :type="getStatusType(material.status)">
                  {{ getStatusText(material.status) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>虚拟件：</label>
                <el-tag :type="material.is_virtual ? 'warning' : 'info'">
                  {{ material.is_virtual ? '是' : '否' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>优选库：</label>
                <el-tag :type="material.is_preferred ? 'success' : 'info'">
                  {{ material.is_preferred ? '是' : '否' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDate(material.created_at) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 文件展示区域 -->
        <el-card class="file-card">
          <template #header>
            <div class="card-header">
              <span>模型文件</span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <!-- 缩略图 -->
            <el-col :span="6" v-if="material.file_info?.has_thumbnail">
              <div class="thumbnail-section">
                <h4>缩略图</h4>
                <div class="thumbnail-container">
                  <img 
                    :src="material.file_info.thumbnail_url" 
                    :alt="material.name"
                    class="thumbnail-image"
                    @click="previewImage(material.file_info.thumbnail_url)"
                  />
                </div>
              </div>
            </el-col>
            
            <!-- 3D模型 -->
            <el-col :span="material.file_info?.has_thumbnail ? 9 : 12" v-if="material.file_info?.has_3d_model">
              <div class="model-section">
                <h4>3D模型</h4>
                <div class="model-viewer">
                  <div class="model-placeholder">
                    <el-icon class="model-icon"><View /></el-icon>
                    <p>3D模型预览</p>
                    <el-button type="primary" @click="view3DModel">
                      查看3D模型
                    </el-button>
                  </div>
                </div>
              </div>
            </el-col>
            
            <!-- 2D图纸 -->
            <el-col :span="material.file_info?.has_thumbnail ? 9 : 12" v-if="material.file_info?.has_2d_drawing">
              <div class="drawing-section">
                <h4>2D图纸</h4>
                <div class="drawing-viewer">
                  <div class="drawing-placeholder">
                    <el-icon class="drawing-icon"><Document /></el-icon>
                    <p>2D图纸预览</p>
                    <el-button type="primary" @click="view2DDrawing">
                      查看2D图纸
                    </el-button>
                  </div>
                </div>
              </div>
            </el-col>
            
            <!-- 无文件提示 -->
            <el-col :span="24" v-if="!hasAnyFiles">
              <div class="no-files">
                <el-empty description="暂无模型文件">
                  <el-button type="primary" @click="handleEdit">上传文件</el-button>
                </el-empty>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 供应商信息 -->
        <el-card class="supplier-card" v-if="material.supplier_ids && material.supplier_ids.length > 0">
          <template #header>
            <div class="card-header">
              <span>供应商信息</span>
            </div>
          </template>
          <div class="supplier-list">
            <el-tag
              v-for="supplierId in material.supplier_ids"
              :key="supplierId"
              size="large"
              style="margin-right: 10px; margin-bottom: 10px;"
            >
              {{ getSupplierName(supplierId) }}
            </el-tag>
          </div>
        </el-card>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handleEdit">编辑</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog :model-value="imagePreviewVisible" @update:model-value="imagePreviewVisible = $event" title="图片预览" width="60%">
      <div class="image-preview">
        <img :src="previewImageUrl" :alt="material.name" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>

    <!-- 智能3D模型查看器对话框 -->
    <el-dialog 
      :model-value="model3DVisible" 
      @update:model-value="model3DVisible = $event" 
      title="智能3D模型渲染器" 
      width="95%" 
      class="smart-model-viewer-dialog"
      :append-to-body="true"
      @opened="onModelDialogOpened"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :before-close="handleModelDialogClose"
    >
      <template #header>
        <div class="dialog-header">
          <h3>{{ material.name }} - 3D模型查看</h3>
          <div class="header-info">
            <el-tag size="small" type="info">
              {{ material.file_info?.['3d_model_name'] }}
            </el-tag>
            <el-tag size="small" type="success">
              {{ formatFileSize(material.file_info?.['3d_model_size']) }}
            </el-tag>
          </div>
        </div>
      </template>
      
      <div class="smart-model-viewer-container">
        <Model3DViewer 
          v-if="model3DVisible && material.file_info?.has_3d_model"
          :modelUrl="getFullModelUrl(material.file_info['3d_model_url'])"
          :modelType="getModelType(material.file_info['3d_model_name'])"
          :key="`model-viewer-${material.id}`"
        />
        
        <!-- 模型信息面板 -->
        <div class="model-info-panel" v-if="showModelInfo">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <span>模型信息</span>
                <el-button 
                  size="small" 
                  text 
                  @click="showModelInfo = false"
                  icon="Close"
                />
              </div>
            </template>
            
            <div class="model-stats">
              <div class="stat-item">
                <span class="label">文件类型:</span>
                <span class="value">{{ getModelType(material.file_info?.['3d_model_name'])?.toUpperCase() }}</span>
              </div>
              <div class="stat-item">
                <span class="label">文件大小:</span>
                <span class="value">{{ formatFileSize(material.file_info?.['3d_model_size']) }}</span>
              </div>

            </div>
          </el-card>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button size="small" @click="showModelInfo = !showModelInfo">
              <el-icon><InfoFilled /></el-icon>
              {{ showModelInfo ? '隐藏' : '显示' }}信息
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="download3DModel"
              :loading="downloading"
            >
              <el-icon><Download /></el-icon>
              下载模型
            </el-button>
          </div>
          <div class="footer-right">
            <el-button size="small" @click="model3DVisible = false">
              关闭
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 2D图纸查看器对话框 -->
    <el-dialog :model-value="drawing2DVisible" @update:model-value="drawing2DVisible = $event" title="2D图纸查看" width="90%" class="drawing-viewer-dialog">
      <div class="drawing-viewer-container">
        <div class="drawing-viewer-toolbar">
          <el-button-group>
            <el-button size="small" @click="zoomIn">放大</el-button>
            <el-button size="small" @click="zoomOut">缩小</el-button>
            <el-button size="small" @click="resetZoom">重置</el-button>
            <el-button size="small" @click="rotateImage">旋转</el-button>
          </el-button-group>
          <div class="zoom-info">
            <span>缩放: {{ Math.round(imageScale * 100) }}%</span>
          </div>
          <el-button size="small" type="primary" @click="download2DDrawing">下载图纸</el-button>
        </div>
        <div class="drawing-viewer-content">
          <!-- 图片文件显示 -->
          <div v-if="material.file_info?.has_2d_drawing && isImageFile(material.file_info['2d_drawing_name'])" class="image-container" @wheel="handleWheel">
            <img 
              :src="material.file_info['2d_drawing_url']"
              class="drawing-image"
              :style="{
                transform: `scale(${imageScale}) rotate(${imageRotation}deg)`,
                transformOrigin: 'center center'
              }"
              alt="2D图纸"
            />
          </div>
          <!-- PDF文件显示 -->
          <iframe 
            v-else-if="material.file_info?.has_2d_drawing"
            :src="material.file_info['2d_drawing_url']"
            class="drawing-iframe"
            frameborder="0"
          ></iframe>
          <!-- 无文件提示 -->
          <div v-else class="no-drawing">
            <el-empty description="暂无2D图纸">
              <el-button type="primary" @click="handleEdit">上传图纸</el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import { View, Document, InfoFilled, Download, Close } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import Model3DViewer from '@/components/Model3DViewer.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  material: {
    type: Object,
    default: () => ({})
  },
  suppliers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit'])

// 响应式数据
const loading = ref(false)
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')
const model3DVisible = ref(false)
const drawing2DVisible = ref(false)
const showModelInfo = ref(false)
const downloading = ref(false)



// 图片缩放相关
const imageScale = ref(1)
const imageRotation = ref(0)

// 计算属性
const hasAnyFiles = computed(() => {
  const fileInfo = props.material.file_info || {}
  return fileInfo.has_3d_model || fileInfo.has_2d_drawing || fileInfo.has_thumbnail
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
}

const handleEdit = () => {
  emit('edit', props.material)
  handleClose()
}

const getStatusType = (status) => {
  const statusMap = {
    DRAFT: 'info',
    ACTIVE: 'success',
    INACTIVE: 'warning',
    OBSOLETE: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    DRAFT: '草稿',
    ACTIVE: '活跃',
    INACTIVE: '停用',
    OBSOLETE: '废弃'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getSupplierName = (supplierId) => {
  const supplier = props.suppliers.find(s => s.id === supplierId)
  return supplier ? supplier.name : '未知供应商'
}

const previewImage = (url) => {
  previewImageUrl.value = url
  imagePreviewVisible.value = true
}

const view3DModel = () => {
  model3DVisible.value = true
}

const view2DDrawing = () => {
  drawing2DVisible.value = true
  // 重置缩放和旋转
  imageScale.value = 1
  imageRotation.value = 0
}

const getModelType = (filename) => {
  if (!filename) return 'stl'
  const ext = filename.split('.').pop().toLowerCase()
  return ext
}

// 生成可访问的完整模型URL
const getFullModelUrl = (url) => {
  if (!url) {
    console.warn('模型URL为空')
    return ''
  }
  
  if (/^https?:\/\//i.test(url)) {
    return url
  }
  
  // 获取Django后端的基础URL（去掉/api路径）
  let baseUrl = ''
  try {
    const apiBaseUrl = request.defaults.baseURL
    // 将API URL转换为后端基础URL
    baseUrl = apiBaseUrl.replace('/api', '')
    
  } catch (e) {
    // 如果无法获取，使用默认的后端地址
    baseUrl = 'http://192.168.0.190:8000'
  }
  
  // 构建完整URL
  const fullUrl = baseUrl + url
  
  // 验证URL格式
  try {
    new URL(fullUrl)
  } catch (e) {
    console.error('URL格式错误:', e)
  }
  
  return fullUrl
}

// 对话框打开后，强制触发viewer的渲染尺寸计算
const onModelDialogOpened = () => {
  // 模型对话框打开
  
  // 延迟多次触发resize，确保容器尺寸正确计算
  const resizeDelays = [0, 100, 300, 500]
  resizeDelays.forEach(delay => {
    setTimeout(() => {
  // 触发resize事件以确保渲染尺寸正确
      window.dispatchEvent(new Event('resize'))
    }, delay)
  })
}

// 对话框关闭前的处理
const handleModelDialogClose = (done) => {
  // 模型对话框关闭
  model3DVisible.value = false
  done()
}

const download3DModel = async () => {
  if (props.material.file_info?.has_3d_model) {
    downloading.value = true
    try {
      const link = document.createElement('a')
      link.href = props.material.file_info['3d_model_url']
      link.download = props.material.file_info['3d_model_name']
      link.click()
      
      ElMessage.success('模型下载已开始')
    } catch (error) {
      ElMessage.error('下载失败: ' + error.message)
    } finally {
      downloading.value = false
    }
  }
}



const zoomIn = () => {
  imageScale.value = Math.min(imageScale.value * 1.2, 5) // 最大放大5倍
}

const zoomOut = () => {
  imageScale.value = Math.max(imageScale.value / 1.2, 0.1) // 最小缩小到0.1倍
}

const resetZoom = () => {
  imageScale.value = 1
  imageRotation.value = 0
}

const rotateImage = () => {
  imageRotation.value = (imageRotation.value + 90) % 360
}

const handleWheel = (event) => {
  event.preventDefault()
  if (event.deltaY < 0) {
    // 向上滚动，放大
    zoomIn()
  } else {
    // 向下滚动，缩小
    zoomOut()
  }
}

const download2DDrawing = () => {
  if (props.material.file_info?.has_2d_drawing) {
    const link = document.createElement('a')
    link.href = props.material.file_info['2d_drawing_url']
    link.download = props.material.file_info['2d_drawing_name']
    link.click()
  }
}

const isImageFile = (filename) => {
  if (!filename) return false
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']
  const ext = filename.split('.').pop().toLowerCase()
  return imageExtensions.includes(ext)
}
</script>

<style scoped>
.material-view-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.material-content {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card,
.file-card,
.supplier-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 10px;
}

.thumbnail-section,
.model-section,
.drawing-section {
  text-align: center;
}

.thumbnail-section h4,
.model-section h4,
.drawing-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.thumbnail-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
}

.thumbnail-image {
  max-width: 100%;
  max-height: 150px;
  cursor: pointer;
  transition: transform 0.2s;
}

.thumbnail-image:hover {
  transform: scale(1.05);
}



.model-placeholder,
.drawing-placeholder {
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  padding: 40px 20px;
  background: #fafafa;
  color: #909399;
}

.model-icon,
.drawing-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.no-files {
  text-align: center;
  padding: 40px 20px;
}

.supplier-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.model-viewer-dialog,
.drawing-viewer-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.model-viewer-container,
.drawing-viewer-container {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.model-viewer-toolbar,
.drawing-viewer-toolbar {
  padding: 15px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.zoom-info {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.model-viewer-content {
  flex: 1;
  background: #f0f0f0;
}

/* 智能3D模型查看器对话框样式 */
.smart-model-viewer-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
  }
  
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
  }
  
  :deep(.el-dialog__body) {
    padding: 0;
    height: 70vh;
    overflow: hidden;
  }
  
  :deep(.el-dialog__footer) {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-info {
  display: flex;
  gap: 10px;
}

.smart-model-viewer-container {
  height: 100%;
  min-height: 600px;
  position: relative;
  background: linear-gradient(45deg, #f0f2f5 0%, #e6f7ff 100%);
}

.model-info-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 300px;
  z-index: 10;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.model-info-panel .info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
}

.model-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item .label {
  font-weight: 500;
  color: #606266;
}

.stat-item .value {
  font-weight: 600;
  color: #303133;
}



.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.footer-left {
  display: flex;
  gap: 10px;
}

.footer-right {
  display: flex;
  gap: 10px;
}

.drawing-viewer-content {
  flex: 1;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
  padding: 20px;
}

.drawing-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  cursor: grab;
}

.drawing-image:active {
  cursor: grabbing;
}

.drawing-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.image-preview {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}
</style>
