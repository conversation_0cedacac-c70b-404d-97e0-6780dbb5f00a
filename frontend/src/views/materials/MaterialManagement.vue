<template>
  <div class="material-management">
    <h1 class="page-title">物料管理</h1>
    
    <!-- 大类Tab -->
    <el-card class="tab-card">
      <el-tabs v-model="activeMajorCategory" @tab-change="handleMajorCategoryChange">
        <el-tab-pane
          v-for="majorCategory in majorCategories"
          :key="majorCategory.code"
          :label="majorCategory.name"
          :name="majorCategory.code"
        />
      </el-tabs>
    </el-card>
    
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索物料编码或名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="searchForm.middle_category"
            placeholder="选择中类"
            clearable
            @change="handleMiddleCategoryChange"
          >
            <el-option
              v-for="category in currentMiddleCategories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="searchForm.category_code"
            placeholder="选择子类"
            clearable
            :disabled="!searchForm.middle_category"
            @change="handleSearch"
          >
            <el-option
              v-for="category in currentSubCategories"
              :key="category.code"
              :label="category.name"
              :value="category.code"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="草稿" value="DRAFT" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="停用" value="INACTIVE" />
            <el-option label="废弃" value="OBSOLETE" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="success" :icon="Plus" @click="handleCreate">
            新增物料
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="物料编码" width="150" />
        <el-table-column prop="name" label="物料名称" min-width="200" />
        <el-table-column prop="category_name" label="分类" width="200">
          <template #default="{ row }">
            <div class="category-path">
              <span class="middle-category">{{ getMiddleCategoryName(row.category_code) }}</span>
              <span class="separator">/</span>
              <span class="sub-category">{{ getSubCategoryName(row.category_code) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="specification" label="规格" min-width="200" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="suppliers" label="供应商" width="200">
          <template #default="{ row }">
            <div v-if="row.supplier_ids && row.supplier_ids.length > 0">
              <el-tag
                v-for="supplierId in row.supplier_ids.slice(0, 2)"
                :key="supplierId"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px;"
              >
                {{ getSupplierName(supplierId) }}
              </el-tag>
              <el-tag
                v-if="row.supplier_ids.length > 2"
                size="small"
                type="info"
              >
                +{{ row.supplier_ids.length - 2 }}
              </el-tag>
            </div>
            <span v-else style="color: #909399;">暂无供应商</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :model-value="dialogVisible"
      @update:model-value="dialogVisible = $event"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <!-- 分类选择放在最前面 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="中类" prop="middle_category">
              <el-select 
                v-model="form.middle_category" 
                placeholder="请选择中类" 
                :disabled="isEdit"
                @change="handleFormMiddleCategoryChange"
              >
                <el-option
                  v-for="category in currentMiddleCategories"
                  :key="category.code"
                  :label="category.name"
                  :value="category.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="子类" prop="category_code">
              <el-select 
                v-model="form.category_code" 
                placeholder="请选择子类" 
                :disabled="!form.middle_category || isEdit"
                @change="handleFormCategoryCodeChange"
              >
                <el-option
                  v-for="category in formSubCategories"
                  :key="category.code"
                  :label="category.name"
                  :value="category.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料编码" prop="code">
              <el-input 
                v-model="form.code" 
                :disabled="isEdit" 
                :placeholder="isEdit ? '编辑时不可修改' : '选择分类后自动填入前缀，请手动补充数字'" 
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="name">
              <el-input v-model="form.name" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="规格" prop="specification">
          <el-input v-model="form.specification" type="textarea" :rows="3" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="form.unit" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="草稿" value="DRAFT" />
                <el-option label="活跃" value="ACTIVE" />
                <el-option label="停用" value="INACTIVE" />
                <el-option label="废弃" value="OBSOLETE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="供应商" prop="supplier_ids">
          <el-select
            v-model="form.supplier_ids"
            placeholder="请选择供应商"
            multiple
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="supplier in suppliers"
              :key="supplier.id"
              :label="supplier.name"
              :value="supplier.id"
            >
              <span>{{ supplier.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ supplier.code }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="虚拟件" prop="is_virtual">
              <el-switch v-model="form.is_virtual" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="加入优选库" prop="is_preferred">
              <el-switch v-model="form.is_preferred" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 文件上传区域 -->
        <el-form-item label="模型文件">
          <el-card class="file-upload-card">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="upload-section">
                  <h4>缩略图</h4>
                  <el-upload
                    ref="thumbnailUpload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="{ type: 'thumbnail' }"
                    :show-file-list="false"
                    :on-success="handleThumbnailSuccess"
                    :on-error="handleUploadError"
                    :before-upload="beforeImageUpload"
                    accept="image/*"
                  >
                    <div class="upload-area">
                      <img v-if="form.thumbnail_url" :src="form.thumbnail_url" class="uploaded-image" />
                      <div v-else class="upload-placeholder">
                        <el-icon class="upload-icon"><Plus /></el-icon>
                        <p>点击上传缩略图</p>
                      </div>
                    </div>
                  </el-upload>
                  <el-button v-if="form.thumbnail_url" size="small" type="danger" @click="removeThumbnail">
                    删除
                  </el-button>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="upload-section">
                  <h4>3D模型</h4>
                  <el-upload
                    ref="model3DUpload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="{ type: '3d_model' }"
                    :show-file-list="false"
                    :on-success="handle3DModelSuccess"
                    :on-error="handleUploadError"
                    :before-upload="before3DModelUpload"
                    accept=".stl,.obj,.step,.iges,.3ds,.dae"
                  >
                    <div class="upload-area">
                      <div v-if="form.model_3d_name" class="uploaded-file">
                        <el-icon class="file-icon"><Document /></el-icon>
                        <p>{{ form.model_3d_name }}</p>
                      </div>
                      <div v-else class="upload-placeholder">
                        <el-icon class="upload-icon"><Plus /></el-icon>
                        <p>点击上传3D模型</p>
                        <small>支持STL、OBJ、STEP等格式</small>
                      </div>
                    </div>
                  </el-upload>
                  <el-button v-if="form.model_3d_name" size="small" type="danger" @click="remove3DModel">
                    删除
                  </el-button>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="upload-section">
                  <h4>2D图纸</h4>
                  <el-upload
                    ref="drawing2DUpload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="{ type: '2d_drawing' }"
                    :show-file-list="false"
                    :on-success="handle2DDrawingSuccess"
                    :on-error="handleUploadError"
                    :before-upload="before2DDrawingUpload"
                    accept=".pdf,.dwg,.dxf,.jpg,.jpeg,.png"
                  >
                    <div class="upload-area">
                      <div v-if="form.drawing_2d_name" class="uploaded-file">
                        <el-icon class="file-icon"><Document /></el-icon>
                        <p>{{ form.drawing_2d_name }}</p>
                      </div>
                      <div v-else class="upload-placeholder">
                        <el-icon class="upload-icon"><Plus /></el-icon>
                        <p>点击上传2D图纸</p>
                        <small>支持PDF、DWG、DXF等格式</small>
                      </div>
                    </div>
                  </el-upload>
                  <el-button v-if="form.drawing_2d_name" size="small" type="danger" @click="remove2DDrawing">
                    删除
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 物料详情查看组件 -->
    <MaterialView
      :visible="viewDialogVisible"
      @update:visible="viewDialogVisible = $event"
      :material="currentMaterial"
      :suppliers="suppliers"
      @edit="handleEditFromView"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Document } from '@element-plus/icons-vue'
import { materialsApi, procurementApi } from '@/api'
import dayjs from 'dayjs'
import MaterialView from './MaterialView.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const categories = ref([])
const selectedRows = ref([])
const suppliers = ref([])

// 大类tab相关数据
const majorCategories = ref([])
const activeMajorCategory = ref('')
const categoryTree = ref([])

const searchForm = reactive({
  search: '',
  middle_category: '',
  category_code: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()
const currentMaterial = ref({})

// 文件上传相关
const uploadUrl = '/api/materials/materials/upload/'

// 获取CSRF token的函数
const getCsrfToken = () => {
  const csrfToken = document.cookie.split('; ').find(row => row.startsWith('csrftoken='))
  return csrfToken ? csrfToken.split('=')[1] : ''
}

const uploadHeaders = {
  'Authorization': `Token ${localStorage.getItem('token')}`,
  'X-CSRFToken': getCsrfToken()
}

const form = reactive({
  code: '',
  name: '',
  middle_category: '',
  category_code: '',
  specification: '',
  unit: '',
  supplier_ids: [],
  is_virtual: false,
  is_preferred: false,
  status: 'DRAFT',
  // 文件相关字段
  thumbnail_url: '',
  model_3d_name: '',
  model_3d_temp_path: '',
  drawing_2d_name: '',
  drawing_2d_temp_path: ''
})

const formRules = {
  name: [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
  middle_category: [{ required: true, message: '请选择中类', trigger: 'change' }],
  category_code: [{ required: true, message: '请选择子类', trigger: 'change' }],
  code: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  status: [{ required: true, message: '请选择物料状态', trigger: 'change' }]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑物料' : '新增物料')

// 获取当前大类的中类
const currentMiddleCategories = computed(() => {
  if (!activeMajorCategory.value || !categoryTree.value.length) {
    return []
  }
  
  const majorCategory = categoryTree.value.find(cat => cat.code === activeMajorCategory.value)
  return majorCategory ? majorCategory.children || [] : []
})

// 获取当前选中的中类的子类
const currentSubCategories = computed(() => {
  if (!searchForm.middle_category || !categoryTree.value.length) {
    return []
  }
  
  const middleCategory = categoryTree.value
    .flatMap(cat => cat.children || [])
    .find(cat => cat.code === searchForm.middle_category)
  
  return middleCategory ? middleCategory.children || [] : []
})

// 获取表单中选中中类的子类
const formSubCategories = computed(() => {
  if (!form.middle_category || !categoryTree.value.length) {
    return []
  }
  
  const middleCategory = categoryTree.value
    .flatMap(cat => cat.children || [])
    .find(cat => cat.code === form.middle_category)
  
  return middleCategory ? middleCategory.children || [] : []
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.size,
      search: searchForm.search || undefined,
      category_code: searchForm.category_code || undefined,
      status: searchForm.status || undefined
    }
    
    // 如果选择了中类，则获取该中类下所有子类的编码
    if (searchForm.middle_category) {
      const middleCategory = categoryTree.value
        .flatMap(cat => cat.children || [])
        .find(cat => cat.code === searchForm.middle_category)
      
      if (middleCategory && middleCategory.children && middleCategory.children.length > 0) {
        // 收集该中类下所有子类的编码
        const subCategoryCodes = middleCategory.children.map(child => child.code)
        // 如果没有指定具体子类，则查询该中类下所有子类的物料
        if (!params.category_code) {
          params.category_codes = subCategoryCodes.join(',')
          console.log('中类筛选 - 子类编码:', subCategoryCodes)
        }
      } else if (middleCategory) {
        // 如果中类没有子类，则直接使用中类编码
        params.category_code = middleCategory.code
        console.log('中类筛选 - 中类编码:', middleCategory.code)
      }
    } else if (activeMajorCategory.value) {
      // 如果当前选中的大类，则只加载该大类下的物料
      const majorCategory = categoryTree.value.find(cat => cat.code === activeMajorCategory.value)
      if (majorCategory) {
        const allCategoryCodes = [majorCategory.code]
        if (majorCategory.children && majorCategory.children.length > 0) {
          // 递归收集所有子分类编码
          const collectChildCodes = (children) => {
            children.forEach(child => {
              allCategoryCodes.push(child.code)
              if (child.children && child.children.length > 0) {
                collectChildCodes(child.children)
              }
            })
          }
          collectChildCodes(majorCategory.children)
        }
        
        // 如果没有指定具体分类，则查询该大类下所有分类的物料
        if (!params.category_code) {
          params.category_codes = allCategoryCodes.join(',')
        }
      }
    }
    
    console.log('发送给后端的参数:', params)
    const response = await materialsApi.getMaterials(params)
    tableData.value = response.results || []
    pagination.total = response.count || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await materialsApi.getCategories()
    categories.value = response.results || []
  } catch (error) {
    ElMessage.error('加载分类失败')
  }
}

const loadMajorCategories = async () => {
  try {
    const response = await materialsApi.getCategoryTree()
    categoryTree.value = response || []
    
    // 提取大类（根分类）
    majorCategories.value = categoryTree.value.filter(cat => cat.level === 1 || !cat.parent_code)
    
    // 设置默认选中第一个大类
    if (majorCategories.value.length > 0 && !activeMajorCategory.value) {
      activeMajorCategory.value = majorCategories.value[0].code
    }
  } catch (error) {
    ElMessage.error('加载大类分类失败')
  }
}

const loadSuppliers = async () => {
  try {
    const response = await procurementApi.getSuppliers({ page_size: 1000 })
    suppliers.value = response.results || []
  } catch (error) {
    ElMessage.error('加载供应商失败')
  }
}

const handleMajorCategoryChange = (tabName) => {
  activeMajorCategory.value = tabName
  // 清空分类选择
  searchForm.middle_category = ''
  searchForm.category_code = ''
  // 重新加载数据
  pagination.page = 1
  loadData()
}

const handleMiddleCategoryChange = () => {
  // 清空子类选择
  searchForm.category_code = ''
  // 重新加载数据
  pagination.page = 1
  loadData()
}

const handleFormMiddleCategoryChange = () => {
  // 清空子类选择和物料编码
  form.category_code = ''
  form.code = ''
  
  // 如果中类没有子类，直接生成物料编码
  if (form.middle_category) {
    const middleCategory = categoryTree.value
      .flatMap(cat => cat.children || [])
      .find(cat => cat.code === form.middle_category)
    
    if (middleCategory && (!middleCategory.children || middleCategory.children.length === 0)) {
      // 中类没有子类，直接使用中类编码填入物料编码前缀
      generateMaterialCode(middleCategory.code)
      // 提示用户手动补充数字
      ElMessage.info('已自动填入分类编码前缀，请手动补充数字部分')
    }
  }
}

const handleFormCategoryCodeChange = () => {
  // 选择子类后自动填入物料编码前缀
  if (form.category_code) {
    generateMaterialCode(form.category_code)
    // 提示用户手动补充数字
    ElMessage.info('已自动填入分类编码前缀，请手动补充数字部分')
  }
}

const generateMaterialCode = async (categoryCode) => {
  try {
    // 获取该分类下已有的物料数量
    const params = {
      category_code: categoryCode,
      page_size: 1  // 只需要获取数量
    }
    
    const response = await materialsApi.getMaterials(params)
    const existingCount = response.count || 0
    
    // 只填入分类编码前缀，让用户手动填写数字部分
    const suggestedCode = `${categoryCode}${(existingCount + 1).toString().padStart(4, '0')}`
    form.code = suggestedCode
    
    console.log(`建议物料编码: ${suggestedCode} (分类: ${categoryCode}, 已有数量: ${existingCount})`)
  } catch (error) {
    console.error('获取分类物料数量失败:', error)
    // 如果API调用失败，只填入分类编码
    form.code = categoryCode
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    middle_category: '',
    category_code: '',
    status: ''
  })
  handleSearch()
}

const handleCreate = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

const handleEdit = (row) => {
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(form, row)
  
  // 处理供应商数据
  if (row.supplier_ids) {
    form.supplier_ids = Array.isArray(row.supplier_ids) ? row.supplier_ids : []
  } else {
    form.supplier_ids = []
  }
  
  // 处理优选库状态
  form.is_preferred = row.is_preferred || false
  
  // 处理文件上传数据
  form.thumbnail_url = row.thumbnail_url || ''
  form.thumbnail_temp_path = ''  // 编辑时清空临时路径，等待新上传
  form.model_3d_name = row.model_3d_name || ''
  form.model_3d_temp_path = ''
  form.drawing_2d_name = row.drawing_2d_name || ''
  form.drawing_2d_temp_path = ''
  
  // 根据分类编码设置中类（编辑时不可修改）
  if (row.category_code) {
    form.middle_category = getMiddleCategoryCode(row.category_code)
  }
  
  console.log('编辑物料数据:', form)
}

const handleView = (row) => {
  // 获取完整的物料详情数据
  currentMaterial.value = {
    ...row,
    file_info: row.file_info || {}
  }
  viewDialogVisible.value = true
}

const handleEditFromView = (row) => {
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(form, row)
  
  // 处理供应商数据
  if (row.supplier_ids) {
    form.supplier_ids = Array.isArray(row.supplier_ids) ? row.supplier_ids : []
  } else {
    form.supplier_ids = []
  }
  
  // 处理优选库状态
  form.is_preferred = row.is_preferred || false
  
  // 处理文件上传数据
  form.thumbnail_url = row.thumbnail_url || ''
  form.thumbnail_temp_path = ''
  form.model_3d_name = row.model_3d_name || ''
  form.model_3d_temp_path = ''
  form.drawing_2d_name = row.drawing_2d_name || ''
  form.drawing_2d_temp_path = ''
  
  // 根据分类编码设置中类（编辑时不可修改）
  if (row.category_code) {
    form.middle_category = getMiddleCategoryCode(row.category_code)
  }
  
  console.log('从详情编辑物料数据:', form)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个物料吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await materialsApi.deleteMaterial(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

const handlePageChange = () => {
  loadData()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadData()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 过滤掉空字符串的文件字段
    const submitData = { ...form }
    if (!submitData.thumbnail_temp_path) delete submitData.thumbnail_temp_path
    if (!submitData.model_3d_temp_path) delete submitData.model_3d_temp_path
    if (!submitData.drawing_2d_temp_path) delete submitData.drawing_2d_temp_path
    if (!submitData.model_3d_name) delete submitData.model_3d_name
    if (!submitData.drawing_2d_name) delete submitData.drawing_2d_name
    
    console.log('提交的表单数据:', JSON.stringify(submitData, null, 2))
    console.log('缩略图相关字段:', {
      thumbnail_url: submitData.thumbnail_url,
      thumbnail_temp_path: submitData.thumbnail_temp_path
    })
    
    if (isEdit.value) {
      await materialsApi.updateMaterial(submitData.id, submitData)
      ElMessage.success('更新成功')
    } else {
      await materialsApi.createMaterial(submitData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    if (error !== false) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  resetForm()
  formRef.value?.resetFields()
}

const resetForm = () => {
  Object.assign(form, {
    code: '',  // 新增时清空物料编码，等待自动生成
    name: '',
    middle_category: '',
    category_code: '',
    specification: '',
    unit: '',
    supplier_ids: [],
    is_virtual: false,
    is_preferred: false,
    status: 'DRAFT',
    // 文件相关字段
    thumbnail_url: '',
    thumbnail_temp_path: '',
    model_3d_name: '',
    model_3d_temp_path: '',
    drawing_2d_name: '',
    drawing_2d_temp_path: ''
  })
}

const getStatusType = (status) => {
  const statusMap = {
    DRAFT: 'info',
    ACTIVE: 'success',
    INACTIVE: 'warning',
    OBSOLETE: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    DRAFT: '草稿',
    ACTIVE: '活跃',
    INACTIVE: '停用',
    OBSOLETE: '废弃'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 获取中类名称
const getMiddleCategoryName = (categoryCode) => {
  if (!categoryCode || !categoryTree.value.length) return ''
  
  // 在所有大类中查找该分类
  for (const majorCategory of categoryTree.value) {
    const middleCategory = majorCategory.children?.find(cat => cat.code === categoryCode)
    if (middleCategory) {
      return middleCategory.name
    }
    
    // 在子类中查找
    for (const middleCat of majorCategory.children || []) {
      const subCategory = middleCat.children?.find(cat => cat.code === categoryCode)
      if (subCategory) {
        return middleCat.name
      }
    }
  }
  
  return ''
}

// 获取子类名称
const getSubCategoryName = (categoryCode) => {
  if (!categoryCode || !categoryTree.value.length) return ''
  
  // 在所有大类中查找该分类
  for (const majorCategory of categoryTree.value) {
    // 在子类中查找
    for (const middleCat of majorCategory.children || []) {
      const subCategory = middleCat.children?.find(cat => cat.code === categoryCode)
      if (subCategory) {
        return subCategory.name
      }
    }
  }
  
  return ''
}

// 获取中类编码
const getMiddleCategoryCode = (categoryCode) => {
  if (!categoryCode || !categoryTree.value.length) return ''
  
  // 在所有大类中查找该分类
  for (const majorCategory of categoryTree.value) {
    const middleCategory = majorCategory.children?.find(cat => cat.code === categoryCode)
    if (middleCategory) {
      return middleCategory.code
    }
    
    // 在子类中查找
    for (const middleCat of majorCategory.children || []) {
      const subCategory = middleCat.children?.find(cat => cat.code === categoryCode)
      if (subCategory) {
        return middleCat.code
      }
    }
  }
  
  return ''
}

// 根据供应商ID获取供应商名称
const getSupplierName = (supplierId) => {
  const supplier = suppliers.value.find(s => s.id === supplierId)
  return supplier ? supplier.name : '未知供应商'
}

// 文件上传相关方法
const beforeImageUpload = (rawFile) => {
  const isJPGPNG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png'
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!isJPGPNG) {
    ElMessage.error('缩略图只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('缩略图大小不能超过 2MB!')
  }
  return isJPGPNG && isLt2M
}

const handleThumbnailSuccess = (response) => {
  console.log('缩略图上传响应:', response)
  form.thumbnail_url = response.url
  form.thumbnail_temp_path = response.temp_path
  console.log('设置后的表单数据:', {
    thumbnail_url: form.thumbnail_url,
    thumbnail_temp_path: form.thumbnail_temp_path
  })
  ElMessage.success('缩略图上传成功')
}

const removeThumbnail = () => {
  form.thumbnail_url = ''
  form.thumbnail_temp_path = ''
  ElMessage.success('缩略图已删除')
}

const before3DModelUpload = (rawFile) => {
  // 基于文件扩展名验证，因为MIME类型可能不准确
  const allowedExtensions = ['.stl', '.obj', '.step', '.stp', '.iges', '.igs', '.3ds', '.dae']
  const fileName = rawFile.name.toLowerCase()
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
  
  const isLt5M = rawFile.size / 1024 / 1024 < 5

  if (!hasValidExtension) {
    ElMessage.error('3D模型只能是 STL/OBJ/STEP/STP/IGES/IGS/3DS/DAE 格式!')
  }
  if (!isLt5M) {
    ElMessage.error('3D模型大小不能超过 5MB!')
  }
  return hasValidExtension && isLt5M
}

const handle3DModelSuccess = (response) => {
  form.model_3d_name = response.name
  form.model_3d_temp_path = response.temp_path
  ElMessage.success('3D模型上传成功')
}

const remove3DModel = () => {
  form.model_3d_name = ''
  form.model_3d_temp_path = ''
  ElMessage.success('3D模型已删除')
}

const before2DDrawingUpload = (rawFile) => {
  // 基于文件扩展名验证，因为MIME类型可能不准确
  const allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.dwg', '.dxf']
  const fileName = rawFile.name.toLowerCase()
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
  
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!hasValidExtension) {
    ElMessage.error('2D图纸只能是 PDF/JPG/PNG/GIF/BMP/TIFF/WEBP/DWG/DXF 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('2D图纸大小不能超过 2MB!')
  }
  return hasValidExtension && isLt2M
}

const handle2DDrawingSuccess = (response) => {
  form.drawing_2d_name = response.name
  form.drawing_2d_temp_path = response.temp_path
  ElMessage.success('2D图纸上传成功')
}

const remove2DDrawing = () => {
  form.drawing_2d_name = ''
  form.drawing_2d_temp_path = ''
  ElMessage.success('2D图纸已删除')
}

const handleUploadError = (err) => {
  ElMessage.error(`文件上传失败: ${err.message}`)
}

// 生命周期
onMounted(async () => {
  await loadMajorCategories()
  await loadCategories()
  await loadSuppliers()
  loadData()
})
</script>

<style scoped>
.material-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.tab-card {
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}

.category-path {
  display: flex;
  align-items: center;
  gap: 4px;
}

.middle-category {
  color: #409eff;
  font-weight: 500;
}

.separator {
  color: #909399;
  font-size: 12px;
}

.sub-category {
  color: #606266;
}

.file-upload-card {
  margin-top: 20px;
}

.upload-section {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
}

.upload-area {
  position: relative;
  width: 100%;
  height: 150px; /* 固定高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.file-icon {
  font-size: 20px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

.upload-icon {
  font-size: 40px;
  margin-bottom: 10px;
}
</style>