<template>
  <div class="category-management">
    <h1 class="page-title">物料分类管理</h1>
    
    <!-- 操作栏 -->
    <el-card class="toolbar-card">
      <el-button type="primary" :icon="Plus" @click="handleCreate">
        新增分类
      </el-button>
      <el-button :icon="Refresh" @click="loadData">刷新</el-button>
    </el-card>

    <!-- 分类树形表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        row-key="code"
        stripe
        style="width: 100%"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="code" label="分类编码" width="150" />
        <el-table-column prop="name" label="分类名称" min-width="200" />
        <el-table-column prop="level" label="层级" width="80" />
        <el-table-column prop="parent_code" label="父分类编码" width="150" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '活跃' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类编码" prop="code">
          <el-input v-model="form.code" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="层级" prop="level">
          <el-select v-model="form.level" placeholder="请选择层级" @change="handleLevelChange">
            <el-option label="大类" :value="1" />
            <el-option label="中类" :value="2" />
            <el-option label="小类" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item 
          v-if="form.level > 1" 
          label="父分类" 
          prop="parent_code"
        >
          <el-select v-model="form.parent_code" placeholder="请选择父分类" clearable>
            <el-option
              v-for="category in availableParentCategories"
              :key="category.code"
              :label="`${category.code} - ${category.name}`"
              :value="category.code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { materialsApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const allCategories = ref([])

const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

const form = reactive({
  code: '',
  name: '',
  parent_code: '',
  level: 1
})

const formRules = {
  code: [{ required: true, message: '请输入分类编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  level: [{ required: true, message: '请选择层级', trigger: 'change' }],
  parent_code: [
    { 
      required: true, 
      message: '请选择父分类', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.level > 1 && !value) {
          callback(new Error('请选择父分类'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑分类' : '新增分类')

const availableParentCategories = computed(() => {
  // 过滤出可以作为父分类的分类
  return allCategories.value.filter(item => {
    // 排除当前编辑的分类
    if (isEdit.value && item.code === form.code) {
      return false
    }
    // 只显示层级比当前分类小1的分类作为父分类
    return item.level === form.level - 1
  })
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await materialsApi.getCategoryTree()
    tableData.value = response
    
    // 同时获取扁平化的分类列表用于父分类选择
    const flatResponse = await materialsApi.getCategories()
    allCategories.value = flatResponse.results || []
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

const handleEdit = (row) => {
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(form, row)
}

const handleLevelChange = () => {
  // 当层级改变时，清空父分类选择
  form.parent_code = ''
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await materialsApi.deleteCategory(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      await materialsApi.updateCategory(form.id, form)
      ElMessage.success('更新成功')
    } else {
      await materialsApi.createCategory(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  resetForm()
  formRef.value?.resetFields()
}

const resetForm = () => {
  Object.assign(form, {
    code: '',
    name: '',
    parent_code: '',
    level: 1
  })
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.category-management {
  padding: 0;
}

.page-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
}

.toolbar-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
}
</style>