/**
 * 3D渲染器配置
 * 
 * 统一管理3D渲染器的配置、性能参数和优化设置
 */

// 设备性能检测配置
export const DEVICE_PERFORMANCE_CONFIG = {
  // CPU核心数权重
  CPU_CORE_WEIGHT: 8,
  
  // 内存权重
  MEMORY_WEIGHT: 4,
  
  // 移动设备惩罚
  MOBILE_PENALTY: -15,
  
  // GPU品牌加分
  GPU_SCORES: {
    nvidia: 20,
    amd: 15,
    intel: 5,
    apple: 10
  },
  
  // 高端GPU关键词
  HIGH_END_GPU_KEYWORDS: ['rtx', 'gtx', 'radeon'],
  
  // 性能等级阈值
  PERFORMANCE_THRESHOLDS: {
    high: 80,
    medium: 60,
    low: 40
  }
}

// 渲染器配置
export const RENDERER_CONFIGS = {
  // Three.js 优化版配置
  'optimized-threejs': {
    name: 'optimized-threejs',
    title: 'Three.js 优化版',
    description: '基于Three.js的高性能渲染器，针对Web进行了深度优化',
    icon: 'el-icon-lightning',
    performance: 5,
    quality: 4,
    compatibility: 5,
    minDeviceScore: 0,
    maxDeviceScore: 100,
    
    // 质量设置
    qualitySettings: {
      high: {
        pixelRatio: 2,
        antialias: true,
        shadows: true,
        useBasicMaterial: false,
        useLOD: false,
        maxTextures: 16
      },
      medium: {
        pixelRatio: 1.5,
        antialias: false,
        shadows: false,
        useBasicMaterial: false,
        useLOD: true,
        maxTextures: 8
      },
      low: {
        pixelRatio: 1,
        antialias: false,
        shadows: false,
        useBasicMaterial: true,
        useLOD: true,
        maxTextures: 4
      }
    },
    
    // 默认优化参数
    optimizations: {
      enableGeometrySimplification: true,
      enableInstancedRendering: false,
      enableFrustumCulling: true,
      enableOcclusionCulling: false,
      enableWebWorkers: true,
      maxVertexCount: 100000,
      maxTriangleCount: 50000
    }
  },

  // Babylon.js 配置
  'babylonjs': {
    name: 'babylonjs',
    title: 'Babylon.js 引擎',
    description: '微软支持的全功能3D引擎，提供专业级渲染质量',
    icon: 'el-icon-view',
    performance: 4,
    quality: 5,
    compatibility: 4,
    minDeviceScore: 40,
    maxDeviceScore: 100,
    
    // 质量设置
    qualitySettings: {
      high: {
        renderScale: 1.0,
        antialias: true,
        shadows: true,
        postProcessing: true,
        lod: false,
        maxTextures: 16,
        physicsEnabled: true
      },
      medium: {
        renderScale: 0.8,
        antialias: false,
        shadows: false,
        postProcessing: false,
        lod: true,
        maxTextures: 8,
        physicsEnabled: false
      },
      low: {
        renderScale: 0.6,
        antialias: false,
        shadows: false,
        postProcessing: false,
        lod: true,
        maxTextures: 4,
        physicsEnabled: false
      }
    },
    
    // Babylon.js特定优化
    optimizations: {
      enableOctree: true,
      enableMerging: true,
      enableInstancing: true,
      enableLOD: true,
      freezeWorldMatrix: true,
      useIncrementalLoading: true
    }
  },

  // Three.js 标准版配置
  'threejs-basic': {
    name: 'threejs-basic',
    title: 'Three.js 标准版',
    description: '经典Three.js渲染器，兼容性最佳，适合低端设备',
    icon: 'el-icon-monitor',
    performance: 3,
    quality: 3,
    compatibility: 5,
    minDeviceScore: 0,
    maxDeviceScore: 60,
    
    // 简化的质量设置
    qualitySettings: {
      high: {
        pixelRatio: 1.5,
        antialias: false,
        shadows: false
      },
      medium: {
        pixelRatio: 1,
        antialias: false,
        shadows: false
      },
      low: {
        pixelRatio: 1,
        antialias: false,
        shadows: false
      }
    },
    
    // 保守的优化参数
    optimizations: {
      enableGeometrySimplification: false,
      enableInstancedRendering: false,
      maxVertexCount: 50000,
      maxTriangleCount: 25000
    }
  }
}

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  // FPS监控
  fps: {
    excellent: 60,
    good: 45,
    poor: 30,
    critical: 15
  },
  
  // 内存使用监控 (MB)
  memory: {
    warning: 100,
    critical: 200
  },
  
  // 渲染时间监控 (ms)
  renderTime: {
    good: 16,     // 60fps
    poor: 33,     // 30fps
    critical: 50  // 20fps
  },
  
  // 自动优化触发条件
  autoOptimization: {
    fpsThreshold: 25,
    memoryThreshold: 150,
    renderTimeThreshold: 40,
    continuousFrameCount: 30 // 连续帧数
  }
}

// 模型复杂度配置
export const MODEL_COMPLEXITY_CONFIG = {
  // 顶点数阈值
  vertexThresholds: {
    low: 10000,
    medium: 50000,
    high: 100000,
    veryHigh: 500000
  },
  
  // 文件大小阈值 (MB)
  fileSizeThresholds: {
    small: 1,
    medium: 5,
    large: 20,
    veryLarge: 50
  },
  
  // 自动简化配置
  autoSimplification: {
    enabled: true,
    ratios: {
      low: 0.9,     // 保留90%
      medium: 0.7,  // 保留70%
      high: 0.5,    // 保留50%
      veryHigh: 0.3 // 保留30%
    }
  }
}

// LOD (Level of Detail) 配置
export const LOD_CONFIG = {
  // 距离阈值
  distanceThresholds: [10, 25, 50, 100, 200],
  
  // 简化比例
  simplificationRatios: [1.0, 0.7, 0.4, 0.2, 0.1],
  
  // LOD等级描述
  levels: [
    { name: 'lod0', description: '原始质量', distance: 10 },
    { name: 'lod1', description: '高质量', distance: 25 },
    { name: 'lod2', description: '中等质量', distance: 50 },
    { name: 'lod3', description: '低质量', distance: 100 },
    { name: 'lod4', description: '最低质量', distance: 200 }
  ]
}

// Web Workers 配置
export const WEBWORKER_CONFIG = {
  enabled: true,
  maxWorkers: Math.min(navigator.hardwareConcurrency || 4, 4),
  supportedOperations: [
    'geometrySimplification',
    'normalCalculation',
    'meshOptimization',
    'textureCompression'
  ]
}

// 缓存配置
export const CACHE_CONFIG = {
  // 几何体缓存
  geometry: {
    enabled: true,
    maxSize: 50, // MB
    ttl: 1800000 // 30分钟
  },
  
  // 材质缓存
  material: {
    enabled: true,
    maxSize: 20, // MB
    ttl: 3600000 // 1小时
  },
  
  // 纹理缓存
  texture: {
    enabled: true,
    maxSize: 100, // MB
    ttl: 7200000 // 2小时
  }
}

// 自适应质量配置
export const ADAPTIVE_QUALITY_CONFIG = {
  enabled: true,
  
  // 监控间隔 (ms)
  monitorInterval: 1000,
  
  // 调整策略
  adjustmentStrategy: {
    // FPS过低时的操作顺序
    fpsOptimizations: [
      'reducPixelRatio',
      'disableAntialiasing',
      'disableShadows',
      'enableBasicMaterial',
      'simplifyGeometry',
      'enableLOD'
    ],
    
    // 内存过高时的操作
    memoryOptimizations: [
      'clearUnusedTextures',
      'compressTextures',
      'simplifyGeometry',
      'enableInstancing'
    ]
  },
  
  // 恢复条件
  recoveryConditions: {
    fpsStable: 50,    // FPS稳定在50以上
    memoryStable: 80, // 内存使用低于80MB
    stableFrames: 60  // 连续稳定帧数
  }
}

// 调试配置
export const DEBUG_CONFIG = {
  enabled: false, // 生产环境应为false
  
  // 性能统计显示
  showPerformanceStats: true,
  
  // 几何体信息显示
  showGeometryInfo: true,
  
  // 渲染调用统计
  showRenderCalls: true,
  
  // 内存使用监控
  showMemoryUsage: true,
  
  // 控制台日志级别
  logLevel: 'info' // 'debug', 'info', 'warn', 'error'
}

// 导出默认配置
export const DEFAULT_CONFIG = {
  // 自动选择引擎
  autoSelectRenderer: true,
  
  // 默认质量级别
  defaultQuality: 'medium',
  
  // 启用性能监控
  enablePerformanceMonitoring: true,
  
  // 启用自适应质量
  enableAdaptiveQuality: true,
  
  // 启用缓存
  enableCaching: true,
  
  // 启用Web Workers
  enableWebWorkers: true,
  
  // 首选渲染器（用户可以覆盖）
  preferredRenderer: null
}

/**
 * 根据设备性能推荐渲染器
 * @param {number} deviceScore 设备性能评分
 * @returns {string} 推荐的渲染器名称
 */
export function recommendRenderer(deviceScore) {
  const { PERFORMANCE_THRESHOLDS } = DEVICE_PERFORMANCE_CONFIG
  
  if (deviceScore >= PERFORMANCE_THRESHOLDS.high) {
    return 'babylonjs'
  } else if (deviceScore >= PERFORMANCE_THRESHOLDS.medium) {
    return 'optimized-threejs'
  } else {
    return 'threejs-basic'
  }
}

/**
 * 根据设备性能调整质量设置
 * @param {number} deviceScore 设备性能评分
 * @returns {string} 推荐的质量级别
 */
export function recommendQuality(deviceScore) {
  const { PERFORMANCE_THRESHOLDS } = DEVICE_PERFORMANCE_CONFIG
  
  if (deviceScore >= PERFORMANCE_THRESHOLDS.high) {
    return 'high'
  } else if (deviceScore >= PERFORMANCE_THRESHOLDS.medium) {
    return 'medium'
  } else {
    return 'low'
  }
}

/**
 * 获取渲染器配置
 * @param {string} rendererName 渲染器名称
 * @returns {Object} 渲染器配置
 */
export function getRendererConfig(rendererName) {
  return RENDERER_CONFIGS[rendererName] || RENDERER_CONFIGS['threejs-basic']
}

/**
 * 检查渲染器兼容性
 * @param {string} rendererName 渲染器名称
 * @param {number} deviceScore 设备性能评分
 * @returns {boolean} 是否兼容
 */
export function isRendererCompatible(rendererName, deviceScore) {
  const config = getRendererConfig(rendererName)
  return deviceScore >= config.minDeviceScore && deviceScore <= config.maxDeviceScore
}

export default {
  DEVICE_PERFORMANCE_CONFIG,
  RENDERER_CONFIGS,
  PERFORMANCE_CONFIG,
  MODEL_COMPLEXITY_CONFIG,
  LOD_CONFIG,
  WEBWORKER_CONFIG,
  CACHE_CONFIG,
  ADAPTIVE_QUALITY_CONFIG,
  DEBUG_CONFIG,
  DEFAULT_CONFIG,
  recommendRenderer,
  recommendQuality,
  getRendererConfig,
  isRendererCompatible
}
