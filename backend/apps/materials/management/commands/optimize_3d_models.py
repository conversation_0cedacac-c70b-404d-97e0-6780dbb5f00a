# -*- coding: utf-8 -*-
"""
Django管理命令：优化3D模型

使用方法:
python manage.py optimize_3d_models --all
python manage.py optimize_3d_models --material-id 123
python manage.py optimize_3d_models --quality medium --generate-lod
"""

import os
import json
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from apps.materials.models import Material
from utils.model_3d_optimizer import Model3DOptimizer


class Command(BaseCommand):
    help = '优化3D模型文件，提升渲染性能'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='优化所有物料的3D模型',
        )
        parser.add_argument(
            '--material-id',
            type=int,
            help='指定要优化的物料ID',
        )
        parser.add_argument(
            '--quality',
            choices=['low', 'medium', 'high'],
            default='medium',
            help='目标质量级别 (默认: medium)',
        )
        parser.add_argument(
            '--generate-lod',
            action='store_true',
            help='生成多层细节(LOD)模型',
        )
        parser.add_argument(
            '--compress',
            action='store_true',
            help='压缩优化后的模型',
        )
        parser.add_argument(
            '--backup',
            action='store_true',
            help='备份原始文件',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新优化已优化的文件',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='模拟运行，不实际修改文件',
        )

    def handle(self, *args, **options):
        self.optimizer = Model3DOptimizer()
        self.verbosity = options['verbosity']
        self.dry_run = options['dry_run']
        
        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('模拟运行模式，不会实际修改文件')
            )

        # 确定要处理的物料
        if options['all']:
            materials = Material.objects.filter(
                model_3d__isnull=False
            ).exclude(model_3d='')
            self.stdout.write(f'找到 {materials.count()} 个包含3D模型的物料')
        elif options['material_id']:
            try:
                materials = [Material.objects.get(id=options['material_id'])]
            except Material.DoesNotExist:
                raise CommandError(f'物料ID {options["material_id"]} 不存在')
        else:
            raise CommandError('请指定 --all 或 --material-id 参数')

        # 统计信息
        total_materials = len(materials)
        processed_count = 0
        optimized_count = 0
        failed_count = 0
        total_size_saved = 0

        self.stdout.write(f'\n开始处理 {total_materials} 个物料的3D模型...\n')

        for material in materials:
            processed_count += 1
            
            self.stdout.write(
                f'[{processed_count}/{total_materials}] '
                f'处理物料: {material.code} - {material.name}'
            )

            try:
                result = self.process_material_3d_model(material, options)
                
                if result.get('success'):
                    optimized_count += 1
                    size_saved = result.get('size_saved_mb', 0)
                    total_size_saved += size_saved
                    
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'  ✓ 优化完成，节省空间: {size_saved:.2f} MB'
                        )
                    )
                else:
                    failed_count += 1
                    error = result.get('error', '未知错误')
                    self.stdout.write(
                        self.style.ERROR(f'  ✗ 优化失败: {error}')
                    )

            except Exception as e:
                failed_count += 1
                self.stdout.write(
                    self.style.ERROR(f'  ✗ 处理失败: {str(e)}')
                )

        # 输出统计结果
        self.stdout.write('\n' + '='*60)
        self.stdout.write('优化完成统计:')
        self.stdout.write(f'  总物料数: {total_materials}')
        self.stdout.write(f'  成功优化: {optimized_count}')
        self.stdout.write(f'  失败数量: {failed_count}')
        self.stdout.write(f'  总节省空间: {total_size_saved:.2f} MB')
        
        if optimized_count > 0:
            avg_savings = total_size_saved / optimized_count
            self.stdout.write(f'  平均节省: {avg_savings:.2f} MB/文件')

        if not self.dry_run:
            self.stdout.write('\n建议重启前端服务以清除缓存')

    def process_material_3d_model(self, material, options):
        """处理单个物料的3D模型"""
        
        # 获取原始文件路径
        if not material.model_3d:
            return {'error': '物料没有3D模型文件'}

        original_path = os.path.join(settings.MEDIA_ROOT, str(material.model_3d))
        
        if not os.path.exists(original_path):
            return {'error': f'3D模型文件不存在: {original_path}'}

        # 分析原始模型
        if self.verbosity >= 2:
            self.stdout.write(f'    分析原始模型复杂度...')
        
        analysis = self.optimizer.analyze_model_complexity(original_path)
        
        if 'error' in analysis:
            return {'error': f'分析模型失败: {analysis["error"]}'}

        # 显示分析结果
        if self.verbosity >= 2:
            self.stdout.write(
                f'    原始模型: {analysis["faces"]} 面, '
                f'{analysis["file_size_mb"]:.2f} MB, '
                f'复杂度: {analysis["complexity_level"]}'
            )

        # 检查是否需要优化
        if not options['force'] and analysis['complexity_level'] == 'low':
            return {'success': True, 'size_saved_mb': 0, 'message': '低复杂度模型，无需优化'}

        # 准备输出路径
        original_file_path = Path(original_path)
        optimized_dir = original_file_path.parent / 'optimized'
        
        if not self.dry_run:
            optimized_dir.mkdir(exist_ok=True)

        # 备份原始文件
        if options['backup'] and not self.dry_run:
            backup_path = original_file_path.parent / 'backup' / original_file_path.name
            backup_path.parent.mkdir(exist_ok=True)
            
            if not backup_path.exists():
                import shutil
                shutil.copy2(original_path, backup_path)
                if self.verbosity >= 2:
                    self.stdout.write(f'    已备份原始文件到: {backup_path}')

        results = {
            'success': True,
            'size_saved_mb': 0,
            'operations': []
        }

        # 基础优化
        optimized_path = optimized_dir / f"{original_file_path.stem}_optimized{original_file_path.suffix}"
        
        if not self.dry_run:
            if self.verbosity >= 2:
                self.stdout.write(f'    执行基础优化 (质量级别: {options["quality"]})...')
            
            opt_result = self.optimizer.optimize_stl_model(
                original_path, 
                str(optimized_path), 
                options['quality']
            )
            
            if 'error' in opt_result:
                return {'error': f'基础优化失败: {opt_result["error"]}'}
            
            # 计算节省的空间
            size_saved = opt_result['improvements']['original_size_mb'] - \
                        opt_result['improvements']['optimized_size_mb']
            results['size_saved_mb'] += size_saved
            results['operations'].append('基础优化')

        # LOD模型生成
        if options['generate_lod']:
            lod_dir = optimized_dir / 'lod'
            
            if not self.dry_run:
                if self.verbosity >= 2:
                    self.stdout.write(f'    生成LOD模型...')
                
                lod_result = self.optimizer.generate_lod_models(
                    str(optimized_path) if optimized_path.exists() else original_path,
                    str(lod_dir)
                )
                
                if 'error' not in lod_result:
                    results['operations'].append('LOD生成')
                    if self.verbosity >= 2:
                        self.stdout.write(f'    生成了 {lod_result["total_files"]} 个LOD文件')

        # 压缩
        if options['compress']:
            compressed_path = str(optimized_path) + '.gz'
            
            if not self.dry_run:
                if self.verbosity >= 2:
                    self.stdout.write(f'    压缩优化后的模型...')
                
                compress_result = self.optimizer.compress_model(
                    str(optimized_path) if optimized_path.exists() else original_path,
                    compressed_path
                )
                
                if 'error' not in compress_result:
                    results['operations'].append('压缩')
                    additional_savings = compress_result['size_reduction_mb']
                    results['size_saved_mb'] += additional_savings
                    
                    if self.verbosity >= 2:
                        self.stdout.write(
                            f'    压缩节省: {additional_savings:.2f} MB '
                            f'(压缩率: {compress_result["compression_ratio"]:.1f}%)'
                        )

        # 保存优化报告
        if not self.dry_run and results['operations']:
            report = {
                'material_id': material.id,
                'material_code': material.code,
                'original_analysis': analysis,
                'optimization_operations': results['operations'],
                'total_size_saved_mb': results['size_saved_mb'],
                'optimized_at': str(Path().absolute())
            }
            
            report_path = optimized_dir / f"{original_file_path.stem}_optimization_report.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

        return results

    def format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes == 0:
            return "0 B"
        
        import math
        size_names = ["B", "KB", "MB", "GB"]
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
