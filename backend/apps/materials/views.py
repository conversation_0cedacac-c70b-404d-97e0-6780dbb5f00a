# -*- coding: utf-8 -*-
"""
物料管理视图
"""

from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db import models
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import os
from .models import MaterialCategory, Material, MaterialSupplier, MaterialAttribute
from .serializers import (
    MaterialCategorySerializer, MaterialSerializer, MaterialCreateSerializer,
    MaterialListSerializer, MaterialSupplierSerializer, MaterialAttributeSerializer
)


class BaseModelViewSet(viewsets.ModelViewSet):
    """基础视图集，自动处理created_by字段"""
    
    def create(self, request, *args, **kwargs):
        """创建时自动设置created_by字段"""
        created_by = getattr(request.user, 'username', 'admin') if request.user.is_authenticated else 'admin'
        data = request.data.copy()
        data['created_by'] = created_by
        
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    
    def update(self, request, *args, **kwargs):
        """更新时自动设置created_by字段"""
        created_by = getattr(request.user, 'username', 'admin') if request.user.is_authenticated else 'admin'
        data = request.data.copy()
        data['created_by'] = created_by
        
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}
        
        return Response(serializer.data)


class MaterialCategoryViewSet(BaseModelViewSet):
    """物料分类视图集"""
    
    queryset = MaterialCategory.objects.filter(is_active=True)
    serializer_class = MaterialCategorySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['level', 'parent_code']
    search_fields = ['code', 'name']
    ordering_fields = ['code', 'created_at']
    ordering = ['code']
    
    @action(detail=True, methods=['get'])
    def children(self, request, pk=None):
        """获取子分类"""
        category = self.get_object()
        children = category.get_children()
        serializer = self.get_serializer(children, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取分类树"""
        # 构建树形结构
        categories = self.get_queryset().order_by('code')
        tree_data = []
        
        # 找出根节点 - 包括parent_code为None和空字符串的情况
        root_categories = categories.filter(
            models.Q(parent_code__isnull=True) | models.Q(parent_code='')
        )
        
        def build_tree(parent_categories):
            result = []
            for category in parent_categories:
                item = MaterialCategorySerializer(category).data
                children = categories.filter(parent_code=category.code)
                if children.exists():
                    item['children'] = build_tree(children)
                    # 计算该分类下所有子分类的物料总数
                    total_materials = item['material_count']
                    for child in item['children']:
                        total_materials += child.get('total_materials', 0)
                    item['total_materials'] = total_materials
                else:
                    item['children'] = []  # 确保没有子节点时返回空数组
                    item['total_materials'] = item['material_count']
                result.append(item)
            return result
        
        tree_data = build_tree(root_categories)
        return Response(tree_data)


class MaterialViewSet(BaseModelViewSet):
    """物料视图集"""
    
    queryset = Material.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category_code', 'status', 'is_virtual', 'is_preferred']
    search_fields = ['code', 'name', 'specification']
    ordering_fields = ['code', 'created_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """重写查询集，支持category_codes和is_preferred参数"""
        queryset = super().get_queryset()
        
        # 检查是否有category_codes参数
        category_codes = self.request.query_params.get('category_codes')
        if category_codes:
            # 将逗号分隔的分类编码转换为列表
            code_list = [code.strip() for code in category_codes.split(',') if code.strip()]
            if code_list:
                queryset = queryset.filter(category_code__in=code_list)
        
        # 检查是否有is_preferred参数
        is_preferred = self.request.query_params.get('is_preferred')
        if is_preferred is not None:
            is_preferred_bool = is_preferred.lower() == 'true'
            queryset = queryset.filter(is_preferred=is_preferred_bool)
        
        return queryset
    
    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'list':
            return MaterialListSerializer
        elif self.action == 'create':
            return MaterialCreateSerializer
        return MaterialSerializer
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """激活物料"""
        material = self.get_object()
        if material.activate():
            return Response({'message': '物料已激活'})
        return Response({'error': '激活失败，请检查物料属性是否完整'}, 
                       status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """停用物料"""
        material = self.get_object()
        material.deactivate()
        return Response({'message': '物料已停用'})
    
    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """按分类获取物料"""
        category_code = request.query_params.get('category_code')
        if not category_code:
            return Response({'error': '请提供分类编码'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        materials = self.get_queryset().filter(category_code=category_code)
        serializer = MaterialListSerializer(materials, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def add_to_preferred(self, request):
        """批量添加到优选库"""
        material_ids = request.data.get('material_ids', [])
        if not material_ids:
            return Response({'error': '请提供物料ID列表'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        try:
            materials = Material.objects.filter(id__in=material_ids)
            materials.update(is_preferred=True)
            return Response({'message': f'成功添加 {materials.count()} 个物料到优选库'})
        except Exception as e:
            return Response({'error': f'添加失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def remove_from_preferred(self, request):
        """批量从优选库移除"""
        material_ids = request.data.get('material_ids', [])
        if not material_ids:
            return Response({'error': '请提供物料ID列表'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        try:
            materials = Material.objects.filter(id__in=material_ids)
            materials.update(is_preferred=False)
            return Response({'message': f'成功从优选库移除 {materials.count()} 个物料'})
        except Exception as e:
            return Response({'error': f'移除失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @method_decorator(csrf_exempt, name='dispatch')
    @action(detail=False, methods=['post'])
    def upload(self, request):
        """文件上传API"""
        file_type = request.data.get('type')
        uploaded_file = request.FILES.get('file')
        
        if not uploaded_file:
            return Response({'error': '没有上传文件'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        if not file_type:
            return Response({'error': '请指定文件类型'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        # 验证文件类型（基于文件扩展名）
        allowed_extensions = {
            'thumbnail': ['.jpg', '.jpeg', '.png', '.gif'],
            '3d_model': ['.stl', '.obj', '.step', '.stp', '.iges', '.igs', '.3ds', '.dae'],
            '2d_drawing': ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.dwg', '.dxf']
        }
        
        if file_type not in allowed_extensions:
            return Response({'error': '不支持的文件类型'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        # 检查文件扩展名
        import os
        file_extension = os.path.splitext(uploaded_file.name)[1].lower()
        if file_extension not in allowed_extensions[file_type]:
            return Response({'error': f'文件类型不匹配，期望扩展名: {allowed_extensions[file_type]}'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        # 检查文件大小
        max_sizes = {
            'thumbnail': 2 * 1024 * 1024,  # 2MB
            '3d_model': 5 * 1024 * 1024,   # 5MB
            '2d_drawing': 2 * 1024 * 1024  # 2MB
        }
        
        if uploaded_file.size > max_sizes[file_type]:
            return Response({'error': f'文件大小超过限制，最大: {max_sizes[file_type] // (1024*1024)}MB'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 生成临时文件名
            import uuid
            temp_filename = f"{uuid.uuid4()}_{uploaded_file.name}"
            
            # 保存文件到临时目录
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            temp_path = os.path.join(temp_dir, temp_filename)
            
            with open(temp_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)
            
            # 返回文件信息
            file_info = {
                'name': uploaded_file.name,
                'size': uploaded_file.size,
                'temp_path': temp_path,
                'url': f"{settings.MEDIA_URL}temp/{temp_filename}"
            }
            
            return Response(file_info)
            
        except Exception as e:
            return Response({'error': f'文件上传失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MaterialSupplierViewSet(BaseModelViewSet):
    """物料供应商视图集"""
    
    queryset = MaterialSupplier.objects.filter(is_active=True)
    serializer_class = MaterialSupplierSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['material_code', 'supplier_code', 'is_preferred']
    search_fields = ['supplier_name', 'supplier_part_no']


class MaterialAttributeViewSet(BaseModelViewSet):
    """物料属性视图集"""
    
    queryset = MaterialAttribute.objects.filter(is_active=True)
    serializer_class = MaterialAttributeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['category_code', 'data_type', 'is_required']
    search_fields = ['name', 'code']
    ordering = ['category_code', 'display_order']