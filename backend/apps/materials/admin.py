# -*- coding: utf-8 -*-
"""
物料管理后台配置
"""

from django.contrib import admin
from .models import MaterialCategory, Material, MaterialSupplier, MaterialAttribute


@admin.register(MaterialCategory)
class MaterialCategoryAdmin(admin.ModelAdmin):
    """物料分类后台管理"""
    
    list_display = ['code', 'name', 'parent_code', 'level', 'is_active', 'created_at']
    list_filter = ['level', 'is_active', 'created_at']
    search_fields = ['code', 'name']
    ordering = ['code']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('code', 'name', 'parent_code', 'level')
        }),
        ('属性配置', {
            'fields': ('attribute_template',)
        }),
        ('状态', {
            'fields': ('is_active', 'created_by')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Material)
class MaterialAdmin(admin.ModelAdmin):
    """物料后台管理"""
    
    list_display = ['code', 'name', 'category_code', 'unit', 'supplier', 'status', 'is_virtual', 'created_at']
    list_filter = ['category_code', 'status', 'is_virtual', 'created_at']
    search_fields = ['code', 'name', 'specification']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('code', 'name', 'category_code', 'specification', 'unit')
        }),
        ('供应商信息', {
            'fields': ('supplier',)
        }),
        ('扩展属性', {
            'fields': ('attributes',)
        }),
        ('状态', {
            'fields': ('status', 'is_virtual', 'created_by')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['activate_materials', 'deactivate_materials']
    
    def activate_materials(self, request, queryset):
        """批量激活物料"""
        count = 0
        for material in queryset:
            if material.activate():
                count += 1
        self.message_user(request, f'成功激活 {count} 个物料')
    activate_materials.short_description = '激活选中的物料'
    
    def deactivate_materials(self, request, queryset):
        """批量停用物料"""
        queryset.update(status='INACTIVE')
        self.message_user(request, f'成功停用 {queryset.count()} 个物料')
    deactivate_materials.short_description = '停用选中的物料'


@admin.register(MaterialSupplier)
class MaterialSupplierAdmin(admin.ModelAdmin):
    """物料供应商后台管理"""
    
    list_display = ['material_code', 'supplier_name', 'supplier_part_no', 'lead_time', 'price', 'is_preferred', 'is_active']
    list_filter = ['is_preferred', 'is_active', 'created_at']
    search_fields = ['material_code', 'supplier_name', 'supplier_part_no']
    ordering = ['material_code', 'supplier_name']


@admin.register(MaterialAttribute)
class MaterialAttributeAdmin(admin.ModelAdmin):
    """物料属性后台管理"""
    
    list_display = ['category_code', 'name', 'code', 'data_type', 'is_required', 'display_order', 'is_active']
    list_filter = ['category_code', 'data_type', 'is_required', 'is_active']
    search_fields = ['category_code', 'name', 'code']
    ordering = ['category_code', 'display_order']