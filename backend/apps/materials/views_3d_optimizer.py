# -*- coding: utf-8 -*-
"""
3D模型优化相关API视图
"""

import os
import json
import tempfile
from pathlib import Path
from django.conf import settings
from django.http import JsonResponse, FileResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from utils.model_3d_optimizer import Model3DOptimizer
from .models import Material
import logging

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def analyze_3d_model(request, material_id):
    """分析3D模型复杂度"""
    try:
        material = Material.objects.get(id=material_id)
        
        if not material.model_3d:
            return Response({
                'error': '该物料没有3D模型文件'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        model_path = os.path.join(settings.MEDIA_ROOT, str(material.model_3d))
        
        if not os.path.exists(model_path):
            return Response({
                'error': '3D模型文件不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        optimizer = Model3DOptimizer()
        analysis = optimizer.analyze_model_complexity(model_path)
        
        if 'error' in analysis:
            return Response({
                'error': f'分析失败: {analysis["error"]}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # 添加物料信息
        analysis['material'] = {
            'id': material.id,
            'code': material.code,
            'name': material.name
        }
        
        return Response(analysis)
        
    except Material.DoesNotExist:
        return Response({
            'error': '物料不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"分析3D模型时出错: {e}")
        return Response({
            'error': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def optimize_3d_model(request, material_id):
    """优化3D模型"""
    try:
        material = Material.objects.get(id=material_id)
        
        if not material.model_3d:
            return Response({
                'error': '该物料没有3D模型文件'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        model_path = os.path.join(settings.MEDIA_ROOT, str(material.model_3d))
        
        if not os.path.exists(model_path):
            return Response({
                'error': '3D模型文件不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 获取优化参数
        quality = request.data.get('quality', 'medium')
        generate_lod = request.data.get('generate_lod', False)
        compress = request.data.get('compress', False)
        
        if quality not in ['low', 'medium', 'high']:
            return Response({
                'error': '质量级别必须是 low, medium 或 high'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        optimizer = Model3DOptimizer()
        
        # 准备输出路径
        original_file_path = Path(model_path)
        optimized_dir = original_file_path.parent / 'optimized'
        optimized_dir.mkdir(exist_ok=True)
        
        optimized_path = optimized_dir / f"{original_file_path.stem}_optimized{original_file_path.suffix}"
        
        # 执行基础优化
        logger.info(f"开始优化物料 {material.code} 的3D模型")
        opt_result = optimizer.optimize_stl_model(
            model_path, 
            str(optimized_path), 
            quality
        )
        
        if 'error' in opt_result:
            return Response({
                'error': f'优化失败: {opt_result["error"]}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        response_data = {
            'success': True,
            'material': {
                'id': material.id,
                'code': material.code,
                'name': material.name
            },
            'optimization_result': opt_result,
            'files': {
                'optimized': str(optimized_path.relative_to(settings.MEDIA_ROOT))
            }
        }
        
        # LOD生成
        if generate_lod:
            lod_dir = optimized_dir / 'lod'
            lod_result = optimizer.generate_lod_models(str(optimized_path), str(lod_dir))
            
            if 'error' not in lod_result:
                response_data['lod_result'] = lod_result
                response_data['files']['lod_dir'] = str(lod_dir.relative_to(settings.MEDIA_ROOT))
        
        # 压缩
        if compress:
            compressed_path = str(optimized_path) + '.gz'
            compress_result = optimizer.compress_model(str(optimized_path), compressed_path)
            
            if 'error' not in compress_result:
                response_data['compress_result'] = compress_result
                response_data['files']['compressed'] = str(Path(compressed_path).relative_to(settings.MEDIA_ROOT))
        
        logger.info(f"物料 {material.code} 的3D模型优化完成")
        
        return Response(response_data)
        
    except Material.DoesNotExist:
        return Response({
            'error': '物料不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"优化3D模型时出错: {e}")
        return Response({
            'error': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def batch_optimize_3d_models(request):
    """批量优化3D模型"""
    try:
        material_ids = request.data.get('material_ids', [])
        quality = request.data.get('quality', 'medium')
        generate_lod = request.data.get('generate_lod', False)
        compress = request.data.get('compress', False)
        
        if not material_ids:
            return Response({
                'error': '请提供要优化的物料ID列表'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if quality not in ['low', 'medium', 'high']:
            return Response({
                'error': '质量级别必须是 low, medium 或 high'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        materials = Material.objects.filter(
            id__in=material_ids,
            model_3d__isnull=False
        ).exclude(model_3d='')
        
        if not materials.exists():
            return Response({
                'error': '没有找到包含3D模型的物料'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        optimizer = Model3DOptimizer()
        results = []
        total_size_saved = 0
        
        for material in materials:
            try:
                model_path = os.path.join(settings.MEDIA_ROOT, str(material.model_3d))
                
                if not os.path.exists(model_path):
                    results.append({
                        'material_id': material.id,
                        'material_code': material.code,
                        'success': False,
                        'error': '3D模型文件不存在'
                    })
                    continue
                
                # 准备输出路径
                original_file_path = Path(model_path)
                optimized_dir = original_file_path.parent / 'optimized'
                optimized_dir.mkdir(exist_ok=True)
                
                optimized_path = optimized_dir / f"{original_file_path.stem}_optimized{original_file_path.suffix}"
                
                # 执行优化
                opt_result = optimizer.optimize_stl_model(
                    model_path, 
                    str(optimized_path), 
                    quality
                )
                
                if 'error' in opt_result:
                    results.append({
                        'material_id': material.id,
                        'material_code': material.code,
                        'success': False,
                        'error': opt_result['error']
                    })
                    continue
                
                size_saved = opt_result['improvements']['original_size_mb'] - \
                           opt_result['improvements']['optimized_size_mb']
                total_size_saved += size_saved
                
                result_item = {
                    'material_id': material.id,
                    'material_code': material.code,
                    'material_name': material.name,
                    'success': True,
                    'size_saved_mb': round(size_saved, 2),
                    'optimization_result': opt_result
                }
                
                # LOD生成
                if generate_lod:
                    lod_dir = optimized_dir / 'lod'
                    lod_result = optimizer.generate_lod_models(str(optimized_path), str(lod_dir))
                    if 'error' not in lod_result:
                        result_item['lod_files'] = lod_result['total_files']
                
                # 压缩
                if compress:
                    compressed_path = str(optimized_path) + '.gz'
                    compress_result = optimizer.compress_model(str(optimized_path), compressed_path)
                    if 'error' not in compress_result:
                        result_item['compression_ratio'] = compress_result['compression_ratio']
                        total_size_saved += compress_result['size_reduction_mb']
                
                results.append(result_item)
                
            except Exception as e:
                logger.error(f"优化物料 {material.code} 时出错: {e}")
                results.append({
                    'material_id': material.id,
                    'material_code': material.code,
                    'success': False,
                    'error': str(e)
                })
        
        # 统计结果
        successful_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - successful_count
        
        return Response({
            'success': True,
            'summary': {
                'total_materials': len(results),
                'successful_count': successful_count,
                'failed_count': failed_count,
                'total_size_saved_mb': round(total_size_saved, 2)
            },
            'results': results
        })
        
    except Exception as e:
        logger.error(f"批量优化3D模型时出错: {e}")
        return Response({
            'error': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_optimization_report(request, material_id):
    """获取优化报告"""
    try:
        material = Material.objects.get(id=material_id)
        
        if not material.model_3d:
            return Response({
                'error': '该物料没有3D模型文件'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 查找优化报告文件
        model_path = Path(settings.MEDIA_ROOT) / str(material.model_3d)
        optimized_dir = model_path.parent / 'optimized'
        report_file = optimized_dir / f"{model_path.stem}_optimization_report.json"
        
        if not report_file.exists():
            return Response({
                'error': '未找到优化报告，请先执行优化'
            }, status=status.HTTP_404_NOT_FOUND)
        
        with open(report_file, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        return Response(report)
        
    except Material.DoesNotExist:
        return Response({
            'error': '物料不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"获取优化报告时出错: {e}")
        return Response({
            'error': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def download_optimized_model(request, material_id):
    """下载优化后的模型"""
    try:
        material = Material.objects.get(id=material_id)
        
        if not material.model_3d:
            return Response({
                'error': '该物料没有3D模型文件'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        model_path = Path(settings.MEDIA_ROOT) / str(material.model_3d)
        optimized_path = model_path.parent / 'optimized' / f"{model_path.stem}_optimized{model_path.suffix}"
        
        if not optimized_path.exists():
            return Response({
                'error': '优化后的模型文件不存在，请先执行优化'
            }, status=status.HTTP_404_NOT_FOUND)
        
        response = FileResponse(
            open(optimized_path, 'rb'),
            as_attachment=True,
            filename=f"{material.code}_optimized{model_path.suffix}"
        )
        
        return response
        
    except Material.DoesNotExist:
        return Response({
            'error': '物料不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"下载优化模型时出错: {e}")
        return Response({
            'error': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_3d_optimization_stats(request):
    """获取3D优化统计信息"""
    try:
        # 统计所有包含3D模型的物料
        total_materials = Material.objects.filter(
            model_3d__isnull=False
        ).exclude(model_3d='').count()
        
        # 统计已优化的物料（存在optimized目录的）
        optimized_count = 0
        total_original_size = 0
        total_optimized_size = 0
        
        materials_with_3d = Material.objects.filter(
            model_3d__isnull=False
        ).exclude(model_3d='')
        
        for material in materials_with_3d:
            model_path = Path(settings.MEDIA_ROOT) / str(material.model_3d)
            optimized_dir = model_path.parent / 'optimized'
            
            if optimized_dir.exists():
                optimized_count += 1
                
                # 计算文件大小
                if model_path.exists():
                    total_original_size += model_path.stat().st_size
                
                optimized_file = optimized_dir / f"{model_path.stem}_optimized{model_path.suffix}"
                if optimized_file.exists():
                    total_optimized_size += optimized_file.stat().st_size
        
        # 计算节省的空间
        size_saved_mb = (total_original_size - total_optimized_size) / 1024 / 1024
        optimization_rate = (optimized_count / total_materials * 100) if total_materials > 0 else 0
        
        return Response({
            'total_materials': total_materials,
            'optimized_count': optimized_count,
            'optimization_rate': round(optimization_rate, 2),
            'total_size_saved_mb': round(size_saved_mb, 2),
            'average_size_saved_per_file': round(size_saved_mb / optimized_count, 2) if optimized_count > 0 else 0
        })
        
    except Exception as e:
        logger.error(f"获取3D优化统计时出错: {e}")
        return Response({
            'error': f'服务器错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
