# Generated by Django 4.2.7 on 2025-08-06 03:23

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MaterialCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('code', models.CharField(help_text='分类唯一标识码', max_length=50, unique=True, verbose_name='分类编码')),
                ('name', models.CharField(max_length=200, verbose_name='分类名称')),
                ('parent_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='父分类编码')),
                ('level', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)], verbose_name='层级')),
                ('attribute_template', models.JSONField(blank=True, default=dict, help_text='该分类下物料的标准属性字段', verbose_name='属性模板')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
            ],
            options={
                'verbose_name': '物料分类',
                'verbose_name_plural': '物料分类',
                'db_table': 'material_categories',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='MaterialSupplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('material_code', models.CharField(max_length=100, verbose_name='物料编码')),
                ('supplier_code', models.CharField(max_length=100, verbose_name='供应商编码')),
                ('supplier_name', models.CharField(max_length=200, verbose_name='供应商名称')),
                ('supplier_part_no', models.CharField(blank=True, max_length=100, verbose_name='供应商料号')),
                ('lead_time', models.PositiveIntegerField(default=0, verbose_name='交货周期（天）')),
                ('min_order_qty', models.DecimalField(decimal_places=6, default=1, max_digits=15, verbose_name='最小订购量')),
                ('price', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='价格')),
                ('is_preferred', models.BooleanField(default=False, verbose_name='是否首选供应商')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
            ],
            options={
                'verbose_name': '物料供应商',
                'verbose_name_plural': '物料供应商',
                'db_table': 'material_suppliers',
                'unique_together': {('material_code', 'supplier_code')},
            },
        ),
        migrations.CreateModel(
            name='MaterialAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category_code', models.CharField(max_length=50, verbose_name='分类编码')),
                ('name', models.CharField(max_length=100, verbose_name='属性名称')),
                ('code', models.CharField(max_length=50, verbose_name='属性代码')),
                ('data_type', models.CharField(choices=[('STRING', '字符串'), ('INTEGER', '整数'), ('DECIMAL', '小数'), ('BOOLEAN', '布尔值'), ('DATE', '日期'), ('CHOICE', '选择')], default='STRING', max_length=20, verbose_name='数据类型')),
                ('is_required', models.BooleanField(default=False, verbose_name='是否必填')),
                ('default_value', models.TextField(blank=True, verbose_name='默认值')),
                ('choices', models.JSONField(blank=True, default=list, help_text='当数据类型为选择时的可选值', verbose_name='选择项')),
                ('validation_rules', models.JSONField(blank=True, default=dict, verbose_name='验证规则')),
                ('display_order', models.PositiveIntegerField(default=0, verbose_name='显示顺序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
            ],
            options={
                'verbose_name': '物料属性定义',
                'verbose_name_plural': '物料属性定义',
                'db_table': 'material_attributes',
                'ordering': ['category_code', 'display_order'],
                'unique_together': {('category_code', 'code')},
            },
        ),
        migrations.CreateModel(
            name='Material',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('code', models.CharField(max_length=100, unique=True, verbose_name='物料编码')),
                ('name', models.CharField(max_length=500, verbose_name='物料名称')),
                ('category_code', models.CharField(help_text='关联物料分类', max_length=50, verbose_name='分类编码')),
                ('specification', models.TextField(blank=True, help_text='物料详细规格描述', verbose_name='规格')),
                ('unit', models.CharField(max_length=20, verbose_name='单位')),
                ('supplier', models.CharField(blank=True, max_length=200, verbose_name='供应商')),
                ('attributes', models.JSONField(blank=True, default=dict, help_text='基于分类模板的扩展属性', verbose_name='扩展属性')),
                ('status', models.CharField(choices=[('DRAFT', '草稿'), ('ACTIVE', '活跃'), ('INACTIVE', '停用'), ('OBSOLETE', '废弃')], default='DRAFT', max_length=20, verbose_name='状态')),
                ('is_virtual', models.BooleanField(default=False, help_text='虚拟件用于BOM结构优化', verbose_name='是否虚拟件')),
            ],
            options={
                'verbose_name': '物料',
                'verbose_name_plural': '物料',
                'db_table': 'materials',
                'ordering': ['code'],
                'indexes': [models.Index(fields=['code'], name='materials_code_1ed4eb_idx'), models.Index(fields=['category_code'], name='materials_categor_4711e2_idx'), models.Index(fields=['name'], name='materials_name_7d8f27_idx'), models.Index(fields=['status'], name='materials_status_bf1b15_idx')],
            },
        ),
    ]
