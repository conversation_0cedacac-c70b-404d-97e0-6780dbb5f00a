# Generated by Django 4.2.7 on 2025-08-07 09:18

import apps.materials.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('materials', '0002_material_is_preferred'),
    ]

    operations = [
        migrations.AddField(
            model_name='material',
            name='drawing_2d',
            field=models.FileField(blank=True, help_text='支持PDF、DWG、DXF等格式', null=True, upload_to=apps.materials.models.material_file_path, verbose_name='2D图纸文件'),
        ),
        migrations.AddField(
            model_name='material',
            name='drawing_2d_name',
            field=models.CharField(blank=True, max_length=255, verbose_name='2D图纸文件名'),
        ),
        migrations.AddField(
            model_name='material',
            name='model_3d',
            field=models.FileField(blank=True, help_text='支持STL、OBJ、STEP、IGES等格式', null=True, upload_to=apps.materials.models.material_file_path, verbose_name='3D模型文件'),
        ),
        migrations.AddField(
            model_name='material',
            name='model_3d_name',
            field=models.CharField(blank=True, max_length=255, verbose_name='3D模型文件名'),
        ),
        migrations.AddField(
            model_name='material',
            name='thumbnail',
            field=models.ImageField(blank=True, help_text='物料预览图片', null=True, upload_to=apps.materials.models.material_file_path, verbose_name='缩略图'),
        ),
    ]
