# -*- coding: utf-8 -*-
"""
物料管理URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    MaterialCategoryViewSet, MaterialViewSet, 
    MaterialSupplierViewSet, MaterialAttributeViewSet
)
from . import views_3d_optimizer

router = DefaultRouter()
router.register(r'categories', MaterialCategoryViewSet)
router.register(r'materials', MaterialViewSet)
router.register(r'suppliers', MaterialSupplierViewSet)
router.register(r'attributes', MaterialAttributeViewSet)

urlpatterns = [
    path('', include(router.urls)),
    
    # 3D模型优化相关API
    path('materials/<int:material_id>/3d/analyze/', 
         views_3d_optimizer.analyze_3d_model, 
         name='analyze-3d-model'),
    path('materials/<int:material_id>/3d/optimize/', 
         views_3d_optimizer.optimize_3d_model, 
         name='optimize-3d-model'),
    path('materials/<int:material_id>/3d/report/', 
         views_3d_optimizer.get_optimization_report, 
         name='get-optimization-report'),
    path('materials/<int:material_id>/3d/download/', 
         views_3d_optimizer.download_optimized_model, 
         name='download-optimized-model'),
    path('materials/3d/batch-optimize/', 
         views_3d_optimizer.batch_optimize_3d_models, 
         name='batch-optimize-3d-models'),
    path('materials/3d/stats/', 
         views_3d_optimizer.get_3d_optimization_stats, 
         name='get-3d-optimization-stats'),
]