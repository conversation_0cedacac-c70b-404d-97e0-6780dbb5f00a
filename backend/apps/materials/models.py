# -*- coding: utf-8 -*-
"""
物料管理模型

包含物料分类、物料主数据等核心模型
遵循DRY原则和单一职责原则
"""

from django.db import models
from django.core.validators import MinValueValidator
from django.contrib.auth.models import User
import json
import os


class BaseModel(models.Model):
    """基础模型类 - 遵循DRY原则"""
    created_by = models.CharField(max_length=100, verbose_name="创建人")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        abstract = True


class MaterialCategory(BaseModel):
    """物料分类模型"""
    code = models.CharField(
        max_length=50, 
        unique=True, 
        verbose_name="分类编码",
        help_text="分类唯一标识码"
    )
    name = models.CharField(max_length=200, verbose_name="分类名称")
    parent_code = models.CharField(
        max_length=50, 
        blank=True, 
        null=True, 
        verbose_name="父分类编码"
    )
    level = models.PositiveIntegerField(
        default=1, 
        validators=[MinValueValidator(1)],
        verbose_name="层级"
    )
    attribute_template = models.JSONField(
        default=dict, 
        blank=True, 
        verbose_name="属性模板",
        help_text="该分类下物料的标准属性字段"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    
    class Meta:
        db_table = 'material_categories'
        verbose_name = "物料分类"
        verbose_name_plural = "物料分类"
        ordering = ['code']
        
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_children(self):
        """获取子分类"""
        return MaterialCategory.objects.filter(parent_code=self.code)
    
    def get_full_path(self):
        """获取完整路径"""
        path = [self.name]
        parent = MaterialCategory.objects.filter(code=self.parent_code).first()
        while parent:
            path.insert(0, parent.name)
            parent = MaterialCategory.objects.filter(code=parent.parent_code).first()
        return " > ".join(path)


def material_file_path(instance, filename):
    """生成物料文件存储路径"""
    # 根据文件类型分类存储
    file_type = '3d' if filename.lower().endswith(('.stl', '.obj', '.step', '.iges', '.3ds', '.dae')) else '2d'
    return f'materials/{file_type}/{instance.code}/{filename}'


class Material(BaseModel):
    """物料主数据模型"""
    
    STATUS_CHOICES = [
        ('DRAFT', '草稿'),
        ('ACTIVE', '活跃'),
        ('INACTIVE', '停用'),
        ('OBSOLETE', '废弃'),
    ]
    
    code = models.CharField(
        max_length=100, 
        unique=True, 
        verbose_name="物料编码"
    )
    name = models.CharField(max_length=500, verbose_name="物料名称")
    category_code = models.CharField(
        max_length=50, 
        verbose_name="分类编码",
        help_text="关联物料分类"
    )
    specification = models.TextField(
        blank=True, 
        verbose_name="规格",
        help_text="物料详细规格描述"
    )
    unit = models.CharField(max_length=20, verbose_name="单位")
    supplier = models.CharField(
        max_length=200, 
        blank=True, 
        verbose_name="供应商"
    )
    attributes = models.JSONField(
        default=dict, 
        blank=True, 
        verbose_name="扩展属性",
        help_text="基于分类模板的扩展属性"
    )
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='DRAFT', 
        verbose_name="状态"
    )
    is_virtual = models.BooleanField(
        default=False, 
        verbose_name="是否虚拟件",
        help_text="虚拟件用于BOM结构优化"
    )
    is_preferred = models.BooleanField(
        default=False, 
        verbose_name="是否加入优选库",
        help_text="优选库中的物料用于快速选择"
    )
    
    # 3D模型文件字段
    model_3d = models.FileField(
        upload_to=material_file_path,
        blank=True,
        null=True,
        verbose_name="3D模型文件",
        help_text="支持STL、OBJ、STEP、IGES等格式"
    )
    model_3d_name = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="3D模型文件名"
    )
    
    # 2D图纸文件字段
    drawing_2d = models.FileField(
        upload_to=material_file_path,
        blank=True,
        null=True,
        verbose_name="2D图纸文件",
        help_text="支持PDF、DWG、DXF等格式"
    )
    drawing_2d_name = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="2D图纸文件名"
    )
    
    # 缩略图字段
    thumbnail = models.ImageField(
        upload_to=material_file_path,
        blank=True,
        null=True,
        verbose_name="缩略图",
        help_text="物料预览图片"
    )
    
    class Meta:
        db_table = 'materials'
        verbose_name = "物料"
        verbose_name_plural = "物料"
        ordering = ['code']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['category_code']),
            models.Index(fields=['name']),
            models.Index(fields=['status']),
        ]
        
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def category(self):
        """获取关联的分类对象"""
        try:
            return MaterialCategory.objects.get(code=self.category_code)
        except MaterialCategory.DoesNotExist:
            return None
    
    def get_attribute_value(self, key, default=None):
        """安全获取扩展属性值"""
        return self.attributes.get(key, default)
    
    def set_attribute_value(self, key, value):
        """设置扩展属性值"""
        if not isinstance(self.attributes, dict):
            self.attributes = {}
        self.attributes[key] = value
        
    def validate_attributes(self):
        """验证属性是否符合分类模板"""
        category = self.category
        if not category or not category.attribute_template:
            return True
            
        # 验证必填属性
        for field_name, field_config in category.attribute_template.items():
            if field_config.get('required', False):
                if field_name not in self.attributes or not self.attributes[field_name]:
                    return False
        return True
    
    def activate(self):
        """激活物料"""
        if self.validate_attributes():
            self.status = 'ACTIVE'
            self.save()
            return True
        return False
    
    def deactivate(self):
        """停用物料"""
        self.status = 'INACTIVE'
        self.save()
    
    def get_file_info(self):
        """获取文件信息"""
        file_info = {
            'has_3d_model': bool(self.model_3d),
            'has_2d_drawing': bool(self.drawing_2d),
            'has_thumbnail': bool(self.thumbnail),
            '3d_model_name': self.model_3d_name or '',
            '2d_drawing_name': self.drawing_2d_name or '',
        }
        
        if self.model_3d:
            file_info['3d_model_url'] = f"/media/{self.model_3d}"
            file_info['3d_model_size'] = self.model_3d.size if hasattr(self.model_3d, 'size') else 0
            
        if self.drawing_2d:
            file_info['2d_drawing_url'] = f"/media/{self.drawing_2d}"
            file_info['2d_drawing_size'] = self.drawing_2d.size if hasattr(self.drawing_2d, 'size') else 0
            
        if self.thumbnail:
            file_info['thumbnail_url'] = f"/media/{self.thumbnail}"
            
        return file_info
    
    def delete_files(self):
        """删除关联的文件"""
        if self.model_3d and os.path.exists(self.model_3d.path):
            os.remove(self.model_3d.path)
        if self.drawing_2d and os.path.exists(self.drawing_2d.path):
            os.remove(self.drawing_2d.path)
        if self.thumbnail and os.path.exists(self.thumbnail.path):
            os.remove(self.thumbnail.path)


class MaterialSupplier(BaseModel):
    """物料供应商关系模型"""
    material_code = models.CharField(max_length=100, verbose_name="物料编码")
    supplier_code = models.CharField(max_length=100, verbose_name="供应商编码")
    supplier_name = models.CharField(max_length=200, verbose_name="供应商名称")
    supplier_part_no = models.CharField(
        max_length=100, 
        blank=True, 
        verbose_name="供应商料号"
    )
    lead_time = models.PositiveIntegerField(
        default=0, 
        verbose_name="交货周期（天）"
    )
    min_order_qty = models.DecimalField(
        max_digits=15, 
        decimal_places=6, 
        default=1, 
        verbose_name="最小订购量"
    )
    price = models.DecimalField(
        max_digits=15, 
        decimal_places=4, 
        default=0, 
        verbose_name="价格"
    )
    is_preferred = models.BooleanField(
        default=False, 
        verbose_name="是否首选供应商"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    
    class Meta:
        db_table = 'material_suppliers'
        verbose_name = "物料供应商"
        verbose_name_plural = "物料供应商"
        unique_together = ['material_code', 'supplier_code']
        
    def __str__(self):
        return f"{self.material_code} - {self.supplier_name}"


class MaterialAttribute(BaseModel):
    """物料属性定义模型 - 用于动态属性管理"""
    category_code = models.CharField(max_length=50, verbose_name="分类编码")
    name = models.CharField(max_length=100, verbose_name="属性名称")
    code = models.CharField(max_length=50, verbose_name="属性代码")
    data_type = models.CharField(
        max_length=20,
        choices=[
            ('STRING', '字符串'),
            ('INTEGER', '整数'),
            ('DECIMAL', '小数'),
            ('BOOLEAN', '布尔值'),
            ('DATE', '日期'),
            ('CHOICE', '选择'),
        ],
        default='STRING',
        verbose_name="数据类型"
    )
    is_required = models.BooleanField(default=False, verbose_name="是否必填")
    default_value = models.TextField(blank=True, verbose_name="默认值")
    choices = models.JSONField(
        default=list, 
        blank=True, 
        verbose_name="选择项",
        help_text="当数据类型为选择时的可选值"
    )
    validation_rules = models.JSONField(
        default=dict, 
        blank=True, 
        verbose_name="验证规则"
    )
    display_order = models.PositiveIntegerField(default=0, verbose_name="显示顺序")
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    
    class Meta:
        db_table = 'material_attributes'
        verbose_name = "物料属性定义"
        verbose_name_plural = "物料属性定义"
        unique_together = ['category_code', 'code']
        ordering = ['category_code', 'display_order']
        
    def __str__(self):
        return f"{self.category_code} - {self.name}"