# -*- coding: utf-8 -*-
"""
物料管理序列化器
"""

from rest_framework import serializers
from .models import MaterialCategory, Material, MaterialSupplier, MaterialAttribute
import os


class MaterialCategorySerializer(serializers.ModelSerializer):
    """物料分类序列化器"""
    
    children_count = serializers.SerializerMethodField()
    hasChildren = serializers.SerializerMethodField()
    material_count = serializers.SerializerMethodField()
    full_path = serializers.ReadOnlyField()
    
    class Meta:
        model = MaterialCategory
        fields = '__all__'
    
    def get_children_count(self, obj):
        """获取子分类数量"""
        return obj.get_children().count()
    
    def get_hasChildren(self, obj):
        """获取是否有子分类"""
        return obj.get_children().exists()
    
    def get_material_count(self, obj):
        """获取该分类下的物料数量"""
        from .models import Material
        return Material.objects.filter(category_code=obj.code).count()


class MaterialAttributeSerializer(serializers.ModelSerializer):
    """物料属性序列化器"""
    
    class Meta:
        model = MaterialAttribute
        fields = '__all__'


class MaterialSupplierSerializer(serializers.ModelSerializer):
    """物料供应商序列化器"""
    
    class Meta:
        model = MaterialSupplier
        fields = '__all__'


class MaterialSerializer(serializers.ModelSerializer):
    """物料序列化器"""
    
    category_name = serializers.SerializerMethodField()
    suppliers = MaterialSupplierSerializer(many=True, read_only=True, source='materialsupplier_set')
    supplier_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="供应商ID列表"
    )
    file_info = serializers.SerializerMethodField()
    
    # 添加文件相关字段
    thumbnail_temp_path = serializers.CharField(write_only=True, required=False, allow_blank=True)
    model_3d_temp_path = serializers.CharField(write_only=True, required=False, allow_blank=True)
    drawing_2d_temp_path = serializers.CharField(write_only=True, required=False, allow_blank=True)
    
    class Meta:
        model = Material
        fields = '__all__'
    
    def get_category_name(self, obj):
        """获取分类名称"""
        category = obj.category
        return category.name if category else None
    
    def get_file_info(self, obj):
        """获取文件信息"""
        return obj.get_file_info()
    
    def validate(self, data):
        """验证数据"""
        # 验证分类编码是否存在
        category_code = data.get('category_code')
        if category_code:
            try:
                MaterialCategory.objects.get(code=category_code, is_active=True)
            except MaterialCategory.DoesNotExist:
                raise serializers.ValidationError("指定的分类编码不存在或已停用")
        
        # 验证文件格式
        model_3d = data.get('model_3d')
        if model_3d:
            allowed_3d_extensions = ['.stl', '.obj', '.step', '.stp', '.iges', '.igs', '.3ds', '.dae']
            file_ext = os.path.splitext(model_3d.name)[1].lower()
            if file_ext not in allowed_3d_extensions:
                raise serializers.ValidationError("3D模型文件格式不支持，请上传STL、OBJ、STEP、STP、IGES、IGS、3DS或DAE格式文件")
        
        drawing_2d = data.get('drawing_2d')
        if drawing_2d:
            allowed_2d_extensions = ['.pdf', '.dwg', '.dxf', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp']
            file_ext = os.path.splitext(drawing_2d.name)[1].lower()
            if file_ext not in allowed_2d_extensions:
                raise serializers.ValidationError("2D图纸文件格式不支持，请上传PDF、DWG、DXF或图片格式文件")
        
        return data
    
    def create(self, validated_data):
        """创建物料并处理供应商关联"""
        supplier_ids = validated_data.pop('supplier_ids', [])
        
        # 处理文件上传
        self._process_file_uploads(validated_data)
        
        material = super().create(validated_data)
        
        # 处理供应商关联
        if supplier_ids:
            self._update_supplier_relations(material, supplier_ids)
        
        return material
    
    def update(self, instance, validated_data):
        """更新物料并处理供应商关联"""
        supplier_ids = validated_data.pop('supplier_ids', None)
        
        # 处理文件上传
        self._process_file_uploads(validated_data)
        
        # 移除非模型字段
        validated_data.pop('thumbnail_url', None)
        validated_data.pop('thumbnail_temp_path', None)
        validated_data.pop('model_3d_temp_path', None)
        validated_data.pop('drawing_2d_temp_path', None)
        
        material = super().update(instance, validated_data)
        
        # 处理供应商关联
        if supplier_ids is not None:
            self._update_supplier_relations(material, supplier_ids)
        
        return material
    
    def _process_file_uploads(self, validated_data):
        """处理文件上传"""
        import os
        from django.conf import settings
        import logging
        
        logger = logging.getLogger(__name__)
        
        # 处理缩略图
        if 'thumbnail_temp_path' in validated_data and validated_data['thumbnail_temp_path']:
            temp_path = validated_data['thumbnail_temp_path']
            logger.info(f"处理缩略图，临时路径: {temp_path}")
            
            if os.path.exists(temp_path):
                # 移动到正式目录
                material_code = validated_data.get('code', 'temp')
                final_path = f'materials/2d/{material_code}/thumbnail_{os.path.basename(temp_path)}'
                final_full_path = os.path.join(settings.MEDIA_ROOT, final_path)
                
                logger.info(f"移动文件到: {final_full_path}")
                os.makedirs(os.path.dirname(final_full_path), exist_ok=True)
                os.rename(temp_path, final_full_path)
                
                validated_data['thumbnail'] = final_path
                validated_data['thumbnail_url'] = f"{settings.MEDIA_URL}{final_path}"
                logger.info(f"缩略图处理完成: {validated_data['thumbnail_url']}")
                # 清理临时路径
                validated_data.pop('thumbnail_temp_path', None)
            else:
                logger.error(f"临时文件不存在: {temp_path}")
        else:
            logger.info("没有缩略图需要处理")
        
        # 处理3D模型
        if 'model_3d_name' in validated_data and validated_data['model_3d_name']:
            temp_path = validated_data.get('model_3d_temp_path', '')
            if temp_path and os.path.exists(temp_path):
                material_code = validated_data.get('code', 'temp')
                final_path = f'materials/3d/{material_code}/{validated_data["model_3d_name"]}'
                final_full_path = os.path.join(settings.MEDIA_ROOT, final_path)
                
                os.makedirs(os.path.dirname(final_full_path), exist_ok=True)
                os.rename(temp_path, final_full_path)
                
                validated_data['model_3d'] = final_path
                validated_data['model_3d_name'] = validated_data['model_3d_name']
        
        # 处理2D图纸
        if 'drawing_2d_name' in validated_data and validated_data['drawing_2d_name']:
            temp_path = validated_data.get('drawing_2d_temp_path', '')
            if temp_path and os.path.exists(temp_path):
                material_code = validated_data.get('code', 'temp')
                final_path = f'materials/2d/{material_code}/{validated_data["drawing_2d_name"]}'
                final_full_path = os.path.join(settings.MEDIA_ROOT, final_path)
                
                os.makedirs(os.path.dirname(final_full_path), exist_ok=True)
                os.rename(temp_path, final_full_path)
                
                validated_data['drawing_2d'] = final_path
                validated_data['drawing_2d_name'] = validated_data['drawing_2d_name']
    
    def _update_supplier_relations(self, material, supplier_ids):
        """更新物料-供应商关联关系"""
        from apps.procurement.models import Supplier
        
        # 删除现有的关联关系
        MaterialSupplier.objects.filter(material_code=material.code).delete()
        
        # 创建新的关联关系
        for supplier_id in supplier_ids:
            try:
                supplier = Supplier.objects.get(id=supplier_id, is_active=True)
                MaterialSupplier.objects.create(
                    material_code=material.code,
                    supplier_code=supplier.code,
                    supplier_name=supplier.name,
                    created_by=material.created_by
                )
            except Supplier.DoesNotExist:
                pass


class MaterialCreateSerializer(MaterialSerializer):
    """物料创建序列化器"""
    
    # 添加文件相关字段
    thumbnail_temp_path = serializers.CharField(write_only=True, required=False, allow_blank=True)
    model_3d_temp_path = serializers.CharField(write_only=True, required=False, allow_blank=True)
    drawing_2d_temp_path = serializers.CharField(write_only=True, required=False, allow_blank=True)
    
    class Meta:
        model = Material
        exclude = ['created_at', 'updated_at']
    
    def create(self, validated_data):
        """创建物料"""
        import logging
        logger = logging.getLogger(__name__)
        
        logger.info(f"创建物料，接收到的数据: {validated_data}")
        
        # 自动生成物料编码
        category_code = validated_data.get('category_code', '')
        if not validated_data.get('code'):
            # 生成编码逻辑
            count = Material.objects.filter(category_code=category_code).count()
            validated_data['code'] = f"{category_code}{count + 1:04d}"
        
        # 处理文件上传
        self._process_file_uploads(validated_data)
        
        # 移除非模型字段
        validated_data.pop('thumbnail_url', None)
        validated_data.pop('thumbnail_temp_path', None)
        validated_data.pop('model_3d_temp_path', None)
        validated_data.pop('drawing_2d_temp_path', None)
        
        logger.info(f"处理文件后的数据: {validated_data}")
        
        return super().create(validated_data)


class MaterialListSerializer(serializers.ModelSerializer):
    """物料列表序列化器（简化版）"""
    
    category_name = serializers.SerializerMethodField()
    supplier_ids = serializers.SerializerMethodField()
    file_info = serializers.SerializerMethodField()
    
    class Meta:
        model = Material
        fields = ['id', 'code', 'name', 'category_code', 'category_name', 
                 'specification', 'unit', 'status', 'created_at', 'supplier_ids', 
                 'is_preferred', 'file_info']
    
    def get_category_name(self, obj):
        """获取分类名称"""
        category = obj.category
        return category.name if category else None
    
    def get_supplier_ids(self, obj):
        """获取关联的供应商ID列表"""
        from apps.procurement.models import Supplier
        supplier_codes = MaterialSupplier.objects.filter(
            material_code=obj.code, 
            is_active=True
        ).values_list('supplier_code', flat=True)
        
        supplier_ids = []
        for supplier_code in supplier_codes:
            try:
                supplier = Supplier.objects.get(code=supplier_code, is_active=True)
                supplier_ids.append(supplier.id)
            except Supplier.DoesNotExist:
                pass
        
        return supplier_ids
    
    def get_file_info(self, obj):
        """获取文件信息"""
        return obj.get_file_info()