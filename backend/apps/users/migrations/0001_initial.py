# Generated by Django 4.2.7 on 2025-08-06 03:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('code', models.Char<PERSON>ield(max_length=50, unique=True, verbose_name='部门编码')),
                ('name', models.Char<PERSON><PERSON>(max_length=200, verbose_name='部门名称')),
                ('parent_code', models.Char<PERSON><PERSON>(blank=True, max_length=50, null=True, verbose_name='上级部门编码')),
                ('manager', models.CharField(blank=True, max_length=100, verbose_name='部门经理')),
                ('description', models.TextField(blank=True, verbose_name='部门描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
            ],
            options={
                'verbose_name': '部门',
                'verbose_name_plural': '部门',
                'db_table': 'departments',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('employee_id', models.CharField(max_length=50, unique=True, verbose_name='工号')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='部门')),
                ('position', models.CharField(blank=True, max_length=100, verbose_name='职位')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='电话')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.userprofile', verbose_name='直属上级')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户配置',
                'verbose_name_plural': '用户配置',
                'db_table': 'user_profiles',
            },
        ),
    ]
