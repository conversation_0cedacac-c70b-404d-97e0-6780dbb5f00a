# -*- coding: utf-8 -*-
"""
用户管理视图
"""

from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework.permissions import AllowAny, IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from django.db import transaction, models
from apps.materials.views import BaseModelViewSet
from .models import UserProfile, Department
from .serializers import (
    UserSerializer, UserProfileSerializer, DepartmentSerializer,
    LoginSerializer, RegisterSerializer, ChangePasswordSerializer
)


class AuthViewSet(viewsets.ViewSet):
    """认证视图集"""
    
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['post'])
    def login(self, request):
        """用户登录"""
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            username = serializer.validated_data['username']
            password = serializer.validated_data['password']
            user = authenticate(username=username, password=password)
            
            if user:
                if user.is_active:
                    token, created = Token.objects.get_or_create(user=user)
                    return Response({
                        'token': token.key,
                        'user': {
                            'id': user.id,
                            'username': user.username,
                            'email': user.email,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'is_staff': user.is_staff,
                            'is_superuser': user.is_superuser
                        },
                        'message': '登录成功'
                    })
                else:
                    return Response({
                        'error': '用户账户已被禁用'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response({
                    'error': '用户名或密码错误'
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({
                'error': '请求参数错误',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def register(self, request):
        """用户注册"""
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            with transaction.atomic():
                # 创建用户
                user_data = serializer.validated_data
                user = User.objects.create_user(
                    username=user_data['username'],
                    email=user_data.get('email', ''),
                    password=user_data['password'],
                    first_name=user_data.get('first_name', ''),
                    last_name=user_data.get('last_name', '')
                )
                
                # 创建用户配置
                profile_data = {
                    'user': user,
                    'employee_id': user_data.get('employee_id', ''),
                    'department': user_data.get('department', ''),
                    'position': user_data.get('position', ''),
                    'phone': user_data.get('phone', ''),
                    'created_by': 'system'
                }
                UserProfile.objects.create(**profile_data)
                
                # 生成token
                token = Token.objects.create(user=user)
                
                return Response({
                    'token': token.key,
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name
                    },
                    'message': '注册成功'
                }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'error': '注册失败',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def logout(self, request):
        """用户登出"""
        if request.user.is_authenticated:
            try:
                request.user.auth_token.delete()
            except:
                pass
        return Response({'message': '登出成功'})
    
    @action(detail=False, methods=['get'])
    def profile(self, request):
        """获取当前用户信息"""
        if request.user.is_authenticated:
            try:
                profile = UserProfile.objects.get(user=request.user)
                return Response({
                    'user': {
                        'id': request.user.id,
                        'username': request.user.username,
                        'email': request.user.email,
                        'first_name': request.user.first_name,
                        'last_name': request.user.last_name,
                        'is_staff': request.user.is_staff,
                        'is_superuser': request.user.is_superuser
                    },
                    'profile': UserProfileSerializer(profile).data
                })
            except UserProfile.DoesNotExist:
                return Response({
                    'user': {
                        'id': request.user.id,
                        'username': request.user.username,
                        'email': request.user.email,
                        'first_name': request.user.first_name,
                        'last_name': request.user.last_name,
                        'is_staff': request.user.is_staff,
                        'is_superuser': request.user.is_superuser
                    },
                    'profile': None
                })
        else:
            return Response({
                'error': '用户未登录'
            }, status=status.HTTP_401_UNAUTHORIZED)


class UserViewSet(BaseModelViewSet):
    """用户视图集"""
    
    queryset = User.objects.all()
    serializer_class = UserSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering = ['username']
    
    def create(self, request, *args, **kwargs):
        """创建用户时同时创建用户配置"""
        with transaction.atomic():
            # 直接创建用户对象
            user = User.objects.create_user(
                username=request.data.get('username'),
                email=request.data.get('email', ''),
                first_name=request.data.get('first_name', ''),
                password=request.data.get('password'),
                is_staff=request.data.get('is_staff', False),
                is_active=request.data.get('is_active', True)
            )
            
            # 创建用户配置
            profile_data = {
                'user': user.id,
                'department': request.data.get('department', ''),
                'position': request.data.get('position', ''),
                'phone': request.data.get('phone', ''),
                'is_active': True
            }
            
            profile_serializer = UserProfileSerializer(data=profile_data)
            if profile_serializer.is_valid():
                profile_serializer.save()
            
            # 返回序列化后的用户数据
            user_serializer = UserSerializer(user)
            return Response(user_serializer.data, status=status.HTTP_201_CREATED)
    
    def update(self, request, *args, **kwargs):
        """更新用户时同时更新用户配置"""
        with transaction.atomic():
            user = self.get_object()
            
            # 更新用户基本信息
            user_data = {
                'email': request.data.get('email', user.email),
                'first_name': request.data.get('first_name', user.first_name),
                'is_staff': request.data.get('is_staff', user.is_staff),
                'is_active': request.data.get('is_active', user.is_active)
            }
            
            user_serializer = UserSerializer(user, data=user_data, partial=True)
            user_serializer.is_valid(raise_exception=True)
            user = user_serializer.save()
            
            # 更新或创建用户配置
            try:
                profile = UserProfile.objects.get(user=user)
                profile_data = {
                    'department': request.data.get('department', profile.department),
                    'position': request.data.get('position', profile.position),
                    'phone': request.data.get('phone', profile.phone)
                }
                profile_serializer = UserProfileSerializer(profile, data=profile_data, partial=True)
            except UserProfile.DoesNotExist:
                profile_data = {
                    'user': user.id,
                    'department': request.data.get('department', ''),
                    'position': request.data.get('position', ''),
                    'phone': request.data.get('phone', ''),
                    'is_active': True
                }
                profile_serializer = UserProfileSerializer(data=profile_data)
            
            if profile_serializer.is_valid():
                profile_serializer.save()
            
            return Response(user_serializer.data)
    
    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        """修改密码"""
        user = self.get_object()
        serializer = ChangePasswordSerializer(data=request.data)
        
        if serializer.is_valid():
            old_password = serializer.validated_data['old_password']
            new_password = serializer.validated_data['new_password']
            
            if user.check_password(old_password):
                user.set_password(new_password)
                user.save()
                return Response({'message': '密码修改成功'})
            else:
                return Response({
                    'error': '原密码错误'
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({
                'error': '请求参数错误',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """激活用户"""
        user = self.get_object()
        user.is_active = True
        user.save()
        return Response({'message': '用户已激活'})
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """停用用户"""
        user = self.get_object()
        user.is_active = False
        user.save()
        return Response({'message': '用户已停用'})


class UserProfileViewSet(BaseModelViewSet):
    """用户配置视图集"""
    
    queryset = UserProfile.objects.filter(is_active=True)
    serializer_class = UserProfileSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['department', 'position']
    search_fields = ['employee_id', 'user__username']
    ordering = ['employee_id']


class DepartmentViewSet(BaseModelViewSet):
    """部门视图集"""
    
    queryset = Department.objects.filter(is_active=True)
    serializer_class = DepartmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['code', 'name', 'manager']
    ordering = ['code']
    
    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取部门树"""
        departments = self.get_queryset().order_by('code')
        tree_data = []
        
        # 找出根部门（parent_code为null或空字符串）
        root_departments = departments.filter(
            models.Q(parent_code__isnull=True) | models.Q(parent_code='')
        )
        
        def build_tree(parent_departments):
            result = []
            for dept in parent_departments:
                item = DepartmentSerializer(dept).data
                children = departments.filter(parent_code=dept.code)
                if children.exists():
                    item['children'] = build_tree(children)
                result.append(item)
            return result
        
        tree_data = build_tree(root_departments)
        return Response(tree_data)
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """激活部门"""
        dept = self.get_object()
        dept.is_active = True
        dept.save()
        return Response({'message': '部门已激活'})
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """停用部门"""
        dept = self.get_object()
        dept.is_active = False
        dept.save()
        return Response({'message': '部门已停用'})
    
    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """获取部门的用户列表"""
        try:
            department = self.get_object()
            # 获取该部门的所有用户
            users = UserProfile.objects.filter(
                department=department.code,
                is_active=True
            ).select_related('user')
            
            user_list = []
            for profile in users:
                user_list.append({
                    'id': profile.user.id,
                    'username': profile.user.username,
                    'first_name': profile.user.first_name,
                    'last_name': profile.user.last_name,
                    'email': profile.user.email,
                    'employee_id': profile.employee_id,
                    'position': profile.position
                })
            
            return Response({
                'count': len(user_list),
                'results': user_list
            })
            
        except Exception as e:
            return Response(
                {'error': f'获取部门用户失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )