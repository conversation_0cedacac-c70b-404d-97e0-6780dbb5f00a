# -*- coding: utf-8 -*-
"""
用户管理模型

扩展Django的用户功能，实现PLM系统的用户管理
"""

from django.db import models
from django.contrib.auth.models import User
from apps.materials.models import BaseModel


class UserProfile(BaseModel):
    """用户配置模型"""
    
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE, 
        verbose_name="用户"
    )
    employee_id = models.CharField(
        max_length=50, 
        unique=True, 
        null=True,
        blank=True,
        verbose_name="工号"
    )
    department = models.CharField(
        max_length=100, 
        blank=True, 
        verbose_name="部门"
    )
    position = models.CharField(
        max_length=100, 
        blank=True, 
        verbose_name="职位"
    )
    phone = models.CharField(
        max_length=20, 
        blank=True, 
        verbose_name="电话"
    )
    supervisor = models.ForeignKey(
        'self', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        verbose_name="直属上级"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = "用户配置"
        verbose_name_plural = "用户配置"
    
    def __str__(self):
        return f"{self.employee_id} - {self.user.username}"


class Department(BaseModel):
    """部门模型"""
    
    code = models.CharField(
        max_length=50, 
        unique=True, 
        verbose_name="部门编码"
    )
    name = models.CharField(max_length=200, verbose_name="部门名称")
    parent_code = models.CharField(
        max_length=50, 
        blank=True, 
        null=True, 
        verbose_name="上级部门编码"
    )
    manager = models.CharField(
        max_length=100, 
        blank=True, 
        verbose_name="部门经理"
    )
    description = models.TextField(
        blank=True, 
        verbose_name="部门描述"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    
    class Meta:
        db_table = 'departments'
        verbose_name = "部门"
        verbose_name_plural = "部门"
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.name}"
