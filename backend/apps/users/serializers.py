# -*- coding: utf-8 -*-
"""
用户管理序列化器
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from .models import UserProfile, Department


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    
    department = serializers.SerializerMethodField()
    position = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 
                 'is_active', 'is_staff', 'is_superuser', 'date_joined',
                 'department', 'position', 'phone']
        read_only_fields = ['id', 'date_joined']
    
    def get_department(self, obj):
        try:
            return obj.userprofile.department
        except UserProfile.DoesNotExist:
            return ''
    
    def get_position(self, obj):
        try:
            return obj.userprofile.position
        except UserProfile.DoesNotExist:
            return ''
    
    def get_phone(self, obj):
        try:
            return obj.userprofile.phone
        except UserProfile.DoesNotExist:
            return ''


class UserProfileSerializer(serializers.ModelSerializer):
    """用户配置序列化器"""
    
    username = serializers.CharField(source='user.username', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)
    
    class Meta:
        model = UserProfile
        fields = ['id', 'user', 'username', 'email', 'first_name', 'last_name',
                 'employee_id', 'department', 'position', 'phone', 
                 'supervisor', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class DepartmentSerializer(serializers.ModelSerializer):
    """部门序列化器"""
    
    children_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = '__all__'
    
    def get_children_count(self, obj):
        """获取子部门数量"""
        return Department.objects.filter(parent_code=obj.code, is_active=True).count()


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""
    
    username = serializers.CharField(max_length=150, help_text="用户名")
    password = serializers.CharField(max_length=128, write_only=True, help_text="密码")
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            return attrs
        else:
            raise serializers.ValidationError('用户名和密码都是必填项')


class RegisterSerializer(serializers.Serializer):
    """注册序列化器"""
    
    username = serializers.CharField(max_length=150, help_text="用户名")
    password = serializers.CharField(max_length=128, write_only=True, help_text="密码")
    email = serializers.EmailField(required=False, help_text="邮箱")
    first_name = serializers.CharField(max_length=30, required=False, help_text="名")
    last_name = serializers.CharField(max_length=30, required=False, help_text="姓")
    employee_id = serializers.CharField(max_length=50, required=False, help_text="工号")
    department = serializers.CharField(max_length=100, required=False, help_text="部门")
    position = serializers.CharField(max_length=100, required=False, help_text="职位")
    phone = serializers.CharField(max_length=20, required=False, help_text="电话")
    
    def validate_username(self, value):
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError('用户名已存在')
        return value
    
    def validate_password(self, value):
        validate_password(value)
        return value
    
    def validate_email(self, value):
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError('邮箱已存在')
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """修改密码序列化器"""
    
    old_password = serializers.CharField(max_length=128, write_only=True, help_text="原密码")
    new_password = serializers.CharField(max_length=128, write_only=True, help_text="新密码")
    
    def validate_new_password(self, value):
        # 移除密码长度限制，只做基本验证
        if not value:
            raise serializers.ValidationError('新密码不能为空')
        return value