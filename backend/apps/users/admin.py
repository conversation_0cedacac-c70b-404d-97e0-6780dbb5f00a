# -*- coding: utf-8 -*-
"""
用户管理后台配置
"""

from django.contrib import admin
from .models import UserProfile, Department


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户配置后台管理"""
    
    list_display = ['employee_id', 'user', 'department', 'position', 'phone', 'is_active']
    list_filter = ['department', 'position', 'is_active', 'created_at']
    search_fields = ['employee_id', 'user__username', 'user__email']
    ordering = ['employee_id']


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """部门后台管理"""
    
    list_display = ['code', 'name', 'parent_code', 'manager', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'manager']
    ordering = ['code']
