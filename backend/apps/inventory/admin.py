# -*- coding: utf-8 -*-
"""
库存管理后台配置
"""

from django.contrib import admin
from .models import Warehouse, InventoryRecord


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    """仓库后台管理"""
    
    list_display = ['code', 'name', 'warehouse_type', 'manager', 'location', 'is_active']
    list_filter = ['warehouse_type', 'is_active', 'created_at']
    search_fields = ['code', 'name', 'manager']
    ordering = ['code']


@admin.register(InventoryRecord)
class InventoryRecordAdmin(admin.ModelAdmin):
    """库存记录后台管理"""
    
    list_display = ['warehouse_code', 'material_code', 'quantity', 'available_quantity', 'locked_quantity', 'safety_stock']
    list_filter = ['warehouse_code', 'created_at']
    search_fields = ['material_code', 'batch_no']
    ordering = ['warehouse_code', 'material_code']
    readonly_fields = ['created_at', 'updated_at']
