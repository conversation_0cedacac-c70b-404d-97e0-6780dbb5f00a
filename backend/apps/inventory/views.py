# -*- coding: utf-8 -*-
"""
库存管理视图
"""

from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db import transaction
from decimal import Decimal
from .models import Warehouse, InventoryRecord, InventoryTransaction
from .serializers import (
    WarehouseSerializer, InventoryRecordSerializer, 
    InventoryTransactionSerializer, InventoryAdjustmentSerializer
)


class WarehouseViewSet(viewsets.ModelViewSet):
    """仓库视图集"""
    
    queryset = Warehouse.objects.filter(is_active=True)
    serializer_class = WarehouseSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['warehouse_type']
    search_fields = ['code', 'name', 'location']
    ordering = ['code']


class InventoryRecordViewSet(viewsets.ModelViewSet):
    """库存记录视图集"""
    
    queryset = InventoryRecord.objects.all()
    serializer_class = InventoryRecordSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['warehouse_code', 'material_code']
    search_fields = ['material_code', 'batch_no']
    ordering = ['warehouse_code', 'material_code']
    
    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """获取低库存预警"""
        from django.db import models as django_models
        records = self.get_queryset().filter(
            available_quantity__lt=django_models.F('safety_stock')
        )
        serializer = self.get_serializer(records, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """库存汇总"""
        material_code = request.query_params.get('material_code')
        if not material_code:
            return Response({'error': '请提供物料编码'}, 
                           status=status.HTTP_400_BAD_REQUEST)
        
        records = self.get_queryset().filter(material_code=material_code)
        total_quantity = sum(record.quantity for record in records)
        total_available = sum(record.available_quantity for record in records)
        total_locked = sum(record.locked_quantity for record in records)
        
        summary = {
            'material_code': material_code,
            'total_quantity': total_quantity,
            'total_available': total_available,
            'total_locked': total_locked,
            'warehouse_details': InventoryRecordSerializer(records, many=True).data
        }
        
        return Response(summary)
    
    @action(detail=False, methods=['post'])
    def stock_in(self, request):
        """入库操作"""
        serializer = InventoryAdjustmentSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            
            with transaction.atomic():
                # 获取或创建库存记录
                record, created = InventoryRecord.objects.get_or_create(
                    warehouse_code=data['warehouse_code'],
                    material_code=data['material_code'],
                    batch_no=data.get('batch_no', ''),
                    defaults={
                        'quantity': 0,
                        'available_quantity': 0,
                        'locked_quantity': 0,
                        'safety_stock': 0,
                        'unit_cost': data.get('unit_cost')
                    }
                )
                
                # 更新库存
                quantity = data['quantity']
                record.quantity += quantity
                record.available_quantity += quantity
                if data.get('unit_cost'):
                    record.unit_cost = data['unit_cost']
                record.save()
                
                # 创建事务记录
                transaction_no = f"IN-{record.warehouse_code}-{record.material_code}-{record.id}"
                InventoryTransaction.objects.create(
                    transaction_no=transaction_no,
                    warehouse_code=data['warehouse_code'],
                    material_code=data['material_code'],
                    batch_no=data.get('batch_no', ''),
                    transaction_type='IN',
                    quantity=quantity,
                    unit_cost=data.get('unit_cost'),
                    reference_no=data.get('reference_no', ''),
                    operator=data['operator'],
                    notes=data.get('notes', '')
                )
            
            return Response({'message': '入库成功', 'record': InventoryRecordSerializer(record).data})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def stock_out(self, request):
        """出库操作"""
        serializer = InventoryAdjustmentSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            
            try:
                with transaction.atomic():
                    # 获取库存记录
                    record = InventoryRecord.objects.get(
                        warehouse_code=data['warehouse_code'],
                        material_code=data['material_code'],
                        batch_no=data.get('batch_no', '')
                    )
                    
                    quantity = data['quantity']
                    if record.available_quantity < quantity:
                        return Response(
                            {'error': '库存不足'}, 
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    
                    # 更新库存
                    record.quantity -= quantity
                    record.available_quantity -= quantity
                    record.save()
                    
                    # 创建事务记录
                    transaction_no = f"OUT-{record.warehouse_code}-{record.material_code}-{record.id}"
                    InventoryTransaction.objects.create(
                        transaction_no=transaction_no,
                        warehouse_code=data['warehouse_code'],
                        material_code=data['material_code'],
                        batch_no=data.get('batch_no', ''),
                        transaction_type='OUT',
                        quantity=quantity,
                        unit_cost=record.unit_cost,
                        reference_no=data.get('reference_no', ''),
                        operator=data['operator'],
                        notes=data.get('notes', '')
                    )
                
                return Response({'message': '出库成功', 'record': InventoryRecordSerializer(record).data})
            
            except InventoryRecord.DoesNotExist:
                return Response(
                    {'error': '库存记录不存在'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class InventoryTransactionViewSet(viewsets.ReadOnlyModelViewSet):
    """库存事务视图集"""
    
    queryset = InventoryTransaction.objects.all()
    serializer_class = InventoryTransactionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['warehouse_code', 'material_code', 'transaction_type']
    search_fields = ['transaction_no', 'reference_no', 'operator']
    ordering = ['-created_at']