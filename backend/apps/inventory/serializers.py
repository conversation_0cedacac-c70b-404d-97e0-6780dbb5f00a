# -*- coding: utf-8 -*-
"""
库存管理序列化器
"""

from rest_framework import serializers
from .models import Warehouse, InventoryRecord, InventoryTransaction
from apps.materials.models import Material


class WarehouseSerializer(serializers.ModelSerializer):
    """仓库序列化器"""
    
    created_by = serializers.CharField(write_only=True, required=False)
    
    class Meta:
        model = Warehouse
        fields = '__all__'
    
    def create(self, validated_data):
        """创建时自动填充 created_by 字段"""
        if 'created_by' not in validated_data:
            validated_data['created_by'] = 'system'
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """更新时排除 created_by 字段"""
        # 移除 created_by 字段，因为它不应该在更新时被修改
        validated_data.pop('created_by', None)
        return super().update(instance, validated_data)


class InventoryRecordSerializer(serializers.ModelSerializer):
    """库存记录序列化器"""
    
    warehouse_name = serializers.SerializerMethodField()
    material_name = serializers.SerializerMethodField()
    total_value = serializers.ReadOnlyField()
    is_low_stock = serializers.ReadOnlyField()
    
    class Meta:
        model = InventoryRecord
        fields = '__all__'
    
    def get_warehouse_name(self, obj):
        """获取仓库名称"""
        warehouse = obj.warehouse
        return warehouse.name if warehouse else None
    
    def get_material_name(self, obj):
        """获取物料名称"""
        material = obj.material
        return material.name if material else None


class InventoryTransactionSerializer(serializers.ModelSerializer):
    """库存事务序列化器"""
    
    warehouse_name = serializers.SerializerMethodField()
    material_name = serializers.SerializerMethodField()
    
    class Meta:
        model = InventoryTransaction
        fields = '__all__'
    
    def get_warehouse_name(self, obj):
        """获取仓库名称"""
        try:
            warehouse = Warehouse.objects.get(code=obj.warehouse_code)
            return warehouse.name
        except Warehouse.DoesNotExist:
            return None
    
    def get_material_name(self, obj):
        """获取物料名称"""
        try:
            material = Material.objects.get(code=obj.material_code)
            return material.name
        except Material.DoesNotExist:
            return None


class InventoryAdjustmentSerializer(serializers.Serializer):
    """库存调整序列化器"""
    
    warehouse_code = serializers.CharField(max_length=50)
    material_code = serializers.CharField(max_length=100)
    batch_no = serializers.CharField(max_length=100, required=False, allow_blank=True)
    quantity = serializers.DecimalField(max_digits=15, decimal_places=6)
    unit_cost = serializers.DecimalField(max_digits=15, decimal_places=4, required=False, allow_null=True)
    reference_no = serializers.CharField(max_length=100, required=False, allow_blank=True)
    operator = serializers.CharField(max_length=100)
    notes = serializers.CharField(required=False, allow_blank=True)