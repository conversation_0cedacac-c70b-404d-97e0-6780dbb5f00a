# Generated by Django 4.2.7 on 2025-08-06 03:23

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.Char<PERSON>ield(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='仓库编码')),
                ('name', models.CharField(max_length=200, verbose_name='仓库名称')),
                ('location', models.CharField(blank=True, max_length=500, verbose_name='位置')),
                ('manager', models.CharField(blank=True, max_length=100, verbose_name='仓库管理员')),
                ('warehouse_type', models.CharField(choices=[('RAW_MATERIAL', '原材料仓'), ('SEMI_FINISHED', '半成品仓'), ('FINISHED', '成品仓'), ('RETURN', '退料仓')], default='RAW_MATERIAL', max_length=50, verbose_name='仓库类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
            ],
            options={
                'verbose_name': '仓库',
                'verbose_name_plural': '仓库',
                'db_table': 'warehouses',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='InventoryRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('warehouse_code', models.CharField(max_length=50, verbose_name='仓库编码')),
                ('material_code', models.CharField(max_length=100, verbose_name='物料编码')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='库位')),
                ('batch_no', models.CharField(blank=True, max_length=100, verbose_name='批次号')),
                ('quantity', models.DecimalField(decimal_places=6, default=0, max_digits=15, verbose_name='现有库存')),
                ('available_quantity', models.DecimalField(decimal_places=6, default=0, max_digits=15, verbose_name='可用库存')),
                ('locked_quantity', models.DecimalField(decimal_places=6, default=0, max_digits=15, verbose_name='锁定库存')),
                ('safety_stock', models.DecimalField(decimal_places=6, default=0, max_digits=15, verbose_name='安全库存')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True, verbose_name='单位成本')),
            ],
            options={
                'verbose_name': '库存记录',
                'verbose_name_plural': '库存记录',
                'db_table': 'inventory_records',
                'unique_together': {('warehouse_code', 'material_code', 'batch_no')},
            },
        ),
    ]
