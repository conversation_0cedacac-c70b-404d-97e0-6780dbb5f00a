# -*- coding: utf-8 -*-
"""
库存管理模型

实现完整的库存管理功能，包括：
- 库存记录
- 出入库记录
- 库存预警
- 领料单管理
"""

from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal
from apps.materials.models import BaseModel, Material


class Warehouse(BaseModel):
    """仓库模型"""
    
    code = models.CharField(max_length=50, unique=True, verbose_name="仓库编码")
    name = models.CharField(max_length=200, verbose_name="仓库名称")
    location = models.CharField(max_length=500, blank=True, verbose_name="位置")
    manager = models.CharField(max_length=100, blank=True, verbose_name="仓库管理员")
    warehouse_type = models.CharField(
        max_length=50,
        choices=[
            ('RAW_MATERIAL', '原材料仓'),
            ('SEMI_FINISHED', '半成品仓'),
            ('FINISHED', '成品仓'),
            ('RETURN', '退料仓'),
        ],
        default='RAW_MATERIAL',
        verbose_name="仓库类型"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    
    class Meta:
        db_table = 'warehouses'
        verbose_name = "仓库"
        verbose_name_plural = "仓库"
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class InventoryRecord(BaseModel):
    """库存记录模型"""
    
    warehouse_code = models.CharField(max_length=50, verbose_name="仓库编码")
    material_code = models.CharField(max_length=100, verbose_name="物料编码")
    location = models.CharField(max_length=100, blank=True, verbose_name="库位")
    batch_no = models.CharField(max_length=100, blank=True, verbose_name="批次号")
    quantity = models.DecimalField(max_digits=15, decimal_places=6, default=0, verbose_name="现有库存")
    available_quantity = models.DecimalField(max_digits=15, decimal_places=6, default=0, verbose_name="可用库存")
    locked_quantity = models.DecimalField(max_digits=15, decimal_places=6, default=0, verbose_name="锁定库存")
    safety_stock = models.DecimalField(max_digits=15, decimal_places=6, default=0, verbose_name="安全库存")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name="单位成本")
    last_updated = models.DateTimeField(auto_now=True, verbose_name="最后更新时间")
    
    class Meta:
        db_table = 'inventory_records'
        verbose_name = "库存记录"
        verbose_name_plural = "库存记录"
        unique_together = ['warehouse_code', 'material_code', 'batch_no']
        indexes = [
            models.Index(fields=['warehouse_code']),
            models.Index(fields=['material_code']),
            models.Index(fields=['available_quantity']),
        ]
    
    def __str__(self):
        return f"{self.warehouse_code} - {self.material_code} ({self.quantity})"
    
    @property
    def total_value(self):
        """总价值"""
        if self.unit_cost:
            return self.quantity * self.unit_cost
        return None
    
    @property
    def warehouse(self):
        """获取仓库对象"""
        try:
            return Warehouse.objects.get(code=self.warehouse_code)
        except Warehouse.DoesNotExist:
            return None
    
    @property
    def material(self):
        """获取物料对象"""
        from apps.materials.models import Material
        try:
            return Material.objects.get(code=self.material_code)
        except Material.DoesNotExist:
            return None
    
    def is_low_stock(self):
        """是否低库存"""
        return self.available_quantity < self.safety_stock
    
    def can_allocate(self, quantity):
        """是否可以分配指定数量"""
        return self.available_quantity >= quantity
    
    def allocate(self, quantity):
        """分配库存"""
        if self.can_allocate(quantity):
            self.available_quantity -= quantity
            self.locked_quantity += quantity
            self.save()
            return True
        return False
    
    def release(self, quantity):
        """释放锁定库存"""
        if self.locked_quantity >= quantity:
            self.locked_quantity -= quantity
            self.available_quantity += quantity
            self.save()
            return True
        return False
    
    def consume(self, quantity):
        """消耗库存"""
        if self.locked_quantity >= quantity:
            self.locked_quantity -= quantity
            self.quantity -= quantity
            self.save()
            return True
        return False


class InventoryTransaction(BaseModel):
    """库存事务记录"""
    
    TRANSACTION_TYPE_CHOICES = [
        ('IN', '入库'),
        ('OUT', '出库'),
        ('TRANSFER', '调拨'),
        ('ADJUST', '调整'),
        ('COUNT', '盘点'),
    ]
    
    transaction_no = models.CharField(max_length=100, unique=True, verbose_name="事务单号")
    warehouse_code = models.CharField(max_length=50, verbose_name="仓库编码")
    material_code = models.CharField(max_length=100, verbose_name="物料编码")
    batch_no = models.CharField(max_length=100, blank=True, verbose_name="批次号")
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES, verbose_name="事务类型")
    quantity = models.DecimalField(max_digits=15, decimal_places=6, verbose_name="数量")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, verbose_name="单位成本")
    reference_no = models.CharField(max_length=100, blank=True, verbose_name="参考单号")
    operator = models.CharField(max_length=100, verbose_name="操作人")
    notes = models.TextField(blank=True, verbose_name="备注")
    
    class Meta:
        db_table = 'inventory_transactions'
        verbose_name = "库存事务"
        verbose_name_plural = "库存事务"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['transaction_no']),
            models.Index(fields=['warehouse_code']),
            models.Index(fields=['material_code']),
            models.Index(fields=['transaction_type']),
        ]
    
    def __str__(self):
        return f"{self.transaction_no} - {self.get_transaction_type_display()}"
