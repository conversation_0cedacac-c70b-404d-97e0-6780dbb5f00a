# -*- coding: utf-8 -*-
"""
BOM管理视图
"""

from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from apps.materials.views import BaseModelViewSet
from .models import BOM, BOMItem, BOMSubstitute
from .serializers import (
    BOMSerializer, BOMCreateSerializer, BOMUpdateSerializer, BOMListSerializer,
    BOMItemSerializer, BOMSubstituteSerializer
)


class BOMViewSet(BaseModelViewSet):
    """BOM视图集"""
    
    queryset = BOM.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product_code', 'bom_type', 'status']
    search_fields = ['code', 'name', 'product_code']
    ordering_fields = ['code', 'created_at']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'list':
            return BOMListSerializer
        elif self.action == 'create':
            return BOMCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return BOMUpdateSerializer
        return BOMSerializer
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """激活BOM"""
        bom = self.get_object()
        if bom.activate():
            return Response({'message': 'BOM已激活'})
        return Response({'error': '激活失败，请确保BOM已审批'}, 
                       status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """停用BOM"""
        bom = self.get_object()
        bom.deactivate()
        return Response({'message': 'BOM已停用'})
    
    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """获取BOM明细"""
        bom = self.get_object()
        items = bom.bomitem_set.all().order_by('item_no')
        serializer = BOMItemSerializer(items, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def cost_analysis(self, request, pk=None):
        """成本分析"""
        bom = self.get_object()
        items = bom.bomitem_set.all()
        
        analysis = {
            'total_cost': bom.get_total_cost(),
            'item_count': len(items),
            'cost_breakdown': []
        }
        
        for item in items:
            if item.unit_cost:
                analysis['cost_breakdown'].append({
                    'material_code': item.material_code,
                    'material_name': item.material.name if item.material else '',
                    'quantity': item.quantity,
                    'unit': item.unit,
                    'unit_cost': item.unit_cost,
                    'extended_cost': item.extended_cost,
                    'percentage': float(item.extended_cost / bom.get_total_cost() * 100) if bom.get_total_cost() > 0 else 0
                })
        
        return Response(analysis)


class BOMItemViewSet(BaseModelViewSet):
    """BOM明细视图集"""
    
    queryset = BOMItem.objects.all()
    serializer_class = BOMItemSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['bom', 'material_code', 'phantom']
    ordering = ['bom', 'item_no']


class BOMSubstituteViewSet(BaseModelViewSet):
    """BOM替代料视图集"""
    
    queryset = BOMSubstitute.objects.filter(is_active=True)
    serializer_class = BOMSubstituteSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['bom_item', 'substitute_material_code', 'is_active']
    ordering = ['bom_item', 'priority']


