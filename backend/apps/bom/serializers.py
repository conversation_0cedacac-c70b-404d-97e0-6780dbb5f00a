# -*- coding: utf-8 -*-
"""
BOM管理序列化器
"""

from rest_framework import serializers
from django.db import models
from .models import BOM, BOMItem, BOMSubstitute
from apps.materials.models import Material


class BOMItemSerializer(serializers.ModelSerializer):
    """BOM明细序列化器"""
    
    material_name = serializers.SerializerMethodField()
    extended_cost = serializers.ReadOnlyField()
    net_quantity = serializers.ReadOnlyField()
    
    class Meta:
        model = BOMItem
        fields = '__all__'
        extra_kwargs = {
            'created_by': {'required': False},
            'bom': {'required': False}
        }
    
    def get_material_name(self, obj):
        """获取物料名称"""
        material = obj.material
        return material.name if material else None


class BOMItemCreateSerializer(serializers.ModelSerializer):
    """BOM明细创建序列化器（用于BOM创建时）"""
    
    class Meta:
        model = BOMItem
        fields = ['item_no', 'material_code', 'quantity', 'unit', 'unit_cost', 'phantom', 'notes']
        extra_kwargs = {
            'created_by': {'required': False},
            'bom': {'required': False}
        }


class BOMSubstituteSerializer(serializers.ModelSerializer):
    """BOM替代料序列化器"""
    
    substitute_material_name = serializers.SerializerMethodField()
    
    class Meta:
        model = BOMSubstitute
        fields = '__all__'
    
    def get_substitute_material_name(self, obj):
        """获取替代料名称"""
        material = obj.substitute_material
        return material.name if material else None


class BOMSerializer(serializers.ModelSerializer):
    """BOM序列化器"""
    
    product_name = serializers.SerializerMethodField()
    total_items = serializers.ReadOnlyField(source='get_total_items')
    total_cost = serializers.ReadOnlyField(source='get_total_cost')
    items = BOMItemSerializer(many=True, read_only=True, source='bomitem_set')
    
    class Meta:
        model = BOM
        fields = '__all__'
    
    def get_product_name(self, obj):
        """获取成品名称"""
        product = obj.product
        return product.name if product else None


class BOMCreateSerializer(serializers.ModelSerializer):
    """BOM创建序列化器"""
    
    bomitem_set = BOMItemCreateSerializer(many=True, required=False, default=list)
    code = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    
    class Meta:
        model = BOM
        exclude = ['created_at', 'updated_at']
    
    def create(self, validated_data):
        """创建BOM"""
        items_data = validated_data.pop('bomitem_set', [])
        
        # 自动生成BOM编码
        if not validated_data.get('code'):
            product_code = validated_data.get('product_code', '')
            count = BOM.objects.filter(product_code=product_code).count()
            validated_data['code'] = f"BOM-{product_code}-{count + 1:03d}"
        
        # 自动生成版本号（如果已存在相同产品编码的BOM）
        product_code = validated_data.get('product_code', '')
        version = validated_data.get('version', '1.0')
        
        # 检查是否已存在相同产品编码和版本号的BOM
        existing_boms = BOM.objects.filter(product_code=product_code, version=version)
        if existing_boms.exists():
            # 找到该产品编码下的最高版本号
            max_version = BOM.objects.filter(product_code=product_code).aggregate(
                max_version=models.Max('version')
            )['max_version']
            
            if max_version:
                # 解析版本号并递增
                try:
                    version_parts = max_version.split('.')
                    major = int(version_parts[0])
                    minor = int(version_parts[1]) if len(version_parts) > 1 else 0
                    new_version = f"{major}.{minor + 1}"
                except (ValueError, IndexError):
                    new_version = f"{max_version}.1"
            else:
                new_version = "1.1"
            
            validated_data['version'] = new_version
        
        bom = BOM.objects.create(**validated_data)
        
        # 创建BOM明细
        for item_data in items_data:
            # 确保created_by字段存在
            if 'created_by' not in item_data:
                item_data['created_by'] = validated_data.get('created_by', 'admin')
            # 设置bom字段
            item_data['bom'] = bom
            BOMItem.objects.create(**item_data)
        
        return bom
    
    def validate(self, data):
        """验证数据"""
        # 验证成品物料编码
        product_code = data.get('product_code')
        if product_code:
            try:
                Material.objects.get(code=product_code, status='ACTIVE')
            except Material.DoesNotExist:
                raise serializers.ValidationError("指定的成品物料不存在或未激活")
        
        return data


class BOMUpdateSerializer(serializers.ModelSerializer):
    """BOM更新序列化器"""
    
    bomitem_set = BOMItemCreateSerializer(many=True, required=False, default=list)
    
    class Meta:
        model = BOM
        exclude = ['created_at', 'updated_at', 'code', 'product_code']
    
    def update(self, instance, validated_data):
        """更新BOM"""
        items_data = validated_data.pop('bomitem_set', [])
        
        # 更新BOM基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 删除现有的BOM明细
        instance.bomitem_set.all().delete()
        
        # 创建新的BOM明细
        for item_data in items_data:
            # 确保created_by字段存在
            if 'created_by' not in item_data:
                item_data['created_by'] = validated_data.get('created_by', 'admin')
            # 设置bom字段
            item_data['bom'] = instance
            BOMItem.objects.create(**item_data)
        
        return instance
    
    def validate(self, data):
        """验证数据"""
        return data





class BOMListSerializer(serializers.ModelSerializer):
    """BOM列表序列化器（简化版）"""
    
    product_name = serializers.SerializerMethodField()
    total_items = serializers.ReadOnlyField(source='get_total_items')
    
    class Meta:
        model = BOM
        fields = ['id', 'code', 'name', 'product_code', 'product_name', 
                 'version', 'bom_type', 'status', 'total_items', 'created_at']
    
    def get_product_name(self, obj):
        """获取成品名称"""
        product = obj.product
        return product.name if product else None