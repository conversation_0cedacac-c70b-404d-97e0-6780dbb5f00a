# -*- coding: utf-8 -*-
"""
BOM（物料清单）管理模型

基于设计文档实现完整的BOM管理功能
遵循单一职责原则和DRY原则
"""

from django.db import models
from django.core.validators import MinValueValidator
from django.contrib.auth.models import User
from apps.materials.models import BaseModel, Material


class BOM(BaseModel):
    """BOM主表 - 物料清单"""
    
    STATUS_CHOICES = [
        ('DRAFT', '草稿'),
        ('PENDING', '待审批'),
        ('APPROVED', '已批准'), 
        ('ACTIVE', '生效中'),
        ('INACTIVE', '停用'),
        ('OBSOLETE', '废弃'),
    ]
    
    TYPE_CHOICES = [
        ('EBOM', '工程BOM'),  # Engineering BOM
        ('MBOM', '制造BOM'),  # Manufacturing BOM
        ('SBOM', '销售BOM'),  # Sales BOM
    ]
    
    code = models.CharField(
        max_length=100, 
        unique=True, 
        verbose_name="BOM编号"
    )
    name = models.CharField(max_length=500, verbose_name="BOM名称")
    product_code = models.CharField(
        max_length=100, 
        verbose_name="成品物料编码",
        help_text="BOM对应的成品物料"
    )
    version = models.CharField(
        max_length=50, 
        default='1.0', 
        verbose_name="版本号"
    )
    bom_type = models.CharField(
        max_length=10,
        choices=TYPE_CHOICES,
        default='EBOM',
        verbose_name="BOM类型"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='DRAFT',
        verbose_name="状态"
    )
    effective_date = models.DateField(
        null=True, 
        blank=True, 
        verbose_name="生效日期"
    )
    expiry_date = models.DateField(
        null=True, 
        blank=True, 
        verbose_name="失效日期"
    )
    description = models.TextField(
        blank=True, 
        verbose_name="说明"
    )
    approved_by = models.CharField(
        max_length=100, 
        blank=True, 
        verbose_name="审批人"
    )
    approved_at = models.DateTimeField(
        null=True, 
        blank=True, 
        verbose_name="审批时间"
    )
    
    class Meta:
        db_table = 'bom'
        verbose_name = "BOM"
        verbose_name_plural = "BOM"
        ordering = ['-created_at']
        unique_together = ['product_code', 'version']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['product_code']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name} (v{self.version})"
    
    @property
    def product(self):
        """获取成品物料对象"""
        try:
            return Material.objects.get(code=self.product_code)
        except Material.DoesNotExist:
            return None
    
    def get_total_items(self):
        """获取BOM明细总数"""
        return self.bomitem_set.count()
    
    def get_total_cost(self):
        """计算BOM总成本"""
        from decimal import Decimal
        total = Decimal('0')
        for item in self.bomitem_set.all():
            if item.unit_cost:
                total += item.unit_cost * item.quantity
        return total
    
    def activate(self):
        """激活BOM"""
        if self.status == 'APPROVED':
            self.status = 'ACTIVE'
            self.save()
            return True
        return False
    
    def deactivate(self):
        """停用BOM"""
        self.status = 'INACTIVE'
        self.save()


class BOMItem(BaseModel):
    """BOM明细项"""
    
    bom = models.ForeignKey(
        BOM, 
        on_delete=models.CASCADE, 
        verbose_name="所属BOM"
    )
    item_no = models.PositiveIntegerField(
        verbose_name="项次号",
        help_text="在BOM中的序号"
    )
    material_code = models.CharField(
        max_length=100, 
        verbose_name="物料编码"
    )
    quantity = models.DecimalField(
        max_digits=15, 
        decimal_places=6,
        validators=[MinValueValidator(0)],
        verbose_name="用量"
    )
    unit = models.CharField(
        max_length=20, 
        verbose_name="单位"
    )
    unit_cost = models.DecimalField(
        max_digits=15, 
        decimal_places=4,
        null=True, 
        blank=True,
        verbose_name="单价"
    )
    scrap_rate = models.DecimalField(
        max_digits=5, 
        decimal_places=4,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name="损耗率",
        help_text="生产损耗百分比(0-1)"
    )
    phantom = models.BooleanField(
        default=False,
        verbose_name="是否虚拟件",
        help_text="虚拟件不参与库存管理"
    )
    substitute_group = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="替代料组",
        help_text="相同组内的料可以相互替代"
    )
    reference_designator = models.CharField(
        max_length=200,
        blank=True,
        verbose_name="位号",
        help_text="在PCB或装配图中的位置标识"
    )
    notes = models.TextField(
        blank=True,
        verbose_name="备注"
    )
    
    class Meta:
        db_table = 'bom_items'
        verbose_name = "BOM明细"
        verbose_name_plural = "BOM明细"
        ordering = ['bom', 'item_no']
        unique_together = ['bom', 'item_no']
        indexes = [
            models.Index(fields=['bom', 'item_no']),
            models.Index(fields=['material_code']),
        ]
    
    def __str__(self):
        return f"{self.bom.code}-{self.item_no:03d}: {self.material_code}"
    
    @property
    def material(self):
        """获取物料对象"""
        try:
            return Material.objects.get(code=self.material_code)
        except Material.DoesNotExist:
            return None
    
    @property
    def extended_cost(self):
        """计算扩展成本（单价 × 用量）"""
        if self.unit_cost:
            return self.unit_cost * self.quantity
        return None
    
    @property
    def net_quantity(self):
        """计算净用量（含损耗）"""
        return self.quantity * (1 + self.scrap_rate)


class BOMSubstitute(BaseModel):
    """BOM替代料关系"""
    
    bom_item = models.ForeignKey(
        BOMItem, 
        on_delete=models.CASCADE, 
        verbose_name="BOM明细项"
    )
    substitute_material_code = models.CharField(
        max_length=100, 
        verbose_name="替代料编码"
    )
    priority = models.PositiveIntegerField(
        default=1,
        verbose_name="优先级",
        help_text="数字越小优先级越高"
    )
    ratio = models.DecimalField(
        max_digits=10, 
        decimal_places=6,
        default=1,
        validators=[MinValueValidator(0)],
        verbose_name="替代比例",
        help_text="替代料与主料的用量比例"
    )
    is_active = models.BooleanField(
        default=True, 
        verbose_name="是否活跃"
    )
    
    class Meta:
        db_table = 'bom_substitutes'
        verbose_name = "BOM替代料"
        verbose_name_plural = "BOM替代料"
        ordering = ['bom_item', 'priority']
        unique_together = ['bom_item', 'substitute_material_code']
    
    def __str__(self):
        return f"{self.bom_item} -> {self.substitute_material_code}"
    
    @property
    def substitute_material(self):
        """获取替代料对象"""
        try:
            return Material.objects.get(code=self.substitute_material_code)
        except Material.DoesNotExist:
            return None


class BOMChangeRequest(BaseModel):
    """BOM变更申请"""
    
    STATUS_CHOICES = [
        ('DRAFT', '草稿'),
        ('SUBMITTED', '已提交'),
        ('REVIEWING', '审核中'),
        ('APPROVED', '已批准'),
        ('REJECTED', '已拒绝'),
        ('IMPLEMENTED', '已实施'),
        ('CLOSED', '已关闭'),
    ]
    
    CHANGE_TYPE_CHOICES = [
        ('ADD', '新增项目'),
        ('MODIFY', '修改项目'),
        ('DELETE', '删除项目'),
        ('REPLACE', '替换项目'),
    ]
    
    request_no = models.CharField(
        max_length=100, 
        unique=True, 
        verbose_name="申请单号"
    )
    bom = models.ForeignKey(
        BOM, 
        on_delete=models.CASCADE, 
        verbose_name="相关BOM"
    )
    change_type = models.CharField(
        max_length=20,
        choices=CHANGE_TYPE_CHOICES,
        verbose_name="变更类型"
    )
    reason = models.TextField(
        verbose_name="变更原因"
    )
    impact_analysis = models.TextField(
        blank=True,
        verbose_name="影响分析"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='DRAFT',
        verbose_name="状态"
    )
    requested_by = models.CharField(
        max_length=100,
        verbose_name="申请人"
    )
    reviewed_by = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="审核人"
    )
    reviewed_at = models.DateTimeField(
        null=True, 
        blank=True, 
        verbose_name="审核时间"
    )
    implemented_at = models.DateTimeField(
        null=True, 
        blank=True, 
        verbose_name="实施时间"
    )
    
    class Meta:
        db_table = 'bom_change_requests'
        verbose_name = "BOM变更申请"
        verbose_name_plural = "BOM变更申请"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['request_no']),
            models.Index(fields=['bom']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.request_no} - {self.get_change_type_display()}"
    
    def submit(self):
        """提交申请"""
        if self.status == 'DRAFT':
            self.status = 'SUBMITTED'
            self.save()
            return True
        return False
    
    def approve(self, reviewer):
        """批准申请"""
        if self.status in ['SUBMITTED', 'REVIEWING']:
            self.status = 'APPROVED'
            self.reviewed_by = reviewer
            from django.utils import timezone
            self.reviewed_at = timezone.now()
            self.save()
            return True
        return False
    
    def reject(self, reviewer):
        """拒绝申请"""
        if self.status in ['SUBMITTED', 'REVIEWING']:
            self.status = 'REJECTED'
            self.reviewed_by = reviewer
            from django.utils import timezone
            self.reviewed_at = timezone.now()
            self.save()
            return True
        return False
