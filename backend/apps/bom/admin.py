# -*- coding: utf-8 -*-
"""
BOM管理后台配置
"""

from django.contrib import admin
from .models import BOM, BOMItem, BOMSubstitute, BOMChangeRequest


class BOMItemInline(admin.TabularInline):
    """BOM明细内联编辑"""
    model = BOMItem
    extra = 0
    fields = ['item_no', 'material_code', 'quantity', 'unit', 'unit_cost', 'scrap_rate', 'phantom']
    ordering = ['item_no']


@admin.register(BOM)
class BOMAdmin(admin.ModelAdmin):
    """BOM后台管理"""
    
    list_display = ['code', 'name', 'product_code', 'version', 'bom_type', 'status', 'created_at']
    list_filter = ['bom_type', 'status', 'created_at']
    search_fields = ['code', 'name', 'product_code']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [BOMItemInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('code', 'name', 'product_code', 'version', 'bom_type')
        }),
        ('生效信息', {
            'fields': ('effective_date', 'expiry_date')
        }),
        ('状态', {
            'fields': ('status', 'description', 'created_by')
        }),
        ('审批信息', {
            'fields': ('approved_by', 'approved_at'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['activate_boms', 'deactivate_boms']
    
    def activate_boms(self, request, queryset):
        """批量激活BOM"""
        count = 0
        for bom in queryset:
            if bom.activate():
                count += 1
        self.message_user(request, f'成功激活 {count} 个BOM')
    activate_boms.short_description = '激活选中的BOM'
    
    def deactivate_boms(self, request, queryset):
        """批量停用BOM"""
        for bom in queryset:
            bom.deactivate()
        self.message_user(request, f'成功停用 {queryset.count()} 个BOM')
    deactivate_boms.short_description = '停用选中的BOM'


@admin.register(BOMItem)
class BOMItemAdmin(admin.ModelAdmin):
    """BOM明细后台管理"""
    
    list_display = ['bom', 'item_no', 'material_code', 'quantity', 'unit', 'unit_cost', 'phantom']
    list_filter = ['phantom', 'created_at']
    search_fields = ['bom__code', 'material_code']
    ordering = ['bom', 'item_no']


@admin.register(BOMSubstitute)
class BOMSubstituteAdmin(admin.ModelAdmin):
    """BOM替代料后台管理"""
    
    list_display = ['bom_item', 'substitute_material_code', 'priority', 'ratio', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['bom_item__material_code', 'substitute_material_code']
    ordering = ['bom_item', 'priority']


@admin.register(BOMChangeRequest)
class BOMChangeRequestAdmin(admin.ModelAdmin):
    """BOM变更申请后台管理"""
    
    list_display = ['request_no', 'bom', 'change_type', 'status', 'requested_by', 'created_at']
    list_filter = ['change_type', 'status', 'created_at']
    search_fields = ['request_no', 'bom__code', 'requested_by']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('request_no', 'bom', 'change_type', 'requested_by')
        }),
        ('变更内容', {
            'fields': ('reason', 'impact_analysis')
        }),
        ('状态', {
            'fields': ('status',)
        }),
        ('审核信息', {
            'fields': ('reviewed_by', 'reviewed_at'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'implemented_at'),
            'classes': ('collapse',)
        }),
    )