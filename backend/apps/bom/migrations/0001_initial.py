# Generated by Django 4.2.7 on 2025-08-06 03:23

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BOM',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.Char<PERSON>ield(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('code', models.CharField(max_length=100, unique=True, verbose_name='BOM编号')),
                ('name', models.CharField(max_length=500, verbose_name='BOM名称')),
                ('product_code', models.Char<PERSON><PERSON>(help_text='BOM对应的成品物料', max_length=100, verbose_name='成品物料编码')),
                ('version', models.Char<PERSON>ield(default='1.0', max_length=50, verbose_name='版本号')),
                ('bom_type', models.CharField(choices=[('EBOM', '工程BOM'), ('MBOM', '制造BOM'), ('SBOM', '销售BOM')], default='EBOM', max_length=10, verbose_name='BOM类型')),
                ('status', models.CharField(choices=[('DRAFT', '草稿'), ('PENDING', '待审批'), ('APPROVED', '已批准'), ('ACTIVE', '生效中'), ('INACTIVE', '停用'), ('OBSOLETE', '废弃')], default='DRAFT', max_length=20, verbose_name='状态')),
                ('effective_date', models.DateField(blank=True, null=True, verbose_name='生效日期')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='失效日期')),
                ('description', models.TextField(blank=True, verbose_name='说明')),
                ('approved_by', models.CharField(blank=True, max_length=100, verbose_name='审批人')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='审批时间')),
            ],
            options={
                'verbose_name': 'BOM',
                'verbose_name_plural': 'BOM',
                'db_table': 'bom',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BOMItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('item_no', models.PositiveIntegerField(help_text='在BOM中的序号', verbose_name='项次号')),
                ('material_code', models.CharField(max_length=100, verbose_name='物料编码')),
                ('quantity', models.DecimalField(decimal_places=6, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='用量')),
                ('unit', models.CharField(max_length=20, verbose_name='单位')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True, verbose_name='单价')),
                ('scrap_rate', models.DecimalField(decimal_places=4, default=0, help_text='生产损耗百分比(0-1)', max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='损耗率')),
                ('phantom', models.BooleanField(default=False, help_text='虚拟件不参与库存管理', verbose_name='是否虚拟件')),
                ('substitute_group', models.CharField(blank=True, help_text='相同组内的料可以相互替代', max_length=50, verbose_name='替代料组')),
                ('reference_designator', models.CharField(blank=True, help_text='在PCB或装配图中的位置标识', max_length=200, verbose_name='位号')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bom.bom', verbose_name='所属BOM')),
            ],
            options={
                'verbose_name': 'BOM明细',
                'verbose_name_plural': 'BOM明细',
                'db_table': 'bom_items',
                'ordering': ['bom', 'item_no'],
            },
        ),
        migrations.CreateModel(
            name='BOMSubstitute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('substitute_material_code', models.CharField(max_length=100, verbose_name='替代料编码')),
                ('priority', models.PositiveIntegerField(default=1, help_text='数字越小优先级越高', verbose_name='优先级')),
                ('ratio', models.DecimalField(decimal_places=6, default=1, help_text='替代料与主料的用量比例', max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='替代比例')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('bom_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bom.bomitem', verbose_name='BOM明细项')),
            ],
            options={
                'verbose_name': 'BOM替代料',
                'verbose_name_plural': 'BOM替代料',
                'db_table': 'bom_substitutes',
                'ordering': ['bom_item', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='BOMChangeRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('request_no', models.CharField(max_length=100, unique=True, verbose_name='申请单号')),
                ('change_type', models.CharField(choices=[('ADD', '新增项目'), ('MODIFY', '修改项目'), ('DELETE', '删除项目'), ('REPLACE', '替换项目')], max_length=20, verbose_name='变更类型')),
                ('reason', models.TextField(verbose_name='变更原因')),
                ('impact_analysis', models.TextField(blank=True, verbose_name='影响分析')),
                ('status', models.CharField(choices=[('DRAFT', '草稿'), ('SUBMITTED', '已提交'), ('REVIEWING', '审核中'), ('APPROVED', '已批准'), ('REJECTED', '已拒绝'), ('IMPLEMENTED', '已实施'), ('CLOSED', '已关闭')], default='DRAFT', max_length=20, verbose_name='状态')),
                ('requested_by', models.CharField(max_length=100, verbose_name='申请人')),
                ('reviewed_by', models.CharField(blank=True, max_length=100, verbose_name='审核人')),
                ('reviewed_at', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('implemented_at', models.DateTimeField(blank=True, null=True, verbose_name='实施时间')),
                ('bom', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bom.bom', verbose_name='相关BOM')),
            ],
            options={
                'verbose_name': 'BOM变更申请',
                'verbose_name_plural': 'BOM变更申请',
                'db_table': 'bom_change_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='bom',
            index=models.Index(fields=['code'], name='bom_code_ed4466_idx'),
        ),
        migrations.AddIndex(
            model_name='bom',
            index=models.Index(fields=['product_code'], name='bom_product_0b5fc3_idx'),
        ),
        migrations.AddIndex(
            model_name='bom',
            index=models.Index(fields=['status'], name='bom_status_b291c8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='bom',
            unique_together={('product_code', 'version')},
        ),
        migrations.AlterUniqueTogether(
            name='bomsubstitute',
            unique_together={('bom_item', 'substitute_material_code')},
        ),
        migrations.AddIndex(
            model_name='bomitem',
            index=models.Index(fields=['bom', 'item_no'], name='bom_items_bom_id_ac764b_idx'),
        ),
        migrations.AddIndex(
            model_name='bomitem',
            index=models.Index(fields=['material_code'], name='bom_items_materia_818c97_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='bomitem',
            unique_together={('bom', 'item_no')},
        ),
        migrations.AddIndex(
            model_name='bomchangerequest',
            index=models.Index(fields=['request_no'], name='bom_change__request_6a644b_idx'),
        ),
        migrations.AddIndex(
            model_name='bomchangerequest',
            index=models.Index(fields=['bom'], name='bom_change__bom_id_7f8742_idx'),
        ),
        migrations.AddIndex(
            model_name='bomchangerequest',
            index=models.Index(fields=['status'], name='bom_change__status_754d7c_idx'),
        ),
    ]
