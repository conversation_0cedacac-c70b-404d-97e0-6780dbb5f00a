# -*- coding: utf-8 -*-
"""
采购管理模型

实现完整的采购管理功能
"""

from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal
from apps.materials.models import BaseModel, Material


class Supplier(BaseModel):
    """供应商模型"""
    
    SUPPLIER_TYPE_CHOICES = [
        ('MANUFACTURER', '制造商'),
        ('DISTRIBUTOR', '经销商'),
        ('AGENT', '代理商'),
        ('SERVICE', '服务商'),
    ]
    
    RATING_CHOICES = [
        ('A', 'A级（优秀）'),
        ('B', 'B级（良好）'),
        ('C', 'C级（一般）'),
        ('D', 'D级（较差）'),
    ]
    
    code = models.CharField(max_length=50, unique=True, verbose_name="供应商编码")
    name = models.CharField(max_length=200, verbose_name="供应商名称")
    supplier_type = models.CharField(
        max_length=20, 
        choices=SUPPLIER_TYPE_CHOICES, 
        default='MANUFACTURER',
        verbose_name="供应商类型"
    )
    rating = models.CharField(
        max_length=1, 
        choices=RATING_CHOICES, 
        default='B',
        verbose_name="供应商评级"
    )
    contact_person = models.CharField(max_length=100, blank=True, verbose_name="联系人")
    phone = models.CharField(max_length=50, blank=True, verbose_name="电话")
    email = models.EmailField(blank=True, verbose_name="邮箱")
    address = models.TextField(blank=True, verbose_name="地址")
    website = models.URLField(blank=True, verbose_name="网站")
    tax_number = models.CharField(max_length=100, blank=True, verbose_name="税号")
    bank_account = models.CharField(max_length=100, blank=True, verbose_name="银行账号")
    bank_name = models.CharField(max_length=200, blank=True, verbose_name="开户银行")
    payment_terms = models.CharField(max_length=200, blank=True, verbose_name="付款条件")
    lead_time = models.PositiveIntegerField(default=7, verbose_name="交期（天）")
    is_active = models.BooleanField(default=True, verbose_name="是否活跃")
    approved_by = models.CharField(max_length=100, blank=True, verbose_name="审批人")
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="审批时间")
    
    class Meta:
        db_table = 'suppliers'
        verbose_name = "供应商"
        verbose_name_plural = "供应商"
        ordering = ['code']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['rating']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def activate(self):
        """激活供应商"""
        self.is_active = True
        self.save()
    
    def deactivate(self):
        """停用供应商"""
        self.is_active = False
        self.save()


class PurchaseRequest(BaseModel):
    """采购申请模型"""
    
    STATUS_CHOICES = [
        ('DRAFT', '草稿'),
        ('SUBMITTED', '已提交'),
        ('APPROVED', '已批准'),
        ('REJECTED', '已拒绝'),
        ('ORDERED', '已下单'),
        ('PARTIAL_RECEIVED', '部分到货'),
        ('RECEIVED', '已到货'),
        ('CLOSED', '已关闭'),
    ]
    
    PRIORITY_CHOICES = [
        ('LOW', '低'),
        ('NORMAL', '普通'),
        ('HIGH', '高'),
        ('URGENT', '紧急'),
    ]
    
    request_no = models.CharField(max_length=100, unique=True, verbose_name="申请单号")
    department = models.CharField(max_length=100, verbose_name="申请部门")
    requested_by = models.CharField(max_length=100, verbose_name="申请人")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT', verbose_name="状态")
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='NORMAL', verbose_name="优先级")
    required_date = models.DateField(verbose_name="需求日期")
    reason = models.TextField(verbose_name="申请原因")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="总金额")
    approved_by = models.CharField(max_length=100, blank=True, verbose_name="批准人")
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="批准时间")
    rejected_reason = models.TextField(blank=True, verbose_name="拒绝原因")
    
    class Meta:
        db_table = 'purchase_requests'
        verbose_name = "采购申请"
        verbose_name_plural = "采购申请"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['request_no']),
            models.Index(fields=['status']),
            models.Index(fields=['department']),
            models.Index(fields=['required_date']),
        ]
    
    def __str__(self):
        return f"{self.request_no} - {self.department}"
    
    def submit(self):
        """提交申请"""
        if self.status == 'DRAFT':
            self.status = 'SUBMITTED'
            self.save()
            return True
        return False
    
    def approve(self, approver):
        """批准申请"""
        if self.status == 'SUBMITTED':
            self.status = 'APPROVED'
            self.approved_by = approver
            self.approved_at = timezone.now()
            self.save()
            return True
        return False
    
    def reject(self, approver, reason):
        """拒绝申请"""
        if self.status == 'SUBMITTED':
            self.status = 'REJECTED'
            self.approved_by = approver
            self.approved_at = timezone.now()
            self.rejected_reason = reason
            self.save()
            return True
        return False
    
    def calculate_total(self):
        """计算总金额"""
        total = sum(item.total_amount for item in self.purchaserequestitem_set.all())
        self.total_amount = total
        self.save()
        return total


class PurchaseRequestItem(BaseModel):
    """采购申请明细"""
    
    purchase_request = models.ForeignKey(
        PurchaseRequest, 
        on_delete=models.CASCADE, 
        verbose_name="采购申请"
    )
    item_no = models.PositiveIntegerField(verbose_name="项次号")
    material_code = models.CharField(max_length=100, verbose_name="物料编码")
    quantity = models.DecimalField(max_digits=15, decimal_places=6, verbose_name="申请数量")
    unit = models.CharField(max_length=20, verbose_name="单位")
    estimated_price = models.DecimalField(
        max_digits=15, 
        decimal_places=4, 
        null=True, 
        blank=True,
        verbose_name="预估单价"
    )
    total_amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0,
        verbose_name="小计金额"
    )
    required_date = models.DateField(verbose_name="需求日期")
    suggested_supplier = models.CharField(max_length=200, blank=True, verbose_name="建议供应商")
    specification = models.TextField(blank=True, verbose_name="规格要求")
    notes = models.TextField(blank=True, verbose_name="备注")
    
    class Meta:
        db_table = 'purchase_request_items'
        verbose_name = "采购申请明细"
        verbose_name_plural = "采购申请明细"
        ordering = ['purchase_request', 'item_no']
        unique_together = ['purchase_request', 'item_no']
        indexes = [
            models.Index(fields=['purchase_request', 'item_no']),
            models.Index(fields=['material_code']),
        ]
    
    def __str__(self):
        return f"{self.purchase_request.request_no}-{self.item_no:03d}: {self.material_code}"
    
    @property
    def material(self):
        """获取物料对象"""
        try:
            return Material.objects.get(code=self.material_code)
        except Material.DoesNotExist:
            return None
    
    def save(self, *args, **kwargs):
        """保存时自动计算小计"""
        if self.estimated_price and self.quantity:
            self.total_amount = self.estimated_price * self.quantity
        super().save(*args, **kwargs)


class SupplierQuotation(BaseModel):
    """供应商报价"""
    
    quotation_no = models.CharField(max_length=100, unique=True, verbose_name="报价单号")
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, verbose_name="供应商")
    purchase_request = models.ForeignKey(
        PurchaseRequest, 
        on_delete=models.CASCADE, 
        verbose_name="采购申请"
    )
    quoted_by = models.CharField(max_length=100, verbose_name="报价人")
    quote_date = models.DateField(verbose_name="报价日期")
    valid_until = models.DateField(verbose_name="有效期至")
    delivery_date = models.DateField(verbose_name="交货日期")
    payment_terms = models.CharField(max_length=200, verbose_name="付款条件")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="总金额")
    is_selected = models.BooleanField(default=False, verbose_name="是否中标")
    notes = models.TextField(blank=True, verbose_name="备注")
    
    class Meta:
        db_table = 'supplier_quotations'
        verbose_name = "供应商报价"
        verbose_name_plural = "供应商报价"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['quotation_no']),
            models.Index(fields=['supplier']),
            models.Index(fields=['purchase_request']),
        ]
    
    def __str__(self):
        return f"{self.quotation_no} - {self.supplier.name}"
