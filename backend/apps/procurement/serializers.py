# -*- coding: utf-8 -*-
"""
采购管理序列化器
"""

from rest_framework import serializers
from .models import Supplier, PurchaseRequest


class SupplierSerializer(serializers.ModelSerializer):
    """供应商序列化器"""
    
    created_by = serializers.CharField(write_only=True, required=False)
    
    class Meta:
        model = Supplier
        fields = [
            'id', 'code', 'name', 'supplier_type', 'rating', 'contact_person', 
            'phone', 'email', 'address', 'website', 'tax_number', 'bank_account', 
            'bank_name', 'payment_terms', 'lead_time', 'is_active', 'approved_by', 
            'approved_at', 'created_by', 'created_at', 'updated_at'
        ]
    
    def create(self, validated_data):
        """创建时自动填充 created_by 字段"""
        if 'created_by' not in validated_data:
            validated_data['created_by'] = 'system'
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """更新时排除 created_by 字段"""
        # 移除 created_by 字段，因为它不应该在更新时被修改
        validated_data.pop('created_by', None)
        return super().update(instance, validated_data)


class PurchaseRequestSerializer(serializers.ModelSerializer):
    """采购申请序列化器"""
    
    created_by = serializers.CharField(write_only=True, required=False)
    
    class Meta:
        model = PurchaseRequest
        fields = '__all__'
    
    def create(self, validated_data):
        """创建时自动填充 created_by 字段"""
        if 'created_by' not in validated_data:
            validated_data['created_by'] = 'system'
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """更新时排除 created_by 字段"""
        # 移除 created_by 字段，因为它不应该在更新时被修改
        validated_data.pop('created_by', None)
        return super().update(instance, validated_data)