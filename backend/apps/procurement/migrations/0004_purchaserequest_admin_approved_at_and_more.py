# Generated by Django 4.2.7 on 2025-08-08 08:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('procurement', '0003_remove_supplier_scoring_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='admin_approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='管理员审批时间'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='admin_approved_by',
            field=models.CharField(blank=True, max_length=100, verbose_name='管理员审批人'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='admin_rejected_reason',
            field=models.TextField(blank=True, verbose_name='管理员拒绝原因'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='dept_approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='部门审批时间'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='dept_approved_by',
            field=models.CharField(blank=True, max_length=100, verbose_name='部门审批人'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='dept_rejected_reason',
            field=models.TextField(blank=True, verbose_name='部门拒绝原因'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='status',
            field=models.CharField(choices=[('DRAFT', '草稿'), ('SUBMITTED', '已提交'), ('DEPT_APPROVED', '部门已批准'), ('ADMIN_APPROVED', '管理员已批准'), ('REJECTED', '已拒绝'), ('ORDERED', '已下单'), ('PARTIAL_RECEIVED', '部分到货'), ('RECEIVED', '已到货'), ('CLOSED', '已关闭')], default='DRAFT', max_length=20, verbose_name='状态'),
        ),
    ]
