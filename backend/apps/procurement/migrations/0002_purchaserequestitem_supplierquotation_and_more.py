# Generated by Django 4.2.7 on 2025-08-06 08:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('procurement', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseRequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('item_no', models.PositiveIntegerField(verbose_name='项次号')),
                ('material_code', models.CharField(max_length=100, verbose_name='物料编码')),
                ('quantity', models.DecimalField(decimal_places=6, max_digits=15, verbose_name='申请数量')),
                ('unit', models.CharField(max_length=20, verbose_name='单位')),
                ('estimated_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True, verbose_name='预估单价')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='小计金额')),
                ('required_date', models.DateField(verbose_name='需求日期')),
                ('suggested_supplier', models.CharField(blank=True, max_length=200, verbose_name='建议供应商')),
                ('specification', models.TextField(blank=True, verbose_name='规格要求')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '采购申请明细',
                'verbose_name_plural': '采购申请明细',
                'db_table': 'purchase_request_items',
                'ordering': ['purchase_request', 'item_no'],
            },
        ),
        migrations.CreateModel(
            name='SupplierQuotation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('quotation_no', models.CharField(max_length=100, unique=True, verbose_name='报价单号')),
                ('quoted_by', models.CharField(max_length=100, verbose_name='报价人')),
                ('quote_date', models.DateField(verbose_name='报价日期')),
                ('valid_until', models.DateField(verbose_name='有效期至')),
                ('delivery_date', models.DateField(verbose_name='交货日期')),
                ('payment_terms', models.CharField(max_length=200, verbose_name='付款条件')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='总金额')),
                ('is_selected', models.BooleanField(default=False, verbose_name='是否中标')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '供应商报价',
                'verbose_name_plural': '供应商报价',
                'db_table': 'supplier_quotations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='批准时间'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='priority',
            field=models.CharField(choices=[('LOW', '低'), ('NORMAL', '普通'), ('HIGH', '高'), ('URGENT', '紧急')], default='NORMAL', max_length=10, verbose_name='优先级'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='rejected_reason',
            field=models.TextField(blank=True, verbose_name='拒绝原因'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='总金额'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='审批时间'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='approved_by',
            field=models.CharField(blank=True, max_length=100, verbose_name='审批人'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='bank_account',
            field=models.CharField(blank=True, max_length=100, verbose_name='银行账号'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='bank_name',
            field=models.CharField(blank=True, max_length=200, verbose_name='开户银行'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='delivery_score',
            field=models.DecimalField(decimal_places=1, default=80.0, max_digits=3, verbose_name='交期评分'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='lead_time',
            field=models.PositiveIntegerField(default=7, verbose_name='交期（天）'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='payment_terms',
            field=models.CharField(blank=True, max_length=200, verbose_name='付款条件'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='quality_score',
            field=models.DecimalField(decimal_places=1, default=80.0, max_digits=3, verbose_name='质量评分'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='rating',
            field=models.CharField(choices=[('A', 'A级（优秀）'), ('B', 'B级（良好）'), ('C', 'C级（一般）'), ('D', 'D级（较差）')], default='B', max_length=1, verbose_name='供应商评级'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='service_score',
            field=models.DecimalField(decimal_places=1, default=80.0, max_digits=3, verbose_name='服务评分'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='supplier_type',
            field=models.CharField(choices=[('MANUFACTURER', '制造商'), ('DISTRIBUTOR', '经销商'), ('AGENT', '代理商'), ('SERVICE', '服务商')], default='MANUFACTURER', max_length=20, verbose_name='供应商类型'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='tax_number',
            field=models.CharField(blank=True, max_length=100, verbose_name='税号'),
        ),
        migrations.AddField(
            model_name='supplier',
            name='website',
            field=models.URLField(blank=True, verbose_name='网站'),
        ),
        migrations.AlterField(
            model_name='purchaserequest',
            name='status',
            field=models.CharField(choices=[('DRAFT', '草稿'), ('SUBMITTED', '已提交'), ('APPROVED', '已批准'), ('REJECTED', '已拒绝'), ('ORDERED', '已下单'), ('PARTIAL_RECEIVED', '部分到货'), ('RECEIVED', '已到货'), ('CLOSED', '已关闭')], default='DRAFT', max_length=20, verbose_name='状态'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['request_no'], name='purchase_re_request_1b2e6b_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['status'], name='purchase_re_status_d2f181_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['department'], name='purchase_re_departm_75a80c_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaserequest',
            index=models.Index(fields=['required_date'], name='purchase_re_require_cae034_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['code'], name='suppliers_code_e95fc6_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['rating'], name='suppliers_rating_965dcb_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['is_active'], name='suppliers_is_acti_a163b0_idx'),
        ),
        migrations.AddField(
            model_name='supplierquotation',
            name='purchase_request',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='procurement.purchaserequest', verbose_name='采购申请'),
        ),
        migrations.AddField(
            model_name='supplierquotation',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='procurement.supplier', verbose_name='供应商'),
        ),
        migrations.AddField(
            model_name='purchaserequestitem',
            name='purchase_request',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='procurement.purchaserequest', verbose_name='采购申请'),
        ),
        migrations.AddIndex(
            model_name='supplierquotation',
            index=models.Index(fields=['quotation_no'], name='supplier_qu_quotati_9130bc_idx'),
        ),
        migrations.AddIndex(
            model_name='supplierquotation',
            index=models.Index(fields=['supplier'], name='supplier_qu_supplie_df34a4_idx'),
        ),
        migrations.AddIndex(
            model_name='supplierquotation',
            index=models.Index(fields=['purchase_request'], name='supplier_qu_purchas_9aecd5_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaserequestitem',
            index=models.Index(fields=['purchase_request', 'item_no'], name='purchase_re_purchas_8037ae_idx'),
        ),
        migrations.AddIndex(
            model_name='purchaserequestitem',
            index=models.Index(fields=['material_code'], name='purchase_re_materia_b5e67d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='purchaserequestitem',
            unique_together={('purchase_request', 'item_no')},
        ),
    ]
