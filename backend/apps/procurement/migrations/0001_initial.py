# Generated by Django 4.2.7 on 2025-08-06 03:23

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('request_no', models.CharField(max_length=100, unique=True, verbose_name='申请单号')),
                ('department', models.CharField(max_length=100, verbose_name='申请部门')),
                ('requested_by', models.CharField(max_length=100, verbose_name='申请人')),
                ('status', models.CharField(choices=[('DRAFT', '草稿'), ('SUBMITTED', '已提交'), ('APPROVED', '已批准'), ('ORDERED', '已下单'), ('CLOSED', '已关闭')], default='DRAFT', max_length=20, verbose_name='状态')),
                ('required_date', models.DateField(verbose_name='需求日期')),
                ('reason', models.TextField(verbose_name='申请原因')),
                ('approved_by', models.CharField(blank=True, max_length=100, verbose_name='批准人')),
            ],
            options={
                'verbose_name': '采购申请',
                'verbose_name_plural': '采购申请',
                'db_table': 'purchase_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_by', models.CharField(max_length=100, verbose_name='创建人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='供应商编码')),
                ('name', models.CharField(max_length=200, verbose_name='供应商名称')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='联系人')),
                ('phone', models.CharField(blank=True, max_length=50, verbose_name='电话')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('address', models.TextField(blank=True, verbose_name='地址')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
            ],
            options={
                'verbose_name': '供应商',
                'verbose_name_plural': '供应商',
                'db_table': 'suppliers',
                'ordering': ['code'],
            },
        ),
    ]
