# -*- coding: utf-8 -*-
"""
采购管理后台配置
"""

from django.contrib import admin
from .models import Supplier, PurchaseRequest


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    """供应商后台管理"""
    
    list_display = ['code', 'name', 'contact_person', 'phone', 'email', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['code', 'name', 'contact_person']
    ordering = ['code']


@admin.register(PurchaseRequest)
class PurchaseRequestAdmin(admin.ModelAdmin):
    """采购申请后台管理"""
    
    list_display = ['request_no', 'department', 'requested_by', 'status', 'required_date', 'created_at']
    list_filter = ['status', 'department', 'created_at']
    search_fields = ['request_no', 'requested_by']
    ordering = ['-created_at']
