# -*- coding: utf-8 -*-
"""
采购管理视图
"""

from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import Supplier, PurchaseRequest
from .serializers import SupplierSerializer, PurchaseRequestSerializer


class SupplierViewSet(viewsets.ModelViewSet):
    """供应商视图集"""
    
    queryset = Supplier.objects.filter(is_active=True)
    serializer_class = SupplierSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['code', 'name', 'contact_person']
    ordering = ['code']


class PurchaseRequestViewSet(viewsets.ModelViewSet):
    """采购申请视图集"""
    
    queryset = PurchaseRequest.objects.all()
    serializer_class = PurchaseRequestSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['status', 'department']
    search_fields = ['request_no', 'requested_by']
    ordering = ['-created_at']