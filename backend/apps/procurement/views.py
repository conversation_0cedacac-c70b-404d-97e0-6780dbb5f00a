# -*- coding: utf-8 -*-
"""
采购管理视图
"""

from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from apps.users.models import Department, UserProfile
from .models import Supplier, PurchaseRequest
from .serializers import SupplierSerializer, PurchaseRequestSerializer


class SupplierViewSet(viewsets.ModelViewSet):
    """供应商视图集"""
    
    queryset = Supplier.objects.filter(is_active=True)
    serializer_class = SupplierSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['code', 'name', 'contact_person']
    ordering = ['code']


class PurchaseRequestViewSet(viewsets.ModelViewSet):
    """采购申请视图集"""
    
    queryset = PurchaseRequest.objects.all()
    serializer_class = PurchaseRequestSerializer
    permission_classes = [AllowAny]  # 允许匿名访问
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['status', 'department']
    search_fields = ['request_no', 'requested_by']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """根据用户权限过滤查询集"""
        queryset = super().get_queryset()
        
        # 如果用户未登录，返回空查询集
        if not self.request.user.is_authenticated:
            return PurchaseRequest.objects.none()
        
        # 检查用户是否为超级用户或管理员
        if self.request.user.is_superuser or self.request.user.is_staff:
            # 管理员可以看到所有申请记录
            return queryset
        
        # 获取用户信息
        username = self.request.user.username
        
        # 检查用户是否为部门经理
        try:
            # 查找用户所在的部门
            user_profile = UserProfile.objects.get(user__username=username)
            user_department = user_profile.department
            
            if user_department:
                # 查找该部门的经理
                department = Department.objects.get(code=user_department)
                if department.manager == username:
                    # 用户是部门经理，可以看到该部门的所有申请
                    return queryset.filter(department=user_department)
            
            # 普通用户只能看到自己的申请
            return queryset.filter(requested_by=username)
            
        except (UserProfile.DoesNotExist, Department.DoesNotExist):
            # 如果用户信息不完整，只能看到自己的申请
            return queryset.filter(requested_by=username)
    
    def create(self, request, *args, **kwargs):
        """创建采购申请时自动设置申请人信息和申请单号"""
        # 从请求中获取当前用户信息
        if request.user.is_authenticated:
            # 如果用户已登录，使用用户名作为申请人
            requested_by = request.user.username
        else:
            # 如果未登录，使用请求中的申请人信息
            requested_by = request.data.get('requested_by', '未知用户')
        
        # 复制请求数据并设置申请人
        data = request.data.copy()
        data['requested_by'] = requested_by
        
        # 自动生成申请单号
        if 'request_no' not in data or not data['request_no']:
            import datetime
            now = datetime.datetime.now()
            data['request_no'] = f"PR{now.strftime('%Y%m%d%H%M%S')}"
        
        # 确保状态值正确
        if data.get('status') == 'PENDING':
            data['status'] = 'SUBMITTED'
        
        # 创建序列化器并验证数据
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        
        # 保存采购申请
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审批采购申请"""
        try:
            purchase_request = self.get_object()
            
            # 检查用户是否有审批权限
            if not self._has_approval_permission(request.user, purchase_request):
                return Response(
                    {'error': '您没有权限审批此申请'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 根据用户角色和申请状态执行不同的审批
            # 首先检查是否为部门经理（优先进行第一级审批）
            is_dept_manager = self._is_dept_manager(request.user, purchase_request)
            
            if is_dept_manager and purchase_request.status == 'SUBMITTED':
                # 部门经理进行第一级审批
                purchase_request.approve_by_dept_manager(request.user.username)
                return Response({
                    'message': '部门经理审批成功',
                    'status': purchase_request.status,
                    'approved_by': purchase_request.dept_approved_by,
                    'approved_at': purchase_request.dept_approved_at
                })
            elif (request.user.is_superuser or request.user.is_staff) and purchase_request.status == 'DEPT_APPROVED':
                # 管理员进行第二级审批
                purchase_request.approve_by_admin(request.user.username)
                return Response({
                    'message': '管理员审批成功',
                    'status': purchase_request.status,
                    'approved_by': purchase_request.admin_approved_by,
                    'approved_at': purchase_request.admin_approved_at
                })
            else:
                if purchase_request.status == 'SUBMITTED':
                    return Response(
                        {'error': '您没有权限进行第一级审批'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                elif purchase_request.status == 'DEPT_APPROVED':
                    return Response(
                        {'error': '您没有权限进行第二级审批'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                else:
                    return Response(
                        {'error': '申请状态不允许审批'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
        except Exception as e:
            return Response(
                {'error': f'审批失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """拒绝采购申请"""
        try:
            purchase_request = self.get_object()
            rejected_reason = request.data.get('rejected_reason', '')
            
            if not rejected_reason:
                return Response(
                    {'error': '拒绝原因不能为空'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 检查用户是否有审批权限
            if not self._has_approval_permission(request.user, purchase_request):
                return Response(
                    {'error': '您没有权限审批此申请'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 根据用户角色和申请状态执行不同的拒绝
            # 首先检查是否为部门经理（优先进行第一级审批）
            is_dept_manager = self._is_dept_manager(request.user, purchase_request)
            
            if is_dept_manager and purchase_request.status == 'SUBMITTED':
                # 部门经理进行第一级拒绝
                purchase_request.reject_by_dept_manager(request.user.username, rejected_reason)
                return Response({
                    'message': '部门经理拒绝成功',
                    'status': purchase_request.status,
                    'rejected_reason': purchase_request.dept_rejected_reason
                })
            elif (request.user.is_superuser or request.user.is_staff) and purchase_request.status == 'DEPT_APPROVED':
                # 管理员进行第二级拒绝
                purchase_request.reject_by_admin(request.user.username, rejected_reason)
                return Response({
                    'message': '管理员拒绝成功',
                    'status': purchase_request.status,
                    'rejected_reason': purchase_request.admin_rejected_reason
                })
            else:
                if purchase_request.status == 'SUBMITTED':
                    return Response(
                        {'error': '您没有权限进行第一级拒绝'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                elif purchase_request.status == 'DEPT_APPROVED':
                    return Response(
                        {'error': '您没有权限进行第二级拒绝'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                else:
                    return Response(
                        {'error': '申请状态不允许拒绝'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
        except Exception as e:
            return Response(
                {'error': f'拒绝失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _has_approval_permission(self, user, purchase_request):
        """检查用户是否有审批权限"""
        # 超级用户和管理员可以审批所有申请
        if user.is_superuser or user.is_staff:
            return True
        
        # 检查是否为部门经理
        try:
            from apps.users.models import UserProfile, Department
            
            user_profile = UserProfile.objects.get(user__username=user.username)
            user_department = user_profile.department
            
            if user_department and user_department == purchase_request.department:
                # 检查用户是否为该部门的经理
                department = Department.objects.get(code=user_department)
                if department.manager == user.username:
                    return True
            
            return False
            
        except (UserProfile.DoesNotExist, Department.DoesNotExist):
            return False
    
    
    def _is_dept_manager(self, user, purchase_request):
        """检查用户是否为该申请的部门经理"""
        try:
            from apps.users.models import UserProfile, Department
            
            user_profile = UserProfile.objects.get(user__username=user.username)
            user_department = user_profile.department
            
            if user_department and user_department == purchase_request.department:
                # 检查用户是否为该部门的经理
                department = Department.objects.get(code=user_department)
                if department.manager == user.username:
                    return True
            
            return False
            
        except (UserProfile.DoesNotExist, Department.DoesNotExist):
            return False
    
    @action(detail=True, methods=['post'])
    def resubmit(self, request, pk=None):
        """重新提交被拒绝的申请"""
        try:
            purchase_request = self.get_object()
            
            # 检查用户是否有权限重新提交
            if purchase_request.requested_by != request.user.username:
                return Response(
                    {'error': '只有申请人才能重新提交申请'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 检查申请状态
            if purchase_request.status != 'REJECTED':
                return Response(
                    {'error': '只能重新提交被拒绝的申请'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 执行重新提交
            if purchase_request.resubmit():
                return Response({
                    'message': '重新提交成功',
                    'status': purchase_request.status
                })
            else:
                return Response(
                    {'error': '重新提交失败'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            
        except Exception as e:
            return Response(
                {'error': f'重新提交失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def update_notes(self, request, pk=None):
        """更新申请人备注"""
        try:
            purchase_request = self.get_object()
            notes = request.data.get('notes', '')
            
            # 检查用户是否有权限更新备注
            if purchase_request.requested_by != request.user.username:
                return Response(
                    {'error': '只有申请人才能更新备注'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 更新备注
            purchase_request.notes = notes
            purchase_request.save()
            
            return Response({
                'message': '备注更新成功',
                'notes': purchase_request.notes
            })
            
        except Exception as e:
            return Response(
                {'error': f'备注更新失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def destroy(self, request, *args, **kwargs):
        """删除采购申请记录 - 只有管理员可以删除"""
        try:
            purchase_request = self.get_object()
            
            # 检查用户是否为管理员
            if not (request.user.is_superuser or request.user.is_staff):
                return Response(
                    {'error': '只有管理员才能删除采购申请记录'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 记录删除操作
            request_no = purchase_request.request_no
            purchase_request.delete()
            
            return Response({
                'message': f'采购申请记录 "{request_no}" 已成功删除'
            })
            
        except Exception as e:
            return Response(
                {'error': f'删除失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )