#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试大STL文件加载

验证大文件（957KB）的加载性能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

from django.conf import settings
from apps.materials.models import Material

def test_large_stl_file():
    """测试大STL文件加载"""
    print("=" * 60)
    print("测试大STL文件加载")
    print("=" * 60)
    
    # 查找包含大STL文件的物料
    materials_with_3d = Material.objects.filter(
        model_3d__isnull=False,
        model_3d_name__icontains='.stl'
    ).exclude(model_3d='')
    
    print(f"找到 {materials_with_3d.count()} 个包含STL文件的物料")
    
    for material in materials_with_3d:
        print(f"\n物料: {material.code} - {material.name}")
        
        # 获取文件信息
        file_info = material.get_file_info()
        
        if file_info.get('has_3d_model'):
            print(f"  3D模型文件: {file_info.get('3d_model_name')}")
            print(f"  文件大小: {file_info.get('3d_model_size')} bytes")
            print(f"  文件大小(MB): {file_info.get('3d_model_size') / 1024 / 1024:.2f} MB")
            print(f"  文件URL: {file_info.get('3d_model_url')}")
            
            # 检查文件是否存在
            model_path = os.path.join(settings.MEDIA_ROOT, str(material.model_3d))
            if os.path.exists(model_path):
                actual_size = os.path.getsize(model_path)
                print(f"  ✓ 文件存在，实际大小: {actual_size} bytes ({actual_size / 1024 / 1024:.2f} MB)")
                
                # 性能评估
                if actual_size > 500 * 1024:  # 大于500KB
                    print(f"  ⚠ 大文件警告: 文件大小超过500KB，可能需要较长加载时间")
                    print(f"    建议:")
                    print(f"    - 耐心等待加载完成")
                    print(f"    - 检查网络连接")
                    print(f"    - 如果加载失败，点击重试按钮")
                else:
                    print(f"  ✓ 文件大小正常，应该能快速加载")
            else:
                print(f"  ✗ 文件不存在: {model_path}")
        else:
            print("  无3D模型")
    
    print(f"\n前端优化建议:")
    print("1. 已添加加载进度显示")
    print("2. 已添加详细的加载状态提示")
    print("3. 已添加错误处理和重试功能")
    print("4. 已优化大文件加载性能")
    
    print(f"\n使用说明:")
    print("1. 打开3D模型查看器")
    print("2. 观察加载进度条")
    print("3. 如果加载失败，点击重试按钮")
    print("4. 大文件可能需要等待1-2分钟")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    test_large_stl_file()
