#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试3D模型查看器功能

验证STL格式3D模型的显示功能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

from django.conf import settings
from apps.materials.models import Material

def test_3d_viewer():
    """测试3D模型查看器功能"""
    print("=" * 60)
    print("测试3D模型查看器功能")
    print("=" * 60)
    
    # 查找测试物料
    test_material = Material.objects.filter(code='STL001').first()
    if not test_material:
        print("没有找到测试物料 STL001")
        return
    
    print(f"找到测试物料: {test_material.code} - {test_material.name}")
    
    # 获取文件信息
    file_info = test_material.get_file_info()
    
    if file_info.get('has_3d_model'):
        print(f"\n3D模型信息:")
        print(f"  文件名: {file_info.get('3d_model_name')}")
        print(f"  文件URL: {file_info.get('3d_model_url')}")
        print(f"  文件大小: {file_info.get('3d_model_size')} bytes")
        
        # 检查文件是否存在
        model_path = os.path.join(settings.MEDIA_ROOT, str(test_material.model_3d))
        if os.path.exists(model_path):
            print(f"✓ 3D模型文件存在: {model_path}")
            
            # 检查文件类型
            filename = file_info.get('3d_model_name', '').lower()
            if filename.endswith('.stl'):
                print("✓ 文件类型: STL (Three.js支持)")
                print("  支持的查看功能:")
                print("    - 3D模型渲染")
                print("    - 鼠标旋转和缩放")
                print("    - 线框模式切换")
                print("    - 网格显示切换")
                print("    - 坐标轴显示切换")
                print("    - 重置视图")
            elif filename.endswith('.obj'):
                print("✓ 文件类型: OBJ (Three.js支持)")
            else:
                print("⚠ 文件类型: 未知格式")
        else:
            print(f"✗ 3D模型文件不存在: {model_path}")
    else:
        print("  无3D模型")
    
    print(f"\n前端功能验证:")
    print("1. 在物料管理页面找到测试物料 STL001")
    print("2. 点击'查看'按钮进入详情页面")
    print("3. 在'模型文件'区域点击'查看3D模型'")
    print("4. 测试以下功能:")
    print("   - 3D模型是否正确显示")
    print("   - 鼠标旋转和缩放是否正常")
    print("   - 线框模式切换")
    print("   - 网格显示切换")
    print("   - 坐标轴显示切换")
    print("   - 重置视图功能")
    print("   - 下载模型功能")
    
    print("\n调试信息:")
    print("1. 打开浏览器开发者工具")
    print("2. 查看控制台是否有错误信息")
    print("3. 检查网络请求是否成功")
    print("4. 查看Three.js的调试日志")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == '__main__':
    test_3d_viewer()
