#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D模型性能分析

分析STL文件的复杂度和渲染性能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

from django.conf import settings
from apps.materials.models import Material

def analyze_stl_complexity(stl_file_path):
    """分析STL文件的复杂度"""
    try:
        file_size = os.path.getsize(stl_file_path)
        
        # 尝试读取文件头部判断格式
        with open(stl_file_path, 'rb') as f:
            header = f.read(80)
            
        # 检查是否为二进制STL
        if b'solid' in header[:5]:
            # 文本格式STL
            with open(stl_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            facet_count = content.count('facet normal')
            vertex_count = content.count('vertex')
        else:
            # 二进制格式STL
            # 二进制STL: 80字节头部 + 每个面50字节
            # 每个面包含: 法向量(12字节) + 3个顶点(36字节) + 属性(2字节)
            facet_count = (file_size - 80) // 50
            vertex_count = facet_count * 3
        
        return {
            'facet_count': facet_count,
            'vertex_count': vertex_count,
            'file_size': file_size,
            'file_size_mb': file_size / 1024 / 1024,
            'format': 'binary' if b'solid' not in header[:5] else 'text',
            'complexity': 'high' if facet_count > 10000 else 'medium' if facet_count > 1000 else 'low'
        }
    except Exception as e:
        print(f"分析STL文件时出错: {e}")
        return None

def analyze_3d_performance():
    """分析3D模型性能"""
    print("=" * 60)
    print("3D模型性能分析")
    print("=" * 60)
    
    # 查找包含3D模型的物料
    materials_with_3d = Material.objects.filter(
        model_3d__isnull=False
    ).exclude(model_3d='')
    
    print(f"找到 {materials_with_3d.count()} 个包含3D模型的物料")
    
    for material in materials_with_3d:
        print(f"\n物料: {material.code} - {material.name}")
        
        # 获取文件信息
        file_info = material.get_file_info()
        
        if file_info.get('has_3d_model'):
            print(f"  3D模型文件: {file_info.get('3d_model_name')}")
            print(f"  文件大小: {file_info.get('3d_model_size')} bytes ({file_info.get('3d_model_size') / 1024 / 1024:.2f} MB)")
            
            # 检查文件是否存在
            model_path = os.path.join(settings.MEDIA_ROOT, str(material.model_3d))
            if os.path.exists(model_path):
                # 分析STL复杂度
                complexity_info = analyze_stl_complexity(model_path)
                if complexity_info:
                    print(f"  STL文件复杂度分析:")
                    print(f"    格式: {complexity_info['format']}")
                    print(f"    面数: {complexity_info['facet_count']:,}")
                    print(f"    顶点数: {complexity_info['vertex_count']:,}")
                    print(f"    复杂度等级: {complexity_info['complexity']}")
                    
                    # 性能评估
                    if complexity_info['facet_count'] > 50000:
                        print(f"    ⚠ 高复杂度警告: 面数超过50,000，渲染会很慢")
                        print(f"      建议: 简化模型或使用LOD技术")
                    elif complexity_info['facet_count'] > 10000:
                        print(f"    ⚠ 中等复杂度: 面数超过10,000，渲染较慢")
                        print(f"      建议: 考虑优化模型")
                    else:
                        print(f"    ✓ 低复杂度: 渲染应该较快")
                    
                    # 预估渲染时间
                    estimated_time = complexity_info['facet_count'] / 1000  # 每1000个面约1秒
                    print(f"    预估渲染时间: {estimated_time:.1f} 秒")
                else:
                    print(f"    ✗ 无法分析STL文件")
            else:
                print(f"  ✗ 文件不存在: {model_path}")
        else:
            print("  无3D模型")
    
    print(f"\n性能优化建议:")
    print("1. **模型简化**: 减少面数和顶点数")
    print("2. **LOD技术**: 根据距离显示不同精度的模型")
    print("3. **渐进式加载**: 先显示低精度，再加载高精度")
    print("4. **后台处理**: 在后台预处理几何体")
    print("5. **缓存机制**: 缓存已处理的几何体")
    
    print(f"\n前端优化建议:")
    print("1. **Web Workers**: 在后台线程处理几何体")
    print("2. **分块加载**: 将大模型分块加载")
    print("3. **几何体优化**: 使用BufferGeometry优化")
    print("4. **材质优化**: 使用更高效的材质")
    print("5. **渲染优化**: 减少不必要的渲染调用")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    analyze_3d_performance()
