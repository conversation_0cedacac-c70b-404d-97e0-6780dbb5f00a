#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证3D模型显示状态

检查3D模型是否正确加载和显示
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

from django.conf import settings
from apps.materials.models import Material

def verify_3d_model_display():
    """验证3D模型显示状态"""
    print("=" * 60)
    print("验证3D模型显示状态")
    print("=" * 60)
    
    # 查找包含3D模型的物料
    materials_with_3d = Material.objects.filter(
        model_3d__isnull=False
    ).exclude(model_3d='')
    
    print(f"找到 {materials_with_3d.count()} 个包含3D模型的物料")
    
    for material in materials_with_3d:
        print(f"\n物料: {material.code} - {material.name}")
        
        # 获取文件信息
        file_info = material.get_file_info()
        
        if file_info.get('has_3d_model'):
            print(f"  3D模型文件: {file_info.get('3d_model_name')}")
            print(f"  文件大小: {file_info.get('3d_model_size')} bytes")
            print(f"  文件大小(MB): {file_info.get('3d_model_size') / 1024 / 1024:.2f} MB")
            print(f"  文件URL: {file_info.get('3d_model_url')}")
            
            # 检查文件是否存在
            model_path = os.path.join(settings.MEDIA_ROOT, str(material.model_3d))
            if os.path.exists(model_path):
                actual_size = os.path.getsize(model_path)
                print(f"  ✓ 文件存在，实际大小: {actual_size} bytes ({actual_size / 1024 / 1024:.2f} MB)")
                
                # 检查文件格式
                filename = file_info.get('3d_model_name', '').lower()
                if filename.endswith('.stl'):
                    print(f"  ✓ 文件格式: STL (Three.js支持)")
                    print(f"  ✓ 应该能正常显示")
                elif filename.endswith('.obj'):
                    print(f"  ✓ 文件格式: OBJ (Three.js支持)")
                    print(f"  ✓ 应该能正常显示")
                else:
                    print(f"  ⚠ 文件格式: {filename} (可能不支持)")
            else:
                print(f"  ✗ 文件不存在: {model_path}")
        else:
            print("  无3D模型")
    
    print(f"\n前端显示状态分析:")
    print("从日志可以看出:")
    print("1. ✓ STL文件加载成功 (100% 进度)")
    print("2. ✓ 几何体创建成功")
    print("3. ✓ 材质和网格创建成功")
    print("4. ✓ 模型居中处理完成")
    print("5. ✓ 相机位置调整完成")
    print("6. ✓ 模型已添加到场景")
    
    print(f"\n可能的问题:")
    print("1. 模型可能太小或太大，需要调整相机位置")
    print("2. 模型可能被其他对象遮挡")
    print("3. 渲染器可能没有正确渲染")
    print("4. 场景背景色可能与模型颜色相近")
    
    print(f"\n建议的解决方案:")
    print("1. 检查模型是否在视野范围内")
    print("2. 尝试使用鼠标滚轮缩放")
    print("3. 尝试拖拽鼠标旋转视角")
    print("4. 点击'重置视图'按钮")
    print("5. 检查浏览器控制台是否有其他错误")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    verify_3d_model_display()
