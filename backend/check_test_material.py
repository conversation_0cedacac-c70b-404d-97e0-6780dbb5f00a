#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查测试物料
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

from apps.materials.models import Material

def check_test_material():
    """检查测试物料"""
    print("=" * 60)
    print("检查测试物料")
    print("=" * 60)
    
    # 查找测试物料
    test_material = Material.objects.filter(code='TEST001').first()
    if not test_material:
        print("没有找到测试物料 TEST001")
        return
    
    print(f"找到测试物料: {test_material.code} - {test_material.name}")
    
    # 获取文件信息
    file_info = test_material.get_file_info()
    print(f"\n文件信息: {file_info}")
    
    if file_info.get('has_3d_model'):
        print(f"\n3D模型信息:")
        print(f"  文件名: {file_info.get('3d_model_name')}")
        print(f"  文件URL: {file_info.get('3d_model_url')}")
        print(f"  文件大小: {file_info.get('3d_model_size')} bytes")
        
        # 检查文件类型
        filename = file_info.get('3d_model_name', '').lower()
        if filename.endswith('.stl'):
            print("✓ 文件类型: STL (Three.js支持)")
        elif filename.endswith('.obj'):
            print("✓ 文件类型: OBJ (Three.js支持)")
        elif filename.endswith(('.step', '.stp')):
            print("⚠ 文件类型: STEP (Three.js不支持，需要转换)")
        elif filename.endswith(('.iges', '.igs')):
            print("⚠ 文件类型: IGES (Three.js不支持，需要转换)")
        else:
            print(f"⚠ 文件类型: {filename} (未知格式)")
    else:
        print("  无3D模型")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    check_test_material()
