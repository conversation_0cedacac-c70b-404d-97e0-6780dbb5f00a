#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D模型优化工具

提供STL模型的预处理、压缩和优化功能
"""

import os
import sys
import numpy as np
import json
import gzip
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    # 尝试导入trimesh用于3D处理
    import trimesh
    TRIMESH_AVAILABLE = True
except ImportError:
    TRIMESH_AVAILABLE = False
    logger.warning("trimesh未安装，3D模型优化功能将受限")

try:
    # 尝试导入PIL用于纹理处理
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logger.warning("PIL未安装，纹理优化功能将受限")


class Model3DOptimizer:
    """3D模型优化器"""
    
    def __init__(self):
        self.optimization_cache = {}
        
    def analyze_model_complexity(self, file_path: str) -> Dict[str, Any]:
        """分析模型复杂度"""
        try:
            file_size = os.path.getsize(file_path)
            file_ext = Path(file_path).suffix.lower()
            
            analysis = {
                'file_path': file_path,
                'file_size': file_size,
                'file_size_mb': round(file_size / 1024 / 1024, 2),
                'file_type': file_ext,
                'vertices': 0,
                'faces': 0,
                'complexity_level': 'unknown',
                'optimization_suggestions': [],
                'estimated_render_time': 0
            }
            
            if file_ext == '.stl':
                analysis.update(self._analyze_stl_file(file_path))
            elif TRIMESH_AVAILABLE and file_ext in ['.obj', '.ply', '.glb', '.gltf']:
                analysis.update(self._analyze_trimesh_file(file_path))
            
            # 计算复杂度等级和优化建议
            analysis.update(self._calculate_complexity_level(analysis))
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析模型复杂度时出错: {e}")
            return {'error': str(e)}
    
    def _analyze_stl_file(self, file_path: str) -> Dict[str, Any]:
        """分析STL文件"""
        analysis = {}
        
        try:
            with open(file_path, 'rb') as f:
                header = f.read(80)
                
            # 判断STL格式
            if header[:5] == b'solid':
                # ASCII STL
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                facet_count = content.count('facet normal')
                vertex_count = content.count('vertex')
                analysis['format'] = 'ascii'
                
            else:
                # Binary STL
                with open(file_path, 'rb') as f:
                    f.seek(80)  # 跳过头部
                    triangle_count_bytes = f.read(4)
                    if len(triangle_count_bytes) == 4:
                        facet_count = int.from_bytes(triangle_count_bytes, byteorder='little')
                        vertex_count = facet_count * 3
                    else:
                        facet_count = 0
                        vertex_count = 0
                
                analysis['format'] = 'binary'
            
            analysis.update({
                'vertices': vertex_count,
                'faces': facet_count,
                'triangles': facet_count
            })
            
        except Exception as e:
            logger.error(f"分析STL文件时出错: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def _analyze_trimesh_file(self, file_path: str) -> Dict[str, Any]:
        """使用trimesh分析3D文件"""
        analysis = {}
        
        try:
            mesh = trimesh.load(file_path)
            
            if hasattr(mesh, 'vertices'):
                analysis.update({
                    'vertices': len(mesh.vertices),
                    'faces': len(mesh.faces) if hasattr(mesh, 'faces') else 0,
                    'triangles': len(mesh.faces) if hasattr(mesh, 'faces') else 0,
                    'is_watertight': mesh.is_watertight if hasattr(mesh, 'is_watertight') else False,
                    'volume': float(mesh.volume) if hasattr(mesh, 'volume') else 0,
                    'surface_area': float(mesh.area) if hasattr(mesh, 'area') else 0
                })
                
                # 计算边界框
                if hasattr(mesh, 'bounds'):
                    bounds = mesh.bounds
                    size = bounds[1] - bounds[0]
                    analysis['bounding_box'] = {
                        'min': bounds[0].tolist(),
                        'max': bounds[1].tolist(),
                        'size': size.tolist(),
                        'max_dimension': float(np.max(size))
                    }
            
        except Exception as e:
            logger.error(f"使用trimesh分析文件时出错: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def _calculate_complexity_level(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """计算复杂度等级和优化建议"""
        result = {}
        
        vertices = analysis.get('vertices', 0)
        faces = analysis.get('faces', 0)
        file_size_mb = analysis.get('file_size_mb', 0)
        
        # 复杂度等级判断
        if vertices > 500000 or faces > 250000 or file_size_mb > 50:
            complexity = 'very_high'
            render_time = faces / 1000 * 2  # 估算渲染时间
        elif vertices > 100000 or faces > 50000 or file_size_mb > 10:
            complexity = 'high'
            render_time = faces / 1000 * 1.5
        elif vertices > 20000 or faces > 10000 or file_size_mb > 2:
            complexity = 'medium'
            render_time = faces / 1000 * 1
        else:
            complexity = 'low'
            render_time = faces / 1000 * 0.5
        
        result['complexity_level'] = complexity
        result['estimated_render_time'] = round(render_time, 2)
        
        # 优化建议
        suggestions = []
        
        if complexity in ['high', 'very_high']:
            suggestions.extend([
                '模型复杂度较高，建议进行几何体简化',
                '考虑生成多层细节(LOD)模型',
                '使用Draco压缩减少文件大小',
                '在移动设备上使用低精度版本'
            ])
        
        if file_size_mb > 5:
            suggestions.append('文件较大，建议压缩或转换为更高效的格式(如glTF)')
        
        if vertices > 100000:
            suggestions.append('顶点数过多，建议使用几何体简化算法')
        
        if analysis.get('format') == 'ascii':
            suggestions.append('ASCII格式效率较低，建议转换为二进制格式')
        
        result['optimization_suggestions'] = suggestions
        
        return result
    
    def optimize_stl_model(self, input_path: str, output_path: str, 
                          target_complexity: str = 'medium') -> Dict[str, Any]:
        """优化STL模型"""
        
        if not TRIMESH_AVAILABLE:
            return {'error': 'trimesh库未安装，无法进行模型优化'}
        
        try:
            logger.info(f"开始优化STL模型: {input_path}")
            
            # 加载模型
            mesh = trimesh.load(input_path)
            original_analysis = self.analyze_model_complexity(input_path)
            
            optimization_log = []
            
            # 根据目标复杂度设置简化参数
            simplification_ratios = {
                'low': 0.3,      # 保留30%的面
                'medium': 0.5,   # 保留50%的面
                'high': 0.7      # 保留70%的面
            }
            
            target_ratio = simplification_ratios.get(target_complexity, 0.5)
            
            # 1. 修复模型（如果需要）
            if hasattr(mesh, 'fill_holes'):
                try:
                    mesh.fill_holes()
                    optimization_log.append("已修复模型中的孔洞")
                except:
                    pass
            
            # 2. 移除重复顶点
            if hasattr(mesh, 'merge_vertices'):
                original_vertices = len(mesh.vertices)
                mesh.merge_vertices()
                new_vertices = len(mesh.vertices)
                if new_vertices < original_vertices:
                    optimization_log.append(f"合并重复顶点: {original_vertices} -> {new_vertices}")
            
            # 3. 几何体简化
            if hasattr(mesh, 'simplify_quadric_decimation'):
                target_faces = int(len(mesh.faces) * target_ratio)
                if target_faces < len(mesh.faces):
                    original_faces = len(mesh.faces)
                    mesh = mesh.simplify_quadric_decimation(target_faces)
                    optimization_log.append(f"几何体简化: {original_faces} -> {len(mesh.faces)} 面")
            
            # 4. 平滑处理（可选）
            if hasattr(mesh, 'smoothed') and target_complexity != 'low':
                try:
                    mesh = mesh.smoothed()
                    optimization_log.append("应用了平滑处理")
                except:
                    pass
            
            # 5. 保存优化后的模型
            mesh.export(output_path)
            optimized_analysis = self.analyze_model_complexity(output_path)
            
            # 计算优化效果
            original_size = original_analysis.get('file_size_mb', 0)
            optimized_size = optimized_analysis.get('file_size_mb', 0)
            size_reduction = ((original_size - optimized_size) / original_size * 100) if original_size > 0 else 0
            
            original_faces = original_analysis.get('faces', 0)
            optimized_faces = optimized_analysis.get('faces', 0)
            face_reduction = ((original_faces - optimized_faces) / original_faces * 100) if original_faces > 0 else 0
            
            result = {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'original_analysis': original_analysis,
                'optimized_analysis': optimized_analysis,
                'optimization_log': optimization_log,
                'improvements': {
                    'file_size_reduction_percent': round(size_reduction, 2),
                    'face_reduction_percent': round(face_reduction, 2),
                    'original_size_mb': original_size,
                    'optimized_size_mb': optimized_size,
                    'original_faces': original_faces,
                    'optimized_faces': optimized_faces
                }
            }
            
            logger.info(f"模型优化完成，文件大小减少: {size_reduction:.1f}%，面数减少: {face_reduction:.1f}%")
            
            return result
            
        except Exception as e:
            logger.error(f"优化模型时出错: {e}")
            return {'error': str(e)}
    
    def generate_lod_models(self, input_path: str, output_dir: str) -> Dict[str, Any]:
        """生成多层细节(LOD)模型"""
        
        if not TRIMESH_AVAILABLE:
            return {'error': 'trimesh库未安装，无法生成LOD模型'}
        
        try:
            logger.info(f"开始生成LOD模型: {input_path}")
            
            # 确保输出目录存在
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # 加载原始模型
            mesh = trimesh.load(input_path)
            base_name = Path(input_path).stem
            
            # LOD等级配置
            lod_levels = {
                'lod0': {'ratio': 1.0, 'description': '原始质量'},
                'lod1': {'ratio': 0.7, 'description': '高质量'},
                'lod2': {'ratio': 0.4, 'description': '中等质量'},
                'lod3': {'ratio': 0.2, 'description': '低质量'},
                'lod4': {'ratio': 0.1, 'description': '最低质量'}
            }
            
            lod_files = {}
            
            for lod_name, config in lod_levels.items():
                try:
                    if config['ratio'] == 1.0:
                        # LOD0使用原始模型
                        current_mesh = mesh.copy()
                    else:
                        # 简化模型
                        target_faces = int(len(mesh.faces) * config['ratio'])
                        current_mesh = mesh.simplify_quadric_decimation(target_faces)
                    
                    # 生成文件名
                    output_file = os.path.join(output_dir, f"{base_name}_{lod_name}.stl")
                    
                    # 保存模型
                    current_mesh.export(output_file)
                    
                    # 分析生成的模型
                    analysis = self.analyze_model_complexity(output_file)
                    
                    lod_files[lod_name] = {
                        'file_path': output_file,
                        'ratio': config['ratio'],
                        'description': config['description'],
                        'analysis': analysis
                    }
                    
                    logger.info(f"生成 {lod_name}: {analysis.get('faces', 0)} 面, {analysis.get('file_size_mb', 0):.2f} MB")
                    
                except Exception as e:
                    logger.error(f"生成 {lod_name} 时出错: {e}")
                    continue
            
            # 生成LOD配置文件
            lod_config = {
                'base_model': input_path,
                'lod_levels': lod_files,
                'generated_at': str(Path().absolute()),
                'usage_guide': {
                    'lod0': '距离 < 10 单位时使用',
                    'lod1': '距离 10-25 单位时使用',
                    'lod2': '距离 25-50 单位时使用',
                    'lod3': '距离 50-100 单位时使用',
                    'lod4': '距离 > 100 单位时使用'
                }
            }
            
            config_file = os.path.join(output_dir, f"{base_name}_lod_config.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(lod_config, f, indent=2, ensure_ascii=False)
            
            result = {
                'success': True,
                'input_path': input_path,
                'output_dir': output_dir,
                'lod_files': lod_files,
                'config_file': config_file,
                'total_files': len(lod_files)
            }
            
            logger.info(f"LOD模型生成完成，共生成 {len(lod_files)} 个文件")
            
            return result
            
        except Exception as e:
            logger.error(f"生成LOD模型时出错: {e}")
            return {'error': str(e)}
    
    def compress_model(self, input_path: str, output_path: str) -> Dict[str, Any]:
        """压缩模型文件"""
        try:
            logger.info(f"开始压缩模型: {input_path}")
            
            # 读取原始文件
            with open(input_path, 'rb') as f:
                original_data = f.read()
            
            # Gzip压缩
            with gzip.open(output_path, 'wb') as f:
                f.write(original_data)
            
            # 计算压缩效果
            original_size = len(original_data)
            compressed_size = os.path.getsize(output_path)
            compression_ratio = (1 - compressed_size / original_size) * 100
            
            result = {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'original_size': original_size,
                'compressed_size': compressed_size,
                'compression_ratio': round(compression_ratio, 2),
                'size_reduction_mb': round((original_size - compressed_size) / 1024 / 1024, 2)
            }
            
            logger.info(f"压缩完成，压缩率: {compression_ratio:.1f}%")
            
            return result
            
        except Exception as e:
            logger.error(f"压缩模型时出错: {e}")
            return {'error': str(e)}
    
    def convert_to_gltf(self, input_path: str, output_path: str) -> Dict[str, Any]:
        """转换为glTF格式"""
        
        if not TRIMESH_AVAILABLE:
            return {'error': 'trimesh库未安装，无法进行格式转换'}
        
        try:
            logger.info(f"开始转换为glTF格式: {input_path}")
            
            # 加载模型
            mesh = trimesh.load(input_path)
            
            # 导出为glTF
            export_data = mesh.export(file_type='gltf')
            
            # 保存文件
            if isinstance(export_data, dict):
                # glTF场景，保存所有文件
                for filename, data in export_data.items():
                    file_path = os.path.join(os.path.dirname(output_path), filename)
                    if isinstance(data, str):
                        with open(file_path, 'w') as f:
                            f.write(data)
                    else:
                        with open(file_path, 'wb') as f:
                            f.write(data)
            else:
                # 单个文件
                with open(output_path, 'wb') as f:
                    f.write(export_data)
            
            # 分析转换结果
            original_analysis = self.analyze_model_complexity(input_path)
            converted_analysis = self.analyze_model_complexity(output_path)
            
            result = {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'original_analysis': original_analysis,
                'converted_analysis': converted_analysis,
                'format_change': f"{Path(input_path).suffix} -> .gltf"
            }
            
            logger.info("glTF转换完成")
            
            return result
            
        except Exception as e:
            logger.error(f"转换为glTF时出错: {e}")
            return {'error': str(e)}


def main():
    """命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='3D模型优化工具')
    parser.add_argument('command', choices=['analyze', 'optimize', 'lod', 'compress', 'convert'], 
                       help='要执行的操作')
    parser.add_argument('input', help='输入文件路径')
    parser.add_argument('-o', '--output', help='输出文件或目录路径')
    parser.add_argument('-q', '--quality', choices=['low', 'medium', 'high'], 
                       default='medium', help='目标质量级别')
    
    args = parser.parse_args()
    
    optimizer = Model3DOptimizer()
    
    if args.command == 'analyze':
        result = optimizer.analyze_model_complexity(args.input)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    elif args.command == 'optimize':
        if not args.output:
            args.output = args.input.replace('.stl', '_optimized.stl')
        result = optimizer.optimize_stl_model(args.input, args.output, args.quality)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    elif args.command == 'lod':
        if not args.output:
            args.output = os.path.dirname(args.input)
        result = optimizer.generate_lod_models(args.input, args.output)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    elif args.command == 'compress':
        if not args.output:
            args.output = args.input + '.gz'
        result = optimizer.compress_model(args.input, args.output)
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    elif args.command == 'convert':
        if not args.output:
            args.output = args.input.replace(Path(args.input).suffix, '.gltf')
        result = optimizer.convert_to_gltf(args.input, args.output)
        print(json.dumps(result, indent=2, ensure_ascii=False))


if __name__ == '__main__':
    main()
