#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件上传修复

验证STEP格式等文件的上传功能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

def test_file_extensions():
    """测试支持的文件扩展名"""
    print("=" * 60)
    print("测试支持的文件扩展名")
    print("=" * 60)
    
    # 前端支持的文件扩展名
    frontend_3d_extensions = ['.stl', '.obj', '.step', '.stp', '.iges', '.igs', '.3ds', '.dae']
    frontend_2d_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.dwg', '.dxf']
    frontend_thumbnail_extensions = ['.jpg', '.jpeg', '.png', '.gif']
    
    # 后端支持的文件扩展名
    backend_3d_extensions = ['.stl', '.obj', '.step', '.stp', '.iges', '.igs', '.3ds', '.dae']
    backend_2d_extensions = ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.dwg', '.dxf']
    backend_thumbnail_extensions = ['.jpg', '.jpeg', '.png', '.gif']
    
    print("3D模型文件扩展名:")
    print(f"  前端支持: {', '.join(frontend_3d_extensions)}")
    print(f"  后端支持: {', '.join(backend_3d_extensions)}")
    print(f"  一致性: {'✓' if frontend_3d_extensions == backend_3d_extensions else '✗'}")
    
    print("\n2D图纸文件扩展名:")
    print(f"  前端支持: {', '.join(frontend_2d_extensions)}")
    print(f"  后端支持: {', '.join(backend_2d_extensions)}")
    print(f"  一致性: {'✓' if frontend_2d_extensions == backend_2d_extensions else '✗'}")
    
    print("\n缩略图文件扩展名:")
    print(f"  前端支持: {', '.join(frontend_thumbnail_extensions)}")
    print(f"  后端支持: {', '.join(backend_thumbnail_extensions)}")
    print(f"  一致性: {'✓' if frontend_thumbnail_extensions == backend_thumbnail_extensions else '✗'}")
    
    # 测试STEP文件
    test_step_files = ['test.step', 'model.stp', 'part.iges', 'assembly.igs']
    print(f"\nSTEP文件测试:")
    for filename in test_step_files:
        ext = os.path.splitext(filename)[1].lower()
        is_supported = ext in frontend_3d_extensions
        print(f"  {filename}: {'✓' if is_supported else '✗'} ({ext})")
    
    print("\n修复说明:")
    print("1. 前端验证改为基于文件扩展名而不是MIME类型")
    print("2. 后端验证改为基于文件扩展名而不是MIME类型")
    print("3. 添加了更多支持的扩展名格式")
    print("4. 统一了前后端的文件类型验证逻辑")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == '__main__':
    test_file_extensions()
