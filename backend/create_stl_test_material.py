#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建带有STL格式3D模型的测试物料

用于测试3D模型查看器功能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

from django.conf import settings
from apps.materials.models import Material, MaterialCategory
from apps.materials.serializers import MaterialCreateSerializer

def create_stl_test_material():
    """创建带有STL格式3D模型的测试物料"""
    print("=" * 60)
    print("创建带有STL格式3D模型的测试物料")
    print("=" * 60)
    
    # 检查是否存在分类
    categories = MaterialCategory.objects.filter(is_active=True)
    if not categories.exists():
        print("没有找到可用的分类，请先创建分类")
        return
    
    category = categories.first()
    print(f"使用分类: {category.code} - {category.name}")
    
    # 创建测试物料数据
    test_material_data = {
        'code': 'STL001',
        'name': '测试物料-STL模型',
        'category_code': category.code,
        'specification': '这是一个用于测试3D模型查看器功能的测试物料，包含STL格式的3D模型',
        'unit': '个',
        'status': 'ACTIVE',
        'is_virtual': False,
        'is_preferred': True,
        'created_by': 'admin'
    }
    
    # 检查物料是否已存在
    existing_material = Material.objects.filter(code=test_material_data['code']).first()
    if existing_material:
        print(f"物料 {test_material_data['code']} 已存在，将更新文件信息")
        material = existing_material
    else:
        print("创建新物料...")
        serializer = MaterialCreateSerializer(data=test_material_data)
        if serializer.is_valid():
            material = serializer.save()
            print(f"物料创建成功: {material.code} - {material.name}")
        else:
            print(f"物料创建失败: {serializer.errors}")
            return
    
    # 创建测试文件
    media_root = settings.MEDIA_ROOT
    materials_dir = os.path.join(media_root, 'materials')
    
    # 确保目录存在
    os.makedirs(materials_dir, exist_ok=True)
    
    # 创建3D模型目录
    models_3d_dir = os.path.join(materials_dir, '3d', material.code)
    os.makedirs(models_3d_dir, exist_ok=True)
    
    # 创建2D图纸目录
    drawings_2d_dir = os.path.join(materials_dir, '2d', material.code)
    os.makedirs(drawings_2d_dir, exist_ok=True)
    
    # 创建测试文件
    test_files_created = []
    
    # 创建测试STL文件（立方体）
    stl_content = """solid cube
  facet normal 0.0 0.0 1.0
    outer loop
      vertex 0.0 0.0 1.0
      vertex 1.0 0.0 1.0
      vertex 1.0 1.0 1.0
    endloop
  endfacet
  facet normal 0.0 0.0 1.0
    outer loop
      vertex 0.0 0.0 1.0
      vertex 1.0 1.0 1.0
      vertex 0.0 1.0 1.0
    endloop
  endfacet
  facet normal 0.0 0.0 -1.0
    outer loop
      vertex 0.0 0.0 0.0
      vertex 0.0 1.0 0.0
      vertex 1.0 1.0 0.0
    endloop
  endfacet
  facet normal 0.0 0.0 -1.0
    outer loop
      vertex 0.0 0.0 0.0
      vertex 1.0 1.0 0.0
      vertex 1.0 0.0 0.0
    endloop
  endfacet
  facet normal 0.0 1.0 0.0
    outer loop
      vertex 0.0 1.0 0.0
      vertex 0.0 1.0 1.0
      vertex 1.0 1.0 1.0
    endloop
  endfacet
  facet normal 0.0 1.0 0.0
    outer loop
      vertex 0.0 1.0 0.0
      vertex 1.0 1.0 1.0
      vertex 1.0 1.0 0.0
    endloop
  endfacet
  facet normal 0.0 -1.0 0.0
    outer loop
      vertex 0.0 0.0 0.0
      vertex 1.0 0.0 0.0
      vertex 1.0 0.0 1.0
    endloop
  endfacet
  facet normal 0.0 -1.0 0.0
    outer loop
      vertex 0.0 0.0 0.0
      vertex 1.0 0.0 1.0
      vertex 0.0 0.0 1.0
    endloop
  endfacet
  facet normal 1.0 0.0 0.0
    outer loop
      vertex 1.0 0.0 0.0
      vertex 1.0 1.0 0.0
      vertex 1.0 1.0 1.0
    endloop
  endfacet
  facet normal 1.0 0.0 0.0
    outer loop
      vertex 1.0 0.0 0.0
      vertex 1.0 1.0 1.0
      vertex 1.0 0.0 1.0
    endloop
  endfacet
  facet normal -1.0 0.0 0.0
    outer loop
      vertex 0.0 0.0 0.0
      vertex 0.0 0.0 1.0
      vertex 0.0 1.0 1.0
    endloop
  endfacet
  facet normal -1.0 0.0 0.0
    outer loop
      vertex 0.0 0.0 0.0
      vertex 0.0 1.0 1.0
      vertex 0.0 1.0 0.0
    endloop
  endfacet
endsolid cube"""
    
    stl_file_path = os.path.join(models_3d_dir, 'test_cube.stl')
    with open(stl_file_path, 'w') as f:
        f.write(stl_content)
    test_files_created.append(stl_file_path)
    print(f"✓ 创建STL模型文件: {stl_file_path}")
    
    # 创建测试2D图纸文件（简单的PNG文件内容）
    png_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc```\x00\x00\x00\x04\x00\x01\xf5\xf7\xa0\xd8\x00\x00\x00\x00IEND\xaeB`\x82'
    
    drawing_file_path = os.path.join(drawings_2d_dir, 'test_drawing.png')
    with open(drawing_file_path, 'wb') as f:
        f.write(png_content)
    test_files_created.append(drawing_file_path)
    print(f"✓ 创建2D图纸文件: {drawing_file_path}")
    
    # 创建测试缩略图文件
    thumbnail_file_path = os.path.join(drawings_2d_dir, 'test_thumbnail.png')
    with open(thumbnail_file_path, 'wb') as f:
        f.write(png_content)
    test_files_created.append(thumbnail_file_path)
    print(f"✓ 创建缩略图文件: {thumbnail_file_path}")
    
    # 更新物料记录
    material.model_3d = f'materials/3d/{material.code}/test_cube.stl'
    material.model_3d_name = 'test_cube.stl'
    material.drawing_2d = f'materials/2d/{material.code}/test_drawing.png'
    material.drawing_2d_name = 'test_drawing.png'
    material.thumbnail = f'materials/2d/{material.code}/test_thumbnail.png'
    material.save()
    
    print(f"\n✓ 物料文件信息更新完成")
    
    # 验证文件信息
    file_info = material.get_file_info()
    print(f"\n文件信息验证:")
    print(f"  有3D模型: {file_info.get('has_3d_model')}")
    print(f"  有2D图纸: {file_info.get('has_2d_drawing')}")
    print(f"  有缩略图: {file_info.get('has_thumbnail')}")
    print(f"  3D模型URL: {file_info.get('3d_model_url')}")
    print(f"  2D图纸URL: {file_info.get('2d_drawing_url')}")
    print(f"  缩略图URL: {file_info.get('thumbnail_url')}")
    
    print(f"\n✓ 测试物料创建完成: {material.code} - {material.name}")
    print(f"  物料ID: {material.id}")
    print(f"  创建的文件数量: {len(test_files_created)}")
    print(f"  3D模型格式: STL (Three.js支持)")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    create_stl_test_material()
