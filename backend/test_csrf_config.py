#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSRF配置

验证CSRF设置是否正确
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'plm_core.settings')
django.setup()

from django.conf import settings

def test_csrf_config():
    """测试CSRF配置"""
    print("=" * 60)
    print("测试CSRF配置")
    print("=" * 60)
    
    print("CSRF配置检查:")
    print(f"  CSRF_TRUSTED_ORIGINS: {getattr(settings, 'CSRF_TRUSTED_ORIGINS', '未设置')}")
    print(f"  CSRF_COOKIE_SECURE: {getattr(settings, 'CSRF_COOKIE_SECURE', '未设置')}")
    print(f"  CSRF_COOKIE_HTTPONLY: {getattr(settings, 'CSRF_COOKIE_HTTPONLY', '未设置')}")
    print(f"  CSRF_USE_SESSIONS: {getattr(settings, 'CSRF_USE_SESSIONS', '未设置')}")
    
    print("\nCORS配置检查:")
    print(f"  CORS_ALLOW_ALL_ORIGINS: {getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', '未设置')}")
    print(f"  CORS_ALLOWED_ORIGINS: {getattr(settings, 'CORS_ALLOWED_ORIGINS', '未设置')}")
    print(f"  CORS_ALLOW_CREDENTIALS: {getattr(settings, 'CORS_ALLOW_CREDENTIALS', '未设置')}")
    
    print("\n中间件检查:")
    middleware = getattr(settings, 'MIDDLEWARE', [])
    csrf_middleware = 'django.middleware.csrf.CsrfViewMiddleware'
    cors_middleware = 'corsheaders.middleware.CorsMiddleware'
    
    print(f"  CSRF中间件: {'✓' if csrf_middleware in middleware else '✗'}")
    print(f"  CORS中间件: {'✓' if cors_middleware in middleware else '✗'}")
    
    print("\n支持的域名:")
    trusted_origins = getattr(settings, 'CSRF_TRUSTED_ORIGINS', [])
    for origin in trusted_origins:
        print(f"  ✓ {origin}")
    
    print("\n修复说明:")
    print("1. 添加了CSRF_TRUSTED_ORIGINS配置")
    print("2. 为文件上传API添加了CSRF豁免")
    print("3. 前端添加了CSRF token获取逻辑")
    print("4. 配置了开发环境的CSRF设置")
    
    print("\n测试建议:")
    print("1. 重新启动后端服务器")
    print("2. 在前端尝试上传文件")
    print("3. 检查浏览器控制台是否有CSRF错误")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == '__main__':
    test_csrf_config()
