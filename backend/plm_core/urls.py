"""
PLM系统主URL配置

配置所有应用的API路由和管理界面
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
# from rest_framework.documentation import include_docs_urls
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions

# API文档配置
schema_view = get_schema_view(
    openapi.Info(
        title="PLM系统 API",
        default_version='v1',
        description="产品生命周期管理系统API文档",
        contact=openapi.Contact(email="<EMAIL>"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    # 管理后台
    path('admin/', admin.site.urls),
    
    # API文档
    # path('docs/', include_docs_urls(title='PLM系统 API文档')),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    
    # API路由
    path('api/materials/', include('apps.materials.urls')),
    path('api/bom/', include('apps.bom.urls')),
    path('api/inventory/', include('apps.inventory.urls')),
    path('api/procurement/', include('apps.procurement.urls')),
    path('api/users/', include('apps.users.urls')),
    
    # 认证相关
    path('api-auth/', include('rest_framework.urls')),
]

# 开发环境静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
