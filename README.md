# PLM 产品生命周期管理系统

## 项目概述

PLM（Product Lifecycle Management）产品生命周期管理系统是一套完整的企业级解决方案，专注于物料管理、BOM管理、库存管理、采购管理等核心业务流程，为制造企业提供全面的产品数据管理能力。

## 技术架构

### 后端技术栈
- **框架**: Django 4.2.7 + Django REST Framework
- **数据库**: SQLite（开发）/ MySQL（生产）
- **API文档**: drf-yasg (Swagger)
- **认证**: Django Token Authentication
- **任务队列**: Celery + Redis
- **开发语言**: Python 3.12

### 前端技术栈
- **框架**: Vue.js 3 + Vite
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **日期处理**: Day.js

## 系统功能模块

### 1. 物料管理 (Materials)
- 物料分类管理（支持多级分类）
- 物料主数据管理
- 物料供应商关系管理
- 物料属性动态定义
- 物料生命周期状态管理

### 2. BOM管理 (Bill of Materials)
- 多类型BOM支持（工程BOM、制造BOM、销售BOM）
- BOM明细项管理
- 替代料管理
- BOM变更申请流程
- BOM成本分析
- 版本控制

### 3. 库存管理 (Inventory)
- 多仓库管理
- 库存记录实时跟踪
- 安全库存预警
- 库存锁定机制
- 出入库事务记录

### 4. 采购管理 (Procurement)
- 供应商管理
- 采购申请流程
- 采购订单管理
- 收货管理
- 供应商评级

### 5. 用户管理 (Users)
- 用户账户管理
- 部门组织架构
- 权限控制
- 用户配置文件

## 项目结构

```
plm_system/
├── backend/                    # Django后端
│   ├── apps/                  # 应用模块
│   │   ├── materials/         # 物料管理
│   │   ├── bom/              # BOM管理
│   │   ├── inventory/        # 库存管理
│   │   ├── procurement/      # 采购管理
│   │   └── users/            # 用户管理
│   ├── plm_core/             # 核心配置
│   ├── requirements.txt      # Python依赖
│   └── manage.py            # Django管理脚本
├── frontend/                 # Vue.js前端
│   ├── src/
│   │   ├── views/           # 页面组件
│   │   ├── api/             # API接口
│   │   ├── utils/           # 工具函数
│   │   └── router/          # 路由配置
│   ├── package.json         # 前端依赖
│   └── vite.config.js       # Vite配置
├── deployment/              # 部署配置
├── doc/                     # 项目文档
└── README.md               # 项目说明
```

## 开发指南

### 环境要求
- Python 3.12+
- Node.js 18+
- MySQL 8.0+（生产环境）

### 后端启动

1. 进入后端目录：
   ```bash
   cd backend
   ```

2. 激活虚拟环境：
   ```bash
   source plm_env/bin/activate  # Linux/Mac
   # 或
   plm_env\Scripts\activate     # Windows
   ```

3. 安装依赖：
   ```bash
   pip3 install -r requirements.txt
   ```

4. 数据库迁移：
   ```bash
   python3 manage.py migrate
   ```

5. 创建超级用户：
   ```bash
   python3 manage.py createsuperuser
   ```

6. 启动开发服务器：
   ```bash
   python3 manage.py runserver
   ```

### 前端启动

1. 进入前端目录：
   ```bash
   cd frontend
   ```

2. 安装依赖：
   ```bash
   npm install
   ```

3. 启动开发服务器：
   ```bash
   npm run dev
   ```

### 访问地址

- **前端应用**: http://localhost:5173 (或5174、5175等可用端口)
- **后端API**: http://localhost:8000/api
- **管理后台**: http://localhost:8000/admin
- **API文档**: http://localhost:8000/swagger

### 系统状态

✅ **系统已成功运行！**

- 前端开发服务器已启动
- 后端Django服务器已启动
- 数据库连接正常
- API接口可访问
- 管理后台可访问

### 默认账户

- **用户名**: admin
- **密码**: admin123

## 设计原则

项目严格遵循以下软件设计原则：

1. **DRY原则** (Don't Repeat Yourself): 避免代码重复，提高可维护性
2. **开闭原则** (Open/Closed Principle): 对扩展开放，对修改封闭
3. **单一职责原则** (Single Responsibility Principle): 每个模块只负责一个职责
4. **依赖反转原则** (Dependency Inversion Principle): 依赖抽象而非具体实现
5. **奥卡姆剃刀原则** (Occam's Razor): 保持简单，避免过度设计

## 核心特性

### 1. 模块化设计
- 松耦合的应用模块设计
- 清晰的接口定义
- 可独立开发和测试

### 2. RESTful API
- 标准化的API设计
- 完整的CRUD操作
- 统一的错误处理

### 3. 响应式UI
- 现代化的用户界面
- 移动端适配
- 直观的操作体验

### 4. 数据完整性
- 完善的数据验证
- 事务一致性保证
- 审计跟踪

### 5. 扩展性
- 支持自定义字段
- 插件化架构
- 多租户支持（规划中）

## 与MES系统集成

PLM系统为与MES（制造执行系统）的集成预留了标准化接口：

- 生产计划下达
- 物料需求传递
- 成品入库反馈
- 异常退料处理

## 部署说明

### 生产环境配置

1. **数据库配置**
   - 使用MySQL数据库
   - 配置数据库连接池
   - 启用读写分离（可选）

2. **Web服务器**
   - 使用Nginx作为反向代理
   - 配置SSL证书
   - 启用GZIP压缩

3. **应用服务器**
   - 使用Gunicorn部署Django应用
   - 配置多进程模式
   - 启用日志轮转

4. **前端部署**
   - 构建生产版本：`npm run build`
   - 部署到CDN或静态文件服务器

### 环境变量配置

在生产环境中，需要配置以下环境变量：

```bash
# 数据库配置
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=smart_production
DATABASE_USER=smart_user
DATABASE_PASSWORD=your_password
DATABASE_HOST=localhost
DATABASE_PORT=3306

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 安全配置
SECRET_KEY=your_secret_key
DEBUG=False
ALLOWED_HOSTS=yourdomain.com
```

## 开发规范

### 代码风格
- Python: 遵循PEP 8规范
- JavaScript: 使用ESLint和Prettier
- 中文注释，英文代码
- UI元素使用中文

### 提交规范
- 使用语义化提交信息
- 功能分支开发
- 代码审查制度

### 测试要求
- 单元测试覆盖率 > 80%
- API接口测试
- 前端组件测试

## 版本历史

### v1.0.0 (2024-01-15)
- ✅ 完成基础架构搭建
- ✅ 实现物料管理模块
- ✅ 实现BOM管理模块
- ✅ 实现库存管理模块
- ✅ 实现采购管理模块
- ✅ 实现用户管理模块
- ✅ 完成前端UI界面
- ✅ 集成API文档

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请联系开发团队：
- 邮箱: <EMAIL>
- 技术支持: <EMAIL>

---

**PLM产品生命周期管理系统** - 为制造企业数字化转型提供强有力的支撑！

## 项目概述

本项目是一个基于Django和Vue.js的PLM（产品生命周期管理）系统，专注于物料管理、BOM管理、库存管理和采购管理等核心功能。系统遵循软件设计原则，采用现代化的技术栈，为MES系统预留了标准化的API接口。

## 系统特性

- 🎯 **物料管理**：完整的物料分类、编码、属性管理
- 📋 **BOM管理**：多层级BOM结构设计和版本控制
- 📦 **库存管理**：实时库存跟踪、出入库记录、预警机制
- 🛒 **采购管理**：采购申请、供应商管理、成本控制
- 🔄 **工作流引擎**：可配置的审批流程
- 🔌 **API接口**：RESTful API设计，便于系统集成
- 🌐 **现代化UI**：基于Vue.js和Element Plus的中文友好界面

## 技术栈

### 后端
- **Python 3.11+**
- **Django 4.2** - Web框架
- **Django REST Framework** - API框架
- **MySQL 8.0** - 主数据库
- **Redis 7.0** - 缓存和会话存储
- **Celery** - 异步任务处理
- **PyMySQL** - MySQL数据库连接器

### 前端
- **Vue.js 3** - 前端框架
- **Element Plus** - UI组件库
- **Pinia** - 状态管理
- **Vue Router 4** - 路由管理
- **Vite** - 构建工具
- **Axios** - HTTP客户端

## 项目结构

```
plm_system/
├── backend/                    # Django后端
│   ├── plm_core/              # 项目核心配置
│   ├── apps/                  # 应用模块
│   │   ├── materials/         # 物料管理
│   │   ├── bom/              # BOM管理
│   │   ├── inventory/        # 库存管理
│   │   ├── procurement/      # 采购管理
│   │   └── users/            # 用户管理
│   ├── common/               # 公共组件
│   ├── utils/                # 工具函数
│   ├── requirements.txt      # Python依赖
│   └── manage.py            # Django管理命令
├── frontend/                 # Vue.js前端
├── deployment/              # 部署配置
├── doc/                    # 项目文档
└── README.md              # 项目说明
```

## 快速开始

### 环境要求

- Python 3.11+
- Node.js 16+
- MySQL 8.0
- Redis 7.0

### 后端设置

1. **创建虚拟环境**
```bash
cd backend
python3 -m venv plm_env
source plm_env/bin/activate  # Linux/Mac
# 或
plm_env\Scripts\activate  # Windows
```

2. **安装依赖**
```bash
pip3 install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和Redis连接信息
```

4. **数据库迁移**
```bash
python3 manage.py makemigrations
python3 manage.py migrate
```

5. **创建超级用户**
```bash
python3 manage.py createsuperuser
```

6. **启动开发服务器**
```bash
python3 manage.py runserver
```

### 前端设置

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

## 核心功能模块

### 1. 物料管理模块

- **分类体系**：支持多层级物料分类
- **编码规则**：自动化物料编码生成
- **属性模板**：基于分类的动态属性管理
- **供应商关系**：物料与供应商的关联管理

### 2. BOM管理模块

- **多层级结构**：支持复杂产品的BOM层级设计
- **版本控制**：BOM版本管理和历史追溯
- **成本分析**：基于BOM的成本核算
- **替代料管理**：支持物料替代和优选

### 3. 库存管理模块

- **实时库存**：准确的库存数量跟踪
- **出入库记录**：完整的物料流动记录
- **库存预警**：低库存自动预警机制
- **盘点管理**：支持定期库存盘点

### 4. 采购管理模块

- **采购申请**：基于需求的采购申请流程
- **供应商管理**：供应商信息和评估管理
- **价格管理**：历史价格跟踪和比较
- **交货管理**：采购订单和交货跟踪

## API接口

系统提供完整的RESTful API接口，支持：

- **认证授权**：基于Token的API认证
- **数据分页**：支持大数据量的分页查询
- **过滤排序**：灵活的数据过滤和排序
- **文档化**：基于Swagger的API文档

主要API端点：
- `/api/v1/materials/` - 物料管理
- `/api/v1/bom/` - BOM管理
- `/api/v1/inventory/` - 库存管理
- `/api/v1/procurement/` - 采购管理

## 部署指南

### 开发环境

使用Django开发服务器和Vite开发服务器进行本地开发。

### 生产环境

推荐使用Docker容器化部署：

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 数据库配置

生产环境建议使用MySQL，配置示例：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'smart_production',
        'USER': 'smart_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'sql_mode': 'traditional',
        }
    }
}
```

## 集成说明

### 与MES系统集成

PLM系统为MES系统预留了标准化的API接口：

- **生产计划下发**：PLM向MES推送生产计划
- **物料领料申请**：MES向PLM申请物料领料
- **库存状态同步**：实时同步库存变化
- **成品入库确认**：MES完成生产后通知PLM入库

### 与财务系统集成

- **成本数据提供**：基于BOM的物料成本计算
- **库存价值评估**：库存金额统计和分析
- **采购数据同步**：采购订单和发票数据

## 开发规范

### 编码规范

- 遵循PEP 8 Python编码规范
- 使用中文注释和文档字符串
- API接口返回中文错误信息
- 前端UI元素使用中文

### 设计原则

- **DRY原则**：避免代码重复
- **单一职责**：每个模块职责明确
- **开放封闭**：对扩展开放，对修改封闭
- **依赖反转**：依赖抽象而非具体实现

### 测试要求

- 单元测试覆盖率 > 80%
- API接口集成测试
- 前端组件测试

## 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 技术支持

如有问题或建议，请通过以下方式联系：

- 创建GitHub Issue
- 发送邮件至项目维护者
- 查看项目文档和FAQ

## 更新日志

### v1.0.0 (开发中)
- 完成项目架构设计
- 实现物料管理基础功能
- 完成数据库表结构设计
- 搭建API接口框架