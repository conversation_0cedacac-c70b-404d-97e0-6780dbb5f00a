version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: plm_mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: smart_production
      MYSQL_USER: smart_user
      MYSQL_PASSWORD: smart_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - plm_network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: plm_redis
    command: redis-server --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - plm_network

  # PLM后端服务
  backend:
    build:
      context: ../backend
      dockerfile: ../deployment/backend.Dockerfile
    container_name: plm_backend
    environment:
      - DATABASE_ENGINE=django.db.backends.mysql
      - DATABASE_NAME=smart_production
      - DATABASE_USER=smart_user
      - DATABASE_PASSWORD=smart_password
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
      - DEBUG=False
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
      - static_files:/app/staticfiles
      - media_files:/app/media
    depends_on:
      - mysql
      - redis
    networks:
      - plm_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn plm_core.wsgi:application --bind 0.0.0.0:8000"

  # PLM前端服务
  frontend:
    build:
      context: ../frontend
      dockerfile: ../deployment/frontend.Dockerfile
    container_name: plm_frontend
    ports:
      - "3000:80"
    volumes:
      - ../frontend/dist:/usr/share/nginx/html
    depends_on:
      - backend
    networks:
      - plm_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: plm_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - static_files:/static
      - media_files:/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - plm_network

  # Celery异步任务处理
  celery:
    build:
      context: ../backend
      dockerfile: ../deployment/backend.Dockerfile
    container_name: plm_celery
    environment:
      - DATABASE_ENGINE=django.db.backends.mysql
      - DATABASE_NAME=smart_production
      - DATABASE_USER=smart_user
      - DATABASE_PASSWORD=smart_password
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
    volumes:
      - ../backend:/app
    depends_on:
      - mysql
      - redis
    networks:
      - plm_network
    command: celery -A plm_core worker -l info

volumes:
  mysql_data:
  redis_data:
  static_files:
  media_files:

networks:
  plm_network:
    driver: bridge