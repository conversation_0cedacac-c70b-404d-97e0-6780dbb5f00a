# PLM系统开发指南

## 开发环境搭建

### 1. 环境要求

- **Python**: 3.11+
- **Node.js**: 18+
- **MySQL**: 8.0
- **Redis**: 7.0
- **Git**: 最新版本

### 2. 项目克隆与初始化

```bash
# 克隆项目
git clone <repository-url>
cd plm_system

# 初始化后端环境
cd backend
python3 -m venv plm_env
source plm_env/bin/activate  # Linux/Mac
pip3 install -r requirements.txt

# 初始化前端环境
cd ../frontend
npm install
```

### 3. 数据库配置

#### MySQL设置
```sql
-- 创建数据库
CREATE DATABASE smart_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'smart_user'@'localhost' IDENTIFIED BY 'smart_password';
GRANT ALL PRIVILEGES ON smart_production.* TO 'smart_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 环境变量配置
在backend目录下创建.env文件：
```env
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=smart_production
DATABASE_USER=smart_user
DATABASE_PASSWORD=smart_password
DATABASE_HOST=localhost
DATABASE_PORT=3306
SECRET_KEY=your-secret-key-here
DEBUG=True
```

### 4. 数据库迁移

```bash
cd backend
python3 manage.py makemigrations
python3 manage.py migrate
python3 manage.py createsuperuser
```

## 代码规范

### 1. Python代码规范

#### 基本规范
- 遵循PEP 8编码标准
- 使用4个空格缩进
- 行长度不超过88字符
- 使用中文注释和文档字符串

#### 命名规范
```python
# 类名：大驼峰
class MaterialCategory:
    pass

# 函数名和变量名：小写下划线
def get_material_list():
    material_code = 'MAT001'

# 常量：大写下划线
MAX_RETRY_COUNT = 3
```

#### 文档字符串示例
```python
def create_material(material_data):
    """
    创建物料
    
    Args:
        material_data (dict): 物料数据
            - code (str): 物料编码
            - name (str): 物料名称
            - category_code (str): 分类编码
    
    Returns:
        Material: 创建的物料对象
    
    Raises:
        ValidationError: 数据验证失败
    """
    pass
```

### 2. Vue.js代码规范

#### 组件命名
```vue
<!-- 组件文件名：PascalCase -->
<!-- MaterialList.vue -->

<!-- 组件注册：kebab-case -->
<material-list />
```

#### 代码结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 导入
import { ref, computed } from 'vue'

// 响应式数据
const materialList = ref([])

// 计算属性
const filteredMaterials = computed(() => {
  // 计算逻辑
})

// 方法
const handleCreate = () => {
  // 处理逻辑
}
</script>

<style scoped>
/* 样式 */
</style>
```

### 3. API设计规范

#### RESTful API规范
```python
# 物料管理API
GET    /api/v1/materials/           # 获取物料列表
POST   /api/v1/materials/           # 创建物料
GET    /api/v1/materials/{code}/    # 获取物料详情
PUT    /api/v1/materials/{code}/    # 更新物料
DELETE /api/v1/materials/{code}/    # 删除物料
```

#### 响应格式标准
```python
# 成功响应
{
    "code": 200,
    "message": "操作成功",
    "data": {
        # 数据内容
    },
    "timestamp": "2024-01-15T10:30:00Z"
}

# 错误响应
{
    "code": 400,
    "message": "请求参数错误",
    "errors": {
        "field_name": ["具体错误信息"]
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

## 数据库设计规范

### 1. 表设计原则

- 表名使用复数形式，小写下划线分隔
- 字段名使用小写下划线分隔
- 主键统一使用id
- 外键以_id结尾
- 时间字段统一使用created_at, updated_at

### 2. 索引设计

```python
class Material(models.Model):
    # 单字段索引
    code = models.CharField(max_length=100, db_index=True)
    
    class Meta:
        # 复合索引
        indexes = [
            models.Index(fields=['category_code', 'status']),
            models.Index(fields=['name']),
        ]
```

### 3. 数据类型选择

```python
# 字符串字段
code = models.CharField(max_length=100)  # 固定长度
description = models.TextField()  # 变长文本

# 数值字段
quantity = models.DecimalField(max_digits=15, decimal_places=6)  # 精确数值
price = models.DecimalField(max_digits=15, decimal_places=4)

# 时间字段
created_at = models.DateTimeField(auto_now_add=True)
updated_at = models.DateTimeField(auto_now=True)

# JSON字段
attributes = models.JSONField(default=dict)
```

## 测试规范

### 1. 单元测试

```python
# tests/test_materials.py
from django.test import TestCase
from apps.materials.models import Material, MaterialCategory

class MaterialModelTest(TestCase):
    def setUp(self):
        """测试数据准备"""
        self.category = MaterialCategory.objects.create(
            code='1000',
            name='电子元器件',
            created_by='test_user'
        )
    
    def test_create_material(self):
        """测试创建物料"""
        material = Material.objects.create(
            code='MAT001',
            name='测试物料',
            category_code='1000',
            unit='个',
            created_by='test_user'
        )
        self.assertEqual(material.code, 'MAT001')
        self.assertEqual(material.status, 'DRAFT')
```

### 2. API测试

```python
# tests/test_api.py
from rest_framework.test import APITestCase
from django.contrib.auth.models import User

class MaterialAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_create_material(self):
        """测试创建物料API"""
        data = {
            'code': 'MAT001',
            'name': '测试物料',
            'category_code': '1000',
            'unit': '个'
        }
        response = self.client.post('/api/v1/materials/', data)
        self.assertEqual(response.status_code, 201)
```

## 部署指南

### 1. 开发环境部署

```bash
# 后端服务
cd backend
source plm_env/bin/activate
python3 manage.py runserver

# 前端服务
cd frontend
npm run dev
```

### 2. 生产环境部署

#### Docker部署
```bash
# 构建并启动所有服务
cd deployment
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs backend
```

#### 传统部署
```bash
# 后端部署
cd backend
pip3 install -r requirements.txt
python3 manage.py collectstatic --noinput
gunicorn plm_core.wsgi:application --bind 0.0.0.0:8000

# 前端部署
cd frontend
npm run build
# 将dist目录内容复制到nginx静态文件目录
```

### 3. 数据库备份

```bash
# 备份数据库
mysqldump -u smart_user -p smart_production > backup.sql

# 恢复数据库
mysql -u smart_user -p smart_production < backup.sql
```

## 性能优化

### 1. 数据库优化

- 合理使用索引
- 避免N+1查询问题
- 使用select_related和prefetch_related
- 定期分析慢查询

```python
# 优化查询示例
materials = Material.objects.select_related('category').filter(
    status='ACTIVE'
).only('code', 'name', 'category__name')
```

### 2. 缓存策略

```python
# Redis缓存使用
from django.core.cache import cache

def get_material_by_code(code):
    cache_key = f'material:{code}'
    material = cache.get(cache_key)
    if material is None:
        material = Material.objects.get(code=code)
        cache.set(cache_key, material, timeout=3600)
    return material
```

### 3. API优化

- 使用分页减少单次数据传输量
- 实现字段过滤和稀疏字段集
- 使用异步任务处理耗时操作

## 安全规范

### 1. 数据验证

```python
# 模型层验证
class Material(models.Model):
    code = models.CharField(
        max_length=100,
        validators=[validate_material_code]
    )
    
# API层验证
class MaterialSerializer(serializers.ModelSerializer):
    def validate_code(self, value):
        if not value.startswith('MAT'):
            raise serializers.ValidationError('物料编码必须以MAT开头')
        return value
```

### 2. 权限控制

```python
# 基于权限的API访问控制
class MaterialViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, MaterialPermission]
    
    def get_queryset(self):
        # 根据用户权限过滤数据
        user = self.request.user
        if user.has_perm('materials.view_all'):
            return Material.objects.all()
        else:
            return Material.objects.filter(created_by=user.username)
```

### 3. 数据加密

- 敏感信息使用加密存储
- API传输使用HTTPS
- 定期更新密钥

## 常见问题解决

### 1. 数据库连接问题

```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u smart_user -p -h localhost
```

### 2. 依赖包冲突

```bash
# 清理pip缓存
pip3 cache purge

# 重新安装依赖
pip3 install -r requirements.txt --force-reinstall
```

### 3. 前端编译错误

```bash
# 清理node_modules
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version
npm --version
```

## 开发工作流

### 1. 功能开发流程

1. 创建功能分支
2. 编写测试用例
3. 实现功能代码
4. 运行测试确保通过
5. 代码审查
6. 合并到主分支

### 2. Git工作流

```bash
# 创建功能分支
git checkout -b feature/material-management

# 提交代码
git add .
git commit -m "feat: 添加物料管理功能"

# 推送分支
git push origin feature/material-management

# 创建Pull Request
```

### 3. 代码审查要点

- 代码是否符合规范
- 是否有足够的测试覆盖
- 是否存在安全漏洞
- 性能是否有优化空间
- 文档是否完整

## 监控和日志

### 1. 应用监控

- 使用Django的内置日志系统
- 配置Prometheus监控指标
- 设置Grafana可视化面板

### 2. 错误追踪

```python
# 日志配置
import logging

logger = logging.getLogger(__name__)

def create_material(data):
    try:
        material = Material.objects.create(**data)
        logger.info(f'物料创建成功: {material.code}')
        return material
    except Exception as e:
        logger.error(f'物料创建失败: {str(e)}', exc_info=True)
        raise
```

这个开发指南为PLM系统的开发提供了完整的规范和最佳实践，确保代码质量和系统稳定性。