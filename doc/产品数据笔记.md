# 产品数据管理核心概念与实践指南

## 目录
1. [Part（物料/部件）管理](#part物料部件管理)
2. [BOM（物料清单）管理](#bom物料清单管理)
3. [BOM结构设计原则](#bom结构设计原则)
4. [PDM系统价值与应用](#pdm系统价值与应用)

---

## Part（物料/部件）管理

### 1. Part基本概念

**Part**（部件/物料）是产品数据管理的基础单元，可以指代：
- 元器件（如电阻、电容）
- 原材料（如钢材、塑料）
- 半成品（如电路板、外壳）
- 成品（如最终产品）

**相关术语**：物料、物料编码、BOM编码、部件编码

### 2. Part分类体系

#### 2.1 分类核心逻辑
- **目的**：通过分类对物料进行结构化描述，确保管理效率
- **原则**：
  - 按物料的**自然属性**分类（功能、材质、用途等）
  - 分类维度需**唯一**，避免交叉混淆
  - 最终形成**分层树状结构**（大类→中类→小类）

#### 2.2 分类实施步骤

**第一步：构建Part分类树**
```
大类（如：电子元器件）
├─ 中类（如：电阻器）
│   ├─ 小类（如：贴片电阻）
│   └─ 小类（如：碳膜电阻）
└─ 中类（如：电容器）
```

**第二步：定义特征属性模板**
- **作用**：为每一类Part绑定结构化描述字段
- **示例**：
  | 分类编码 | 分类名称 | 属性模板字段 |
  |----------|----------|--------------|
  | 5011 | 低压电力电缆 | 芯数、额定电压、截面面积 |

#### 2.3 分类编码示例

##### 电子元器件领域
| 层级 | 分类名称 | 编码规则 | 说明 |
|------|----------|----------|------|
| 大类 | 电子元器件 | `1000` | 最高层级分类 |
| 中类 | 电阻器 | `1100` | 大类下的功能细分 |
| 小类 | 贴片电阻 | `1101` | 中类下的具体类型 |
| 小类 | 碳膜电阻 | `1102` | |
| 中类 | 电容器 | `1200` | |
| 小类 | 陶瓷电容 | `1201` | |
| 小类 | 电解电容 | `1202` | |

##### 机械零件领域
| 层级 | 分类名称 | 编码规则 | 说明 |
|------|----------|----------|------|
| 大类 | 机械结构件 | `2000` | |
| 中类 | 紧固件 | `2100` | 如螺丝、螺母等 |
| 小类 | 六角螺栓 | `2101` | 细分材质或规格 |
| 小类 | 内六角螺丝 | `2102` | |
| 中类 | 轴承 | `2200` | |
| 小类 | 滚珠轴承 | `2201` | |

##### 电力设备领域
| 层级 | 分类名称 | 编码规则 | 属性模板示例 |
|------|----------|----------|--------------|
| 大类 | 电缆与线材 | `5000` | |
| 中类 | 电力电缆 | `5010` | |
| 小类 | 低压电力电缆 | `5011` | 芯数、额定电压、截面面积 |
| 小类 | 高压电力电缆 | `5012` | 绝缘材料、耐压等级 |

#### 2.4 编码设计原则
1. **层级标识**：
   - 大类：`X000`（首位表领域，后三位为0）
   - 中类：`XX00`（继承大类首位，细分第二位）
   - 小类：`XXX1`（继承中类，末位自由分配）
2. **扩展性**：预留编码空间（如 `5013`, `5014` 为未来电缆类型保留）

### 3. Part新增管理原则

#### 3.1 核心原则
**"尽量不新增Part"**是核心原则，目的是避免物料编码冗余和管理混乱。

#### 3.2 必须新增Part的3种情况

##### 1. 关键属性差异
- **条件**：当物料的**功能、性能或关键参数**不同，且影响产品设计/生产时
- **示例**：
  - 现有电阻：`1101`（贴片电阻，阻值1kΩ，精度5%）
  - 新增电阻：阻值10kΩ或精度1%（需新增编码 `1103`）

##### 2. 物理不可替代性
- **条件**：物料在物理上**无法直接替换**使用（如尺寸、接口不兼容）
- **示例**：
  - 现有螺栓：`2101`（M4×10mm六角螺栓）
  - 新增螺栓：M5×10mm（需新增编码 `2103`）

##### 3. 供应链或成本影响
- **条件**：不同供应商/批次的物料可能导致**质量波动或成本差异**
- **示例**：
  - 现有电缆：`5011`（供应商A，铜芯）
  - 新增电缆：供应商B的铝芯电缆（需新增编码 `5013`）

#### 3.3 不应新增Part的3种情况

##### 1. 仅非关键属性不同
- **示例**：包装颜色、标签文字（可通过备注或扩展属性字段描述）

##### 2. 临时替代或等效物料
- **示例**：短期使用的替代供应商物料（应在BOM中标注临时性）

##### 3. 历史遗留重复编码
- **处理**：合并旧编码，统一使用现有分类模板

#### 3.4 操作建议

**新增Part前的检查清单**：
- [ ] 是否影响产品功能/性能？
- [ ] 是否导致生产或装配变更？
- [ ] 是否有库存或采购成本差异？

**替代方案**：
- 在现有Part的**属性模板**中扩展字段（如增加"备注"或"特殊参数"）
- 使用BOM的**替代料功能**（如Altium Designer中的替代元件组）

#### 3.5 案例对比
| 场景 | 是否新增Part | 理由 |
|------|-------------|------|
| 电缆电压从220V→380V | 是 | 关键电气参数变化，影响安全设计 |
| 螺丝表面镀锌→镀镍 | 否 | 可通过属性字段"表面处理"记录差异 |
| 电容品牌A→品牌B | 视情况 | 若参数一致则否，若寿命差异大则需 |

---

## BOM（物料清单）管理

### 1. BOM基本概念

**BOM**（Bill of Materials，物料清单）表达Part之间的组成关系，由三要素构成：
- **父项**：被组成的对象
- **子项**：组成父项的部件
- **用量**：子项在父项中的使用数量

### 2. BOM权责划分

#### 2.1 研发的核心责任
**BOM的源头是研发，是研发的第一责任**

| 要素 | 研发控制内容 | 示例 |
|------|-------------|------|
| **父项** | 定义产品模块化结构 | 决定手机BOM中是否包含"摄像头模组"（第二层）作为独立父项 |
| **子项** | 选择技术可行的组成部分 | 摄像头模组下必须使用特定型号的镜头（第三层），禁用替代料 |
| **用量** | 计算理论需求（含损耗率） | 每台手机需1.05个螺丝（含5%装配损耗） |

**研发第一性原理**：
BOM本质是**产品设计意图的物料化表达**，研发需确保三要素精确反映：
- 功能实现（如电气性能）
- 物理约束（如接口兼容性）
- 可制造性（如装配顺序）

#### 2.2 使用部门的属性扩展权
| 部门 | 可添加属性类型 | 作用 | 约束条件 |
|------|---------------|------|----------|
| 生产 | 工艺参数（如扭矩、温度） | 指导车间操作 | 不得修改研发定义的父子关系或基础用量 |
| 采购 | 供应商偏好、交货周期 | 优化供应链 | 需通过ECN（工程变更通知）流程审核 |
| 成本 | 分摊系数、工时 | 财务核算 | 需与研发确认技术可行性 |

### 3. BOM对生产的影响

#### 3.1 直接影响链
```mermaid
graph LR
    A[研发BOM] -->|传递| B[工艺BOM]
    B -->|派生| C[制造BOM]
    C --> D[生产执行]
    D --> E[质量追溯]
```

#### 3.2 关键控制点
- **源头准确性**：研发BOM错误会导致全链条失效（如错料停线）
- **变更传导**：研发需同步变更到所有派生BOM（如ECN覆盖生产版本）

### 4. 多层BOM结构管理

#### 4.1 中间层保留判断标准

**必须保留中间层的场景**：
| 场景 | 原因 | 示例 |
|------|------|------|
| **独立库存或采购** | 中间层Part作为半成品需单独管理库存或采购 | 电机总成（第二层）由齿轮+外壳（第三层）组成，但电机需独立采购和备货 |
| **模块化设计** | 中间层为可复用模块，可能被多个父项引用 | 电源模块（第二层）被多个产品BOM引用 |
| **生产装配节点** | 中间层代表产线的一个组装环节 | 电路板焊接（第二层）需作为独立工序，由PCB+元件（第三层）组成 |
| **成本核算需求** | 需按层级核算制造成本 | 汽车仪表盘总成（第二层）需单独计算人工和物料成本 |

**可省略中间层的场景**：
| 场景 | 原因 | 替代方案 |
|------|------|----------|
| **纯逻辑分组** | 中间层仅为分类用途，无实际管理意义 | 用虚拟件（Phantom Part）标记，或通过BOM软件折叠显示 |
| **临时组合** | 中间层组合无复用价值 | 直接在父项下挂接第三层Part，并备注关联关系 |

#### 4.2 决策流程图
```mermaid
graph TD
    A[中间层Part是否独立存在?] -->|是| B[需采购/库存?]
    A -->|否| C[可省略]
    B -->|是| D[必须保留]
    B -->|否| E[是否代表生产节点?]
    E -->|是| D
    E -->|否| C
```

#### 4.3 最佳实践建议
1. **研发设计阶段**：
   - 默认保留所有中间层，确保BOM完整表达产品结构
   - 用`虚拟件`标记纯逻辑分组（如编码前缀`V_`）

2. **生产准备阶段**：
   - 与制造部门核对，删除无实际意义的中间层（如不影响工艺路线）

#### 4.4 案例说明
| 层级 | Part类型 | 保留必要性 | 处理方式 |
|------|----------|------------|----------|
| 第一层 | 成品手机 | 必须 | |
| 第二层 | 摄像头模组 | 必须 | 独立采购且多型号复用 |
| 第三层 | 镜头+传感器 | 必须 | |
| 第二层 | "包装材料组" | 可省略 | 改为虚拟件或直接挂接到第一层 |

---

## BOM结构设计原则

### 1. 五大核心原则

#### 1.1 BOM与产品的一致性
**核心要求**：
- **物理结构映射**：BOM层级需严格对应产品物理组装关系
  *示例：手机BOM中"屏幕总成"必须包含触控层、显示层、边框等子项*
- **功能实现验证**：每个Part的组合需满足产品性能指标
  *检查点：电气连接性、机械强度、热传导路径*

**实施工具**：
```mermaid
graph TD
    A[产品设计图纸] --> B[功能模块划分]
    B --> C[BOM层级映射]
    C --> D[DFMEA验证]
```

#### 1.2 BOM与流程的一致性
**工艺对齐策略**：
| 工艺类型 | BOM结构调整方法 | 案例 |
|----------|----------------|------|
| 流水线生产 | 按工位划分BOM层级 | 汽车装配线对应车门/底盘分装 |
| 单元制造 | 将完整模块作为独立层级 | 医疗设备中的传感器模组 |
| 外包加工 | 标记外协件并预留工艺参数接口 | 表面处理件标注镀层厚度要求 |

**冲突解决流程**：
```mermaid
graph LR
    A[工艺路线变更] --> B{BOM是否支持?}
    B -->|是| C[直接实施]
    B -->|否| D[发起ECN流程]
    D --> E[研发评估技术可行性]
```

#### 1.3 BOM扁平化
**优化技术**：
1. **虚拟件应用**
   - 编码规则：`V_`前缀（如`V_PACKING`）
   - 系统配置：在ERP中设置为"非库存物料"

2. **层级压缩条件**
   | 压缩前结构 | 压缩条件 | 压缩后结构 |
   |------------|----------|------------|
   | A→B→C→D | B无独立库存/工艺 | A→(C+D) |
   | 主板→PCBA→元器件 | PCBA不外协不备货 | 主板→元器件 |

**风险控制**：
- 保留原始设计BOM作为技术存档
- 建立压缩映射表（记录虚拟件与实际组件关系）

#### 1.4 Part归一化
**实施方法论**：
1. **相似性分析矩阵**
   | 参数 | 权重 | PartA | PartB | 是否可合并 |
   |------|------|-------|-------|------------|
   | 功能 | 40% | ✓ | ✓ | ✓ |
   | 尺寸 | 30% | 10mm | 12mm | ✗ |
   | 供应商 | 20% | S1 | S1 | ✓ |

2. **替代料策略**
   - 主料：`1101`（贴片电阻1kΩ）
   - 替代料：`1101-ALT1`（同参数不同品牌）
   - 系统配置：设置优先使用级

#### 1.5 BOM结构完整性
**成熟度检查清单**：
| 层级 | 检查项 | 达标标准 |
|------|--------|----------|
| 第一层 | 所有关键模块是否齐全 | 无缺失功能单元 |
| 中间层 | 工艺路线是否明确 | 每个节点有对应SOP |
| 末层 | 物料可采购性验证 | 供应商交期<30天 |

**验证流程**：
```mermaid
graph TB
    A[设计BOM] --> B[工艺评审]
    B --> C[试产验证]
    C --> D{合格?}
    D -->|是| E[发布生产BOM]
    D -->|否| F[回溯修改]
```

### 2. 五大原则的协同应用

**汽车线束BOM设计案例**：
1. **一致性**：线束分段对应车门/底盘物理结构
2. **流程对齐**：分装工位对应BOM子层级
3. **扁平化**：将"胶带缠绕"虚拟件合并到父项
4. **归一化**：统一使用3种线径规格（替代原有5种）
5. **完整性**：每段线束标注导通测试参数

### 3. 实施路线图
1. **诊断阶段**：现有BOM与原则的差距分析
2. **重构阶段**：按优先级逐步优化（先关键部件后辅助材料）
3. **固化阶段**：建立BOM设计规范与检查清单

---

## PDM系统价值与应用

### 1. PDM系统五大核心价值

#### 1.1 数据与流程集成
**实现方式**：
- 通过工作流引擎将产品数据（CAD模型、BOM、工艺文件）与审批/发布流程绑定
- 自动触发ECN流程（如设计变更时联动更新BOM和工艺路线）

**业务价值**：
- 消除信息孤岛，设计变更响应速度提升50%+

#### 1.2 产品结构与ERP集成
**典型集成点**：
```mermaid
graph LR
    A[PDM产品结构] -->|接口| B[ERP物料主数据]
    A -->|BOM发布| C[ERP生产BOM]
```

**关键控制**：
- 确保Part分类编码与ERP物料编码一一对应
- 传递技术属性（如材料、尺寸）供ERP成本核算

#### 1.3 安全协同环境
**权限模型示例**：
| 角色 | 数据权限 | 操作权限 |
|------|----------|----------|
| 设计工程师 | 本产品线数据 | 创建/修改CAD、发起ECN |
| 工艺工程师 | 跨产品线工艺数据 | 编辑工艺路线、查看完整BOM |
| 供应商 | 指定零部件数据 | 只读视图 |

#### 1.4 工程变更（EC）管理
**EC四大特性实现**：
- **协调性**：自动通知受影响部门（生产/采购/质量）
- **全面性**：变更影响分析报告自动生成
- **配套性**：关联文档（如图纸、测试报告）版本同步更新
- **及时性**：移动端审批+看板跟踪进度

#### 1.5 文档全生命周期管理
**集成能力**：
- 版本控制：保留历史版本并支持差异对比
- 关联追溯：点击BOM行项可直接查看对应3D模型

### 2. PDM作为一级配置库的核心定位

#### 2.1 研发数据中枢地位
```mermaid
graph TB
    PDM -->|提供| ERP[ERP系统]
    PDM -->|同步| MES[MES系统]
    PDM -->|交互| CRM[CRM系统]
    PDM -->|对接| SCM[供应链系统]
```

#### 2.2 阶段性成果管理
**里程碑控制**：
- 概念设计→详细设计→试产 各阶段数据冻结与基线化
- 支持按版本快照回溯（如V1.0原型阶段完整数据包）

#### 2.3 唯一数据源（SSOT）价值
- **消除冲突**：生产/采购部门直接使用PDM发布的BOM，避免多版本混乱
- **审计合规**：所有变更留痕

---

## 总结

产品数据管理是一个系统工程，需要从Part分类、BOM设计、结构优化到系统集成等多个维度进行综合考虑。通过建立规范的分类体系、严格的新增管理原则、科学的BOM设计方法以及完善的PDM系统支持，可以实现产品数据的高效管理和准确传递，为企业的研发、生产和供应链管理提供强有力的支撑。 