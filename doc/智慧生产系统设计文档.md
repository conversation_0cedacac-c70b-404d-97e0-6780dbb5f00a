# 智慧生产系统设计文档

## 1. 系统概述

​	本文档详细描述了PLM系统与MES系统的设计思路及其相互关系，重点围绕压力计产品从计划投产到最终发货的完整生产流程展开建模与分析。
### 1.1系统架构设计
​	在系统架构层面，**PLM系统**主要负责物料的入库、出库、库存管理、编码、分类、采购，BOM表管理等任务，保障生产过程中的物料可追溯性与准确性。**MES系统**则涵盖了工单管理、生产进度跟踪、工艺参数采集、设备联动控制以及工序间的信息传递等核心功能，确保生产过程按计划、有节奏地进行。
### 1.2系统文档设计
​	为了清晰表达系统的数据结构和业务流程，本设计文档采用了**UML类图**展示系统中的主要业务实体及其属性、关系，构建出清晰的数据模型结构；同时，结合实际工艺流程，使用**有限状态机（FSM）图**建模生产过程中各工序节点的状态转换逻辑，从工单创建、任务下发到各工序状态的推进与完成，实现对流程状态的统一建模与过程控制。
​	通过对仓库系统与生产系统在业务层与数据层的深入集成，智慧生产系统实现了从原材料入库到产品交付的全生命周期数字化管理，显著提升了工艺执行效率、物料利用率和管理可视化水平，为推动企业智能制造转型奠定了坚实基础。

### 1.3系统交互设计
```mermaid
sequenceDiagram
    participant MES系统
    participant PLM系统
    
	PLM系统->>MES系统: 下达生产计划
    MES系统->>PLM系统: 发起原材料领料申请（BOM表）
    PLM系统-->>MES系统: 返回领料单号
    
    PLM系统->>PLM系统: 执行出库操作，更新库存
    PLM系统-->>MES系统: 出库完成通知
    MES系统->>MES系统: 成品制作
    
    MES系统->>PLM系统: 提交成品入库信息
    PLM系统->>PLM系统: 执行入库操作，更新库存
    
    MES系统->>PLM系统: 异常退料操作
    PLM系统->>PLM系统: 登记退料并更新库存
```
设计说明：
在生产一个产品前，研发先在PLM系统中的元器件优选库选择物料，编辑好对应的BOM表，如果物料缺失则申请采购，采购申请通过后自动分类及编码，在物料到货后，PLM系统需对到货物料进行入库登记，并及时更新库存信息。入库完成后，物料方可参与后续的领料与生产流程。
此外，PLM系统在响应MES系统的领料申请时，需生成对应的领料单号，作为出库操作的唯一标识。该领料单号除提供给生产系统用于物料核对和追溯外，还需同步给财务人员，用于后续账务处理。

## 2. PLM系统设计
### 2.1 数据模型设计

以下类图展示了PLM系统的部分数据模型：

```mermaid
classDiagram
    class 物料 {
        +string 编号
        +string 名称
        +string 类别
        +string 单位
        +string 供应商
        +int 数量
        +string 备注
    }

    class 元器件 {
        +string 编码
        +string 分类名称
        +string 分类编码
        +string 描述
        +string 规格
        +string 单位
        +string 优选等级
    }

    class 领料明细 {
        +string 物料编号
        +int 数量
        +string 用途
    }

    class 领料单 {
        +string 单号
        +date 日期
        +string 领用部门
        +string 领用人
        +string 发料人
    }

    class 补领单 {
        +string 补单号
        +string 原领料单号
        +string 原因
    }

    class 出入库记录 {
        +string 记录编号
        +string 物料编号
        +string 类型 <<入库|出库>>
        +date 日期
        +int 数量
        +float 单价
        +float 总价
        +string 领用人
        +string 用途说明
        +string 备注
    }

    class 库存汇总 {
        +string 物料编号
        +string 类别
        +string 单位
        +int 期初库存
        +int 本年入库
        +int 本年出库
        +int 期末库存
        +计算库存()
    }

    class 库存明细 {
        +string 物料编号
        +date 日期 
        +int 当前库存
        +int 安全库存
        +string 操作来源
        +string 备注
        +更新库存()
        +预警检查()
    }

    class 操作员 {
        +string 姓名
        +string 职位
        +出库()
        +入库()
        +采购()
        +审批()
        +创建BOM表()
    }

    class 申领BOM {
        +string 申领编号
        +string 产品编号
        +string 产品名称
        +string 申请人
        +date 创建日期
        +string 备注
    }
    class 创建BOM {
        +string BOM编号
        +string 产品编号
        +string 产品名称
        +string 创建人
        +date 创建日期
        +string 备注
    }
    class BOM明细 {
        +string BOM编号
        +string 物料编号
        +int 用量
        +string 物料名称
        +string 单位
        +string 备注
    }

    %% 新增关系
    创建BOM "1" o-- "1" 物料 : 对应成品
    创建BOM "1" o-- "*" BOM明细 : 包含
    申领BOM "1" o-- "1" 物料 : 对应成品
    申领BOM "1" o-- "*" BOM明细 : 包含
    BOM明细 "n" --> "1" 物料 : 引用原材料

    %% 原有关系
    领料单 "1" o-- "0..*" 领料明细 : 包含
    领料单 "1" o-- "*" 补领单 : 包含/补充
    领料明细 "n" --> "1" 物料 : 引用
    补领单 "1" o-- "*" 领料明细 : 包含
    出入库记录 "n" --> "1" 物料 : 涉及
    出入库记录 "n" --> "1" 操作员 : 执行人
    创建BOM "n" --> "1" 操作员 : 执行人
    库存汇总 "1" --> "1" 物料 : 汇总对应
    库存明细 "n" --> "1" 物料 : 具体库存项
    库存明细 "0..1" --> "0..1" 出入库记录 : 来源于

    %% 元器件和物料的关系
    元器件 <|-- 物料 : 属于元器件
```
### 2.2 有限状态机
以下是PLM（产品生命周期管理）系统的有限状态机（FSM）图示，涵盖从物料管理到产品发布的核心状态流转逻辑：

```mermaid
stateDiagram-v2
    [*] --> 物料待分类 : 新物料到货
    物料待分类 --> 已编码: 完成分类编码
    已编码 --> 库存可用: 入库完成
    库存可用 --> 已锁定: 被BOM引用/领料申请
    已锁定 --> 出库中: 领料单确认
    出库中 --> 已出库: 实物出库
    已出库 --> 生产中: MES接收物料
    生产中 --> 已报废: 生产异常损耗
    生产中 --> 成品待验: 生产完成
    成品待验 --> 成品合格: 质检通过
    成品合格 --> 已入库: 成品入库
    已入库 --> 已发货: 订单交付

    state 变更管理 {
        已编码 --> 变更中: 发起物料变更
        变更中 --> 已编码: 变更批准
        变更中 --> 已停用: 变更驳回
    }

    state 异常处理 {
        已锁定 --> 库存可用: 领料取消
        出库中 --> 库存可用: 出库撤回
        成品待验 --> 返工: 质检不合格
        返工 --> 生产中: 重新投料
    }

    note left of 物料待分类
        **分类规则**：
        1. 按功能/材质分类
        2. 分配层级编码
        3. 绑定属性模板
    end note

    note right of 已锁定
        **锁定逻辑**：
        - BOM引用自动锁定
        - 安全库存保留机制
        - 超量申请需审批
    end note
```

### 关键状态说明：
| 状态        | 触发条件         | 系统行为           |
| --------- | ------------ | -------------- |
| **物料待分类** | 新物料采购到货      | 触发分类及编码        |
| **已编码**   | 完成分类和属性定义    | 生成唯一物料编码       |
| **库存可用**  | 入库审核通过       | 开放给BOM引用和领用申请  |
| **已锁定**   | 被BOM引用或领料单创建 | 扣减可用库存，保留物理库存  |
| **变更中**   | 发起工程变更(ECN)  | 冻结相关BOM，需跨部门审批 |

### 异常处理路径：
1. **领料撤回**：出库前发现错误 → 释放库存
2. **生产退料**：不良品退回 → 触发库存调整
3. **变更冲突**：新旧版本共存 → 版本隔离管理

## 3.MES系统设计
### 3.1 压力计生产流程
1.计划投产 → 2.领料 → 3. 传感仓打标，隔离仓正负极打标 → 4. 传感仓组装 → 5. 测五线与传感器之间电阻 → 6. 传感仓老化 → 7. 空壳组装打压打标 → 8. 传感器，电路板组装 → 9. 电路板程序烧录 → 10.老化前检测 → 11. 校准前老化 → 12. 校准 → 13. 刷三防漆 → 14. 校准后老化 → 15. 成品组装 → 16. 成品检测 → 17. 成品老化 → 18. 入库
### 3.2 数据模型设计

以下类图展示了MES系统的部分数据模型：

```mermaid
classDiagram
    class 项目批次 {
        +String 批次编号
        +String 项目名称
        +Date 开始时间
        +Integer 数量
        +String 批次状态
        +创建批次()
        +更新状态()
        +查询进度()
    }

    class 物料管理 {
        +String 物料名称
        +Integer 数量
        +Date 领料时间
        +物料出库()
    }

    class 传感器 {
        +String 传感器序列号
        +String 型号规格
        +String 打标编号
        +Boolean 检测电阻
        +执行打标()
        +检测电阻()
        +记录参数()
    }

    class 外壳组件 {
        +String 外壳编号
        +String 表面处理
        +Boolean 隔离仓正负极打标
        +执行打标()
        +执行打压()
        +刷三防漆()
    }

    class 电路板 {
        +String 电路板编号
        +Boolean 接线状态
        +Boolean 烧录状态
        +Date 烧录时间
        +接线()
        +烧录()
        +记录编号()
    }
    
    class 其他组件 {
        +String 组件名称
        +记录()
    }
    
    class 生产工序 {
        +String 工序编号
        +String 工序名称
        +Integer 工序顺序
        +String 工序状态
        +Date 开始时间
        +Date 结束时间
        +String 操作员
        +String 设备编号
        +开始工序()
        +完成工序()
        +记录时间()
        +处理异常()
    }

    class 组装工序 {
        +String 组装编号
        +String 组装类型
        +Date 开始时间
        +Date 结束时间
        +Integer 打压批次
        +执行组装()
    }

    class 老化工序 {
        +String 老化编号
        +Date 开始时间
        +Date 结束时间
        +Integer 累计小时
        +Float 老化温度
        +String 老化状态
        +Boolean 时间达标
        +开始老化()
        +暂停老化()
        +恢复老化()
        +结束老化()
        +计算累计时间()
    }

    class 校准工序 {
        +String 校准编号
        +String 校准类型
        +String 校准参数
        +String 校准结果
        +执行校准()
        +记录参数()
    }

    class 检测工序 {
        +String 检测编号
        +String 检测阶段
        +String 检测参数
        +String 检测结果
        +String 检测标准
        +String 检测员
        +Date 检测时间
        +String 不合格原因
        +Integer 返回工序
        +执行检测()
        +判断质量()
        +记录结果()
        +处理不合格()
    }

    class 压力计产品 {
        +String 产品编号
        +String 批次编号
        +String 当前状态
        +Date 生产开始时间
        +Date 实际完成时间
        +Integer 实际生产天数
        +String 工序记录
        +String 最终质量等级
        +String 备注
        +更新状态()
        +记录工序()
        +质量评估()
        +生成标签()
    }

    项目批次 --> 压力计产品
    物料管理 --> 传感器
    物料管理 --> 外壳组件
    物料管理 --> 电路板
    物料管理 --> 其他组件

    压力计产品 --> 生产工序
    生产工序 --> 组装工序
    生产工序 --> 检测工序
    生产工序 --> 老化工序
    生产工序 --> 校准工序

    其他组件 --> 压力计产品

    传感器 --> 压力计产品
    外壳组件 --> 压力计产品
    电路板 --> 压力计产品
```


### 3.3 有限状态机图

以下状态机图展示了压力计生产的完整状态转换流程：

```mermaid
stateDiagram-v2
    [*] --> 计划投产 : 下达生产计划

    计划投产 --> 领料 : 创建批次
    领料 --> 传感仓打标隔离仓打标 : 完成领料
    传感仓打标隔离仓打标 --> 传感仓组装 : 打标完成
    传感仓组装 --> 测五线与传感器之间电阻 : 完成组装

    测五线与传感器之间电阻 --> 传感仓老化 : 电阻测试合格
    测五线与传感器之间电阻 --> 传感仓组装 : 电阻异常（返工）

    传感仓老化 --> 空壳组装打压打标 : 老化完成
    空壳组装打压打标 --> 传感器电路板组装 : 完成打压
    传感器电路板组装 --> 电路板程序烧录 : 完成组装
    电路板程序烧录 --> 老化前检测 : 烧录程序

    老化前检测 --> 校准前老化 : 检测合格
    老化前检测 --> 电路板程序烧录 : 检测不合格
    老化前检测 --> 传感仓组装 : 严重不合格（返工）

    校准前老化 --> 校准 : 老化完成
    校准前老化 --> 电路板程序烧录 : 检测不合格
    校准前老化 --> 传感仓组装 : 严重不合格

    校准 --> 刷三防漆 : 校准完成
    刷三防漆 --> 校准后老化 : 刷漆完成
    校准后老化 --> 成品组装 : 老化完成
    校准后老化 --> 校准 : 检测不合格
    校准后老化 --> 传感仓组装 : 严重不合格

    成品组装 --> 成品检测 : 完成组装
    成品检测 --> 校准 : 检测不合格
    成品检测 --> 传感仓组装 : 严重不合格
    成品检测 --> 成品老化 : 检测合格

    成品老化 --> 校准 : 检测不合格
    成品老化 --> 传感仓组装 : 严重不合格
    成品老化 --> 入库 : 老化完成
    入库 --> 待发货 : 入库确认
    待发货 --> 发货 : 打标完成
    发货 --> [*] : 发货完成

    note right of 传感仓老化 : 传感仓老化：\n- 温度范围：-10～90度\n- 时间要求：3天\n- 累计时间机制\n- 间断时间累加

    note right of 校准前老化 : 校准前老化：\n- 稳定温度：70度\n- 时间要求：3天\n- 累计时间机制\n- 分析老化曲线

    note right of 校准后老化 : 校准后老化：\n- 稳定温度：50度\n- 时间要求：1天\n- 累计时间机制\n- 分析老化曲线

    note right of 成品老化 : 成品老化：\n- 温度范围：-10～70度\n- 时间要求：3天\n- 累计时间机制\n- 分析老化曲线
    
    note left of 空壳组装打压打标 : 一桶20支，同时打压1天
```

### 3.4 详细工序说明

#### 3.4.1 质量检测参数标准

| 检测项目 | 参数范围 | 判断标准 | 处理方式 |
|---------|---------|---------|---------|
| 老化前检测 | 3.1～3.3 | 在规定范围内 | 合格继续/不合格返工 |
| 老化曲线 | 窗口内差值5 | 在规定范围内 | 合格继续/不合格返工 |
| 成品检测 | 电流在3990-4000ua，压力的误差为正负满量程的千分之一之内 | 在规定范围内 | 合格继续/不合格返工 |

#### 3.4.2 老化工序时间累计机制

**时间计算规则：**
- **目标时间**：48小时连续老化
- **累计方式**：实际运行时间累加
- **暂停处理**：设备故障或维护期间暂停计时
- **恢复机制**：故障排除后从暂停点继续累计
- **达标判断**：累计运行时间≥48小时即可进入下一工序

**状态转换条件：**
```
老化开始 → 老化进行中：启动老化程序
老化进行中 → 老化暂停：设备异常/计划维护
老化暂停 → 老化进行中：故障排除/维护完成
老化进行中 → 老化结束：累计时间≥48小时
```

#### 3.4.3 异常处理路径

**检测不合格处理流程：**
1. **轻微缺陷**：直接返工，回到对应工序重新处理
2. **严重缺陷**：进入不合格品处理流程
3. **物料问题**：更换物料，重新检测

**质量追溯机制：**
- 每个工序都记录详细的时间戳和操作员信息
- 不合格品记录缺陷类型、原因分析和处理方式
- 建立从原材料到成品的完整追溯链
- 定期分析质量数据，持续改进工艺流程






